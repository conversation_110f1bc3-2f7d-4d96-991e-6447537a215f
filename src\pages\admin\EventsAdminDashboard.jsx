import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiUsers,
  FiDollarSign,
  FiTrendingUp,
  FiEye,
  FiEdit,
  FiTrash2,
  FiPlus,
  FiDownload,
  FiFilter,
  FiRefreshCw
} from 'react-icons/fi';
import {
  fetchPublicEvents,
  deleteAdminEvent,
  selectPublicEvents,
  selectPublicEventsLoading,
  selectPublicEventsError,
  selectAdminDeleteLoading,
  selectAdminDeleteSuccess,
  selectAdminDeleteError
} from '../../store/slices/EventsSlice';
import { PayFastService } from '../../services/payfast';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const EventsAdminDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const events = useSelector(selectPublicEvents);
  const loading = useSelector(selectPublicEventsLoading);
  const error = useSelector(selectPublicEventsError);
  const deleteLoading = useSelector(selectAdminDeleteLoading);
  const deleteSuccess = useSelector(selectAdminDeleteSuccess);
  const deleteError = useSelector(selectAdminDeleteError);
  
  const [analytics, setAnalytics] = useState(null);
  const [paymentStats, setPaymentStats] = useState(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  useEffect(() => {
    loadDashboardData();
  }, [dispatch, selectedPeriod]);

  // Handle delete success/error
  useEffect(() => {
    if (deleteSuccess) {
      // Refresh events after successful delete
      dispatch(fetchPublicEvents());
    }
    if (deleteError) {
      console.error('Delete error:', deleteError);
    }
  }, [deleteSuccess, deleteError, dispatch]);

  const loadDashboardData = async () => {
    // Load all public events for admin view
    dispatch(fetchPublicEvents());

    // Load analytics and payment data
    await loadAnalytics();
    await loadPaymentStats();
  };

  const loadAnalytics = async () => {
    setAnalyticsLoading(true);
    try {
      // This would be replaced with actual analytics API call
      const mockAnalytics = {
        total_events: events?.length || 0,
        total_registrations: 245,
        total_revenue: 15750.00,
        avg_attendance: 78.5,
        popular_categories: [
          { name: 'Workshop', count: 12 },
          { name: 'Conference', count: 8 },
          { name: 'Competition', count: 5 }
        ],
        recent_activity: [
          { type: 'registration', event: 'React Workshop', user: 'John Doe', time: '2 hours ago' },
          { type: 'payment', event: 'AI Conference', amount: 'R500', time: '3 hours ago' },
          { type: 'event_created', event: 'Python Bootcamp', time: '1 day ago' }
        ]
      };
      setAnalytics(mockAnalytics);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const loadPaymentStats = async () => {
    try {
      const stats = await PayFastService.getPaymentAnalytics({
        period: selectedPeriod
      });
      setPaymentStats(stats);
    } catch (error) {
      console.error('Failed to load payment stats:', error);
      // Mock data for demo
      setPaymentStats({
        total_revenue: 15750.00,
        total_transactions: 89,
        successful_payments: 85,
        failed_payments: 4,
        refunds: 2,
        avg_transaction_value: 177.00
      });
    }
  };

  const handleCreateEvent = () => {
    // Admin can create events - redirect to admin create page
    navigate('/admin/events/create');
  };

  const handleViewEvent = (eventId) => {
    // View event details in admin view
    navigate(`/admin/events/${eventId}/view`);
  };

  const handleEditEvent = (eventId) => {
    // Admin can edit any event
    navigate(`/admin/events/${eventId}/edit`);
  };

  const handleDeleteEvent = async (eventId) => {
    const event = events.find(e => e.id === eventId);
    const eventTitle = event?.title || 'this event';

    if (window.confirm(`Are you sure you want to delete "${eventTitle}"? This action cannot be undone.`)) {
      try {
        // Use admin delete with force=true to handle events with registrations
        await dispatch(deleteAdminEvent({ eventId, force: true })).unwrap();
        alert('Event deleted successfully');
        // Refresh the events list
        dispatch(fetchPublicEvents());
      } catch (error) {
        alert(`Failed to delete event: ${error.message || error}`);
      }
    }
  };

  const exportData = () => {
    // Export events data as CSV
    const csvHeaders = 'ID,Title,Category,Status,Start Date,Location,Max Attendees,Registrations,Revenue\n';
    const csvContent = events.map(event =>
      `${event.id},"${event.title}","${event.category}","${event.status}","${new Date(event.start_datetime).toLocaleDateString()}","${event.location}",${event.max_attendees},${event.registrations_count || 0},${event.revenue || 0}`
    ).join('\n');

    const blob = new Blob([csvHeaders + csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `events-admin-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading || analyticsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Events Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage your events, registrations, and payments</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadDashboardData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <FiRefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button
            onClick={exportData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <FiDownload className="h-4 w-4 mr-2" />
            Export
          </button>
          <button
            onClick={handleCreateEvent}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Create Event
          </button>
        </div>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 rounded-lg p-3">
              <FiCalendar className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Events</p>
              <p className="text-2xl font-bold text-gray-900">{analytics?.total_events || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-green-100 rounded-lg p-3">
              <FiUsers className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Registrations</p>
              <p className="text-2xl font-bold text-gray-900">{analytics?.total_registrations || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 rounded-lg p-3">
              <FiDollarSign className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">R{paymentStats?.total_revenue?.toLocaleString() || '0'}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-orange-100 rounded-lg p-3">
              <FiTrendingUp className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Attendance</p>
              <p className="text-2xl font-bold text-gray-900">{analytics?.avg_attendance || 0}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Events Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Recent Events</h3>
            <div className="flex items-center space-x-2">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <FiFilter className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Event
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Registrations
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {events?.slice(0, 10).map((event) => (
                <tr key={event.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        {event.banner_image_url ? (
                          <img
                            className="h-10 w-10 rounded-lg object-cover"
                            src={event.banner_image_url}
                            alt={event.title}
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                            <FiCalendar className="h-5 w-5 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{event.title}</div>
                        <div className="text-sm text-gray-500">{event.category}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(event.start_datetime).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {event.registrations_count || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R{event.revenue || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      event.status === 'PUBLISHED' 
                        ? 'bg-green-100 text-green-800'
                        : event.status === 'DRAFT'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {event.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => handleViewEvent(event.id)}
                        className="p-2 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                        title="View Event Details"
                      >
                        <FiEye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleEditEvent(event.id)}
                        className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-lg transition-colors duration-200"
                        title="Edit Event"
                      >
                        <FiEdit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteEvent(event.id)}
                        disabled={deleteLoading}
                        className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Delete Event"
                      >
                        {deleteLoading ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                        ) : (
                          <FiTrash2 className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default EventsAdminDashboard;
