import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiClock,
  FiStar,
  FiAward,
  FiArrowLeft,
  FiShare2,
  FiHeart,
  FiDollarSign,
  FiInfo,
  FiUser,
  FiCheck
} from 'react-icons/fi';
import { format } from 'date-fns';
import {
  fetchEventDetails,
  registerForEvent,
  cancelRegistration,
  selectCurrentEvent,
  selectEventDetailsLoading,
  selectEventDetailsError,
  selectRegistrationSuccess,
  selectIsRegisteredForEvent
} from '../../store/slices/EventsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import EventRegistrationModal from '../../components/events/EventRegistrationModal';

const EventDetailsPage = () => {
  const { eventId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);

  // Redux state
  const event = useSelector(selectCurrentEvent);
  const loading = useSelector(selectEventDetailsLoading);
  const error = useSelector(selectEventDetailsError);

  const registrationSuccess = useSelector(selectRegistrationSuccess);
  const isRegistered = useSelector(selectIsRegisteredForEvent(eventId));

  // Load event details on mount
  useEffect(() => {
    if (eventId) {
      dispatch(fetchEventDetails(eventId));
    }
  }, [dispatch, eventId]);

  // Handle registration success
  useEffect(() => {
    if (registrationSuccess) {
      setShowRegistrationModal(false);
      setRegistrationData({
        ticket_id: '',
        additional_info: {
          dietary_requirements: '',
          emergency_contact: ''
        }
      });
    }
  }, [registrationSuccess]);

  const handleRegister = () => {
    setShowRegistrationModal(true);
  };

  const handleCancelRegistration = async () => {
    try {
      await dispatch(cancelRegistration(eventId)).unwrap();
    } catch (error) {
      console.error('Cancellation failed:', error);
    }
  };

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'EEEE, MMMM dd, yyyy');
  };

  const formatTime = (dateString) => {
    return format(new Date(dateString), 'h:mm a');
  };

  const isRegistrationOpen = event && new Date() < new Date(event.registration_deadline);
  const isFull = event && event.current_participants >= event.max_participants;
  const attendancePercentage = event ? (event.current_participants / event.max_participants) * 100 : 0;

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  if (!event) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Event not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The event you're looking for doesn't exist or has been removed.
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Back Button */}
      <button
        onClick={() => navigate(-1)}
        className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-6"
      >
        <FiArrowLeft className="h-4 w-4 mr-2" />
        Back to Events
      </button>

      {/* Event Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
        {/* Banner Image */}
        {event.image_url && (
          <div className="relative h-64 bg-gray-200">
            <img
              src={event.image_url}
              alt={event.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 left-4 flex space-x-2">
              {event.is_featured && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                  <FiStar className="h-4 w-4 mr-1 fill-current" />
                  Featured
                </span>
              )}
              {event.is_competition && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                  <FiAward className="h-4 w-4 mr-1" />
                  Competition
                </span>
              )}
            </div>
            <div className="absolute top-4 right-4 flex space-x-2">
              <button className="p-2 bg-white rounded-full shadow-sm hover:bg-gray-50">
                <FiShare2 className="h-5 w-5 text-gray-600" />
              </button>
              <button className="p-2 bg-white rounded-full shadow-sm hover:bg-gray-50">
                <FiHeart className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>
        )}

        <div className="p-6">
          {/* Category */}
          {event.category && (
            <div className="mb-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                {event.category.name}
              </span>
            </div>
          )}

          {/* Title and Description */}
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{event.title}</h1>
          <p className="text-gray-600 text-lg mb-6">{event.description}</p>

          {/* Event Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <div className="flex items-center text-gray-600">
                <FiCalendar className="h-5 w-5 mr-3" />
                <div>
                  <p className="font-medium">{formatDate(event.start_date)}</p>
                  <p className="text-sm">{formatTime(event.start_date)} - {formatTime(event.end_date)}</p>
                </div>
              </div>
              
              {event.location && (
                <div className="flex items-center text-gray-600">
                  <FiMapPin className="h-5 w-5 mr-3" />
                  <div>
                    <p className="font-medium">{event.location}</p>
                    <p className="text-sm">{event.venue_address}</p>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <div className="flex items-center text-gray-600">
                <FiUsers className="h-5 w-5 mr-3" />
                <div>
                  <p className="font-medium">{event.current_participants} / {event.max_participants} attendees</p>
                  <div className="w-32 bg-gray-200 rounded-full h-2 mt-1">
                    <div 
                      className={`h-2 rounded-full ${
                        attendancePercentage >= 90 ? 'bg-red-500' : 
                        attendancePercentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(attendancePercentage, 100)}%` }}
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex items-center text-gray-600">
                <FiClock className="h-5 w-5 mr-3" />
                <div>
                  <p className="font-medium">Registration</p>
                  <p className="text-sm">
                    Ends {formatDate(event.registration_deadline)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Registration Button */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              {event.tickets && event.tickets.length > 0 && (
                <div className="flex items-center">
                  <FiDollarSign className="h-4 w-4 mr-1" />
                  {event.tickets[0].price === 0 ? 'Free' : `$${event.tickets[0].price}`}
                </div>
              )}
            </div>
            
            <div>
              {isRegistered ? (
                <div className="flex items-center space-x-3">
                  <span className="inline-flex items-center px-4 py-2 text-sm font-medium text-green-800 bg-green-100 rounded-md">
                    <FiCheck className="h-4 w-4 mr-2" />
                    Registered
                  </span>
                  <button
                    onClick={handleCancelRegistration}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Cancel Registration
                  </button>
                </div>
              ) : (
                <>
                  {isRegistrationOpen && !isFull ? (
                    <button
                      onClick={handleRegister}
                      className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Register Now
                    </button>
                  ) : (
                    <span className="inline-flex items-center px-6 py-3 text-base font-medium text-gray-500 bg-gray-100 rounded-md">
                      {!isRegistrationOpen ? 'Registration Closed' : 'Event Full'}
                    </span>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Speakers */}
          {event.speakers && event.speakers.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Speakers</h2>
              <div className="space-y-4">
                {event.speakers.map((speaker) => (
                  <div key={speaker.id} className="flex items-start space-x-4">
                    <img
                      src={speaker.profile_image_url || '/default-avatar.png'}
                      alt={speaker.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h3 className="font-medium text-gray-900">{speaker.name}</h3>
                      <p className="text-sm text-gray-600">{speaker.title}</p>
                      <p className="text-sm text-gray-500 mt-1">{speaker.bio}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Competition Details */}
          {event.is_competition && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Competition Details</h2>
              {event.competition_rules && (
                <div className="mb-4">
                  <h3 className="font-medium text-gray-900 mb-2">Rules</h3>
                  <p className="text-gray-600">{event.competition_rules}</p>
                </div>
              )}
              {event.prize_details && (
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Prizes</h3>
                  <div className="space-y-1">
                    {Object.entries(event.prize_details).map(([place, prize]) => (
                      <div key={place} className="flex justify-between">
                        <span className="capitalize text-gray-600">{place.replace('_', ' ')}:</span>
                        <span className="font-medium text-gray-900">{prize}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Organizer */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Organizer</h3>
            <div className="flex items-center space-x-3">
              <FiUser className="h-8 w-8 text-gray-400" />
              <div>
                <p className="font-medium text-gray-900">Event Organizer</p>
                <p className="text-sm text-gray-500">Verified organizer</p>
              </div>
            </div>
          </div>

          {/* Location Details */}
          {event.location && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Location</h3>
              <div className="space-y-2">
                <p className="font-medium text-gray-900">{event.location}</p>
                <p className="text-sm text-gray-600">{event.venue_address}</p>
                {event.tags && event.tags.length > 0 && (
                  <div className="mt-3">
                    <p className="text-sm font-medium text-gray-700 mb-1">Tags:</p>
                    <div className="flex flex-wrap gap-1">
                      {event.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Registration Modal */}
      <EventRegistrationModal
        isOpen={showRegistrationModal}
        onClose={() => setShowRegistrationModal(false)}
        event={event}
      />

    </div>
  );
};

export default EventDetailsPage;
