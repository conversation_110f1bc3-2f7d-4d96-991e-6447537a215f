import React from 'react';
import {
  FiCalendar,
  FiDollarSign,
  FiClock,
  FiCheck,
  FiX,
  FiMail,
  FiUser,
  FiHome,
  FiMessageSquare
} from 'react-icons/fi';

const InvitationCard = ({ 
  invitation, 
  onAccept, 
  onReject, 
  type = 'received', // 'sent' or 'received'
  userRole = 'mentor' // 'mentor' or 'institute'
}) => {
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'accepted':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'expired':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isExpired = invitation.expires_at && new Date(invitation.expires_at) < new Date();
  const isPending = invitation.status?.toLowerCase() === 'pending' && !isExpired;
  const canRespond = type === 'received' && isPending;

  const sender = invitation.sender;
  const displayName = userRole === 'mentor' 
    ? sender?.institute_name 
    : sender?.username || sender?.full_name;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            {type === 'sent' ? (
              <FiMail className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            ) : userRole === 'mentor' ? (
              <FiHome className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            ) : (
              <FiUser className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            )}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {type === 'sent' ? `Invitation to ${displayName}` : `Invitation from ${displayName}`}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {sender?.email}
            </p>
          </div>
        </div>
        
        <div className="flex flex-col items-end space-y-2">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(invitation.status)}`}>
            {isExpired ? 'Expired' : invitation.status || 'Pending'}
          </span>
          {invitation.invited_at && (
            <span className="text-xs text-gray-400">
              {formatDate(invitation.invited_at)}
            </span>
          )}
        </div>
      </div>

      {/* Invitation Details */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="flex items-center space-x-2">
          <FiDollarSign className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-600 dark:text-gray-300">
            ${invitation.proposed_hourly_rate || invitation.hourly_rate || 0}/hour
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <FiClock className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-600 dark:text-gray-300">
            {invitation.proposed_hours_per_week || invitation.hours_per_week || 0} hrs/week
          </span>
        </div>
        
        {invitation.expires_at && (
          <div className="flex items-center space-x-2 col-span-2">
            <FiCalendar className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              Expires: {formatDate(invitation.expires_at)}
            </span>
          </div>
        )}
      </div>

      {/* Expertise Areas */}
      {invitation.expertise_areas_needed && invitation.expertise_areas_needed.length > 0 && (
        <div className="mb-4">
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Expertise Areas Needed:
          </p>
          <div className="flex flex-wrap gap-2">
            {invitation.expertise_areas_needed.map((area, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md text-xs"
              >
                {area}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Invitation Message */}
      {invitation.invitation_message && (
        <div className="mb-4">
          <div className="flex items-start space-x-2">
            <FiMessageSquare className="w-4 h-4 text-gray-400 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Message:
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {invitation.invitation_message}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Contract Terms */}
      {invitation.contract_terms && (
        <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            <span className="font-medium">Terms:</span> {invitation.contract_terms}
          </p>
        </div>
      )}

      {/* Response Message (if responded) */}
      {invitation.response_message && invitation.responded_at && (
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Response ({formatDate(invitation.responded_at)}):
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {invitation.response_message}
          </p>
        </div>
      )}

      {/* Actions */}
      {canRespond && (
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={() => onReject?.(invitation)}
            className="flex items-center space-x-2 px-4 py-2 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors"
          >
            <FiX className="w-4 h-4" />
            <span className="text-sm font-medium">Reject</span>
          </button>
          
          <button
            onClick={() => onAccept?.(invitation)}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            <FiCheck className="w-4 h-4" />
            <span className="text-sm font-medium">Accept</span>
          </button>
        </div>
      )}

      {/* Status indicator for non-actionable invitations */}
      {!canRespond && type === 'received' && (
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
            {isExpired ? 'This invitation has expired' : 
             invitation.status === 'accepted' ? 'You have accepted this invitation' :
             invitation.status === 'rejected' ? 'You have rejected this invitation' :
             'No action required'}
          </p>
        </div>
      )}
    </div>
  );
};

export default InvitationCard;
