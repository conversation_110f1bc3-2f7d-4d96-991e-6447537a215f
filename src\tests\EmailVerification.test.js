/**
 * Email Verification Integration Test
 * 
 * This test verifies the email verification flow implementation
 * including Redux state management, API integration, and UI components.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { configureStore } from '@reduxjs/toolkit';
import emailVerificationReducer, {
  sendVerificationEmail,
  verifyEmailCode,
  resendVerificationEmail,
  checkVerificationStatus,
  clearErrors,
  setVerificationStatus,
} from '../store/slices/EmailVerificationSlice';

// Mock axios
vi.mock('axios');

// Mock API_BASE_URL
vi.mock('../utils/api/API_URL', () => ({
  default: 'http://localhost:8000'
}));

describe('Email Verification System', () => {
  let store;

  beforeEach(() => {
    // Create a fresh store for each test
    store = configureStore({
      reducer: {
        emailVerification: emailVerificationReducer,
      },
    });
  });

  describe('Redux Store', () => {
    it('should have correct initial state', () => {
      const state = store.getState().emailVerification;
      
      expect(state.isEmailVerified).toBe(false);
      expect(state.email).toBe(null);
      expect(state.sendingEmail).toBe(false);
      expect(state.verifyingCode).toBe(false);
      expect(state.resendingEmail).toBe(false);
      expect(state.checkingStatus).toBe(false);
      expect(state.canResend).toBe(true);
      expect(state.resendCooldown).toBe(0);
    });

    it('should handle setVerificationStatus action', () => {
      const verificationData = {
        isEmailVerified: true,
        email: '<EMAIL>',
        verifiedAt: '2023-01-01T00:00:00Z',
      };

      store.dispatch(setVerificationStatus(verificationData));
      const state = store.getState().emailVerification;

      expect(state.isEmailVerified).toBe(true);
      expect(state.email).toBe('<EMAIL>');
      expect(state.verifiedAt).toBe('2023-01-01T00:00:00Z');
    });

    it('should handle clearErrors action', () => {
      // First set some errors
      const initialState = {
        ...store.getState().emailVerification,
        sendError: 'Send error',
        verifyError: 'Verify error',
        resendError: 'Resend error',
        statusError: 'Status error',
      };

      // Manually set state with errors (in real app this would come from failed actions)
      store.dispatch({ type: 'emailVerification/test', payload: initialState });
      
      // Clear errors
      store.dispatch(clearErrors());
      const state = store.getState().emailVerification;

      expect(state.sendError).toBe(null);
      expect(state.verifyError).toBe(null);
      expect(state.resendError).toBe(null);
      expect(state.statusError).toBe(null);
    });
  });

  describe('Async Actions', () => {
    it('should handle sendVerificationEmail pending state', () => {
      store.dispatch(sendVerificationEmail.pending());
      const state = store.getState().emailVerification;

      expect(state.sendingEmail).toBe(true);
      expect(state.sendError).toBe(null);
      expect(state.emailSent).toBe(false);
    });

    it('should handle sendVerificationEmail fulfilled state', () => {
      const mockResponse = {
        message: 'Verification email sent successfully',
        email: '<EMAIL>',
      };

      store.dispatch(sendVerificationEmail.fulfilled(mockResponse));
      const state = store.getState().emailVerification;

      expect(state.sendingEmail).toBe(false);
      expect(state.emailSent).toBe(true);
      expect(state.email).toBe('<EMAIL>');
      expect(state.showSuccessMessage).toBe(true);
      expect(state.successMessage).toBe('Verification email sent successfully');
      expect(state.canResend).toBe(false);
      expect(state.resendCooldown).toBe(60);
    });

    it('should handle verifyEmailCode fulfilled state', () => {
      const mockResponse = {
        message: 'Email verified successfully',
        user_id: 'user-123',
        email: '<EMAIL>',
        verified_at: '2023-01-01T00:00:00Z',
      };

      store.dispatch(verifyEmailCode.fulfilled(mockResponse));
      const state = store.getState().emailVerification;

      expect(state.verifyingCode).toBe(false);
      expect(state.emailVerified).toBe(true);
      expect(state.isEmailVerified).toBe(true);
      expect(state.email).toBe('<EMAIL>');
      expect(state.verifiedAt).toBe('2023-01-01T00:00:00Z');
      expect(state.showSuccessMessage).toBe(true);
    });

    it('should handle checkVerificationStatus fulfilled state', () => {
      const mockResponse = {
        is_email_verified: true,
        email: '<EMAIL>',
        verification_sent_at: '2023-01-01T00:00:00Z',
        verified_at: '2023-01-01T00:05:00Z',
      };

      store.dispatch(checkVerificationStatus.fulfilled(mockResponse));
      const state = store.getState().emailVerification;

      expect(state.checkingStatus).toBe(false);
      expect(state.isEmailVerified).toBe(true);
      expect(state.email).toBe('<EMAIL>');
      expect(state.verificationSentAt).toBe('2023-01-01T00:00:00Z');
      expect(state.verifiedAt).toBe('2023-01-01T00:05:00Z');
    });
  });

  describe('Error Handling', () => {
    it('should handle sendVerificationEmail rejected state', () => {
      const errorMessage = 'Failed to send verification email';

      store.dispatch(sendVerificationEmail.rejected(null, '', null, errorMessage));
      const state = store.getState().emailVerification;

      expect(state.sendingEmail).toBe(false);
      expect(state.sendError).toBe(errorMessage);
      expect(state.emailSent).toBe(false);
    });

    it('should handle verifyEmailCode rejected state', () => {
      const errorMessage = 'Invalid verification code';

      store.dispatch(verifyEmailCode.rejected(null, '', null, errorMessage));
      const state = store.getState().emailVerification;

      expect(state.verifyingCode).toBe(false);
      expect(state.verifyError).toBe(errorMessage);
      expect(state.emailVerified).toBe(false);
    });
  });

  describe('UI State Management', () => {
    it('should manage resend cooldown correctly', () => {
      // Simulate successful email send (sets cooldown)
      const mockResponse = {
        message: 'Verification email sent successfully',
        email: '<EMAIL>',
      };

      store.dispatch(sendVerificationEmail.fulfilled(mockResponse));
      let state = store.getState().emailVerification;

      expect(state.canResend).toBe(false);
      expect(state.resendCooldown).toBe(60);

      // Simulate cooldown update
      store.dispatch({ type: 'emailVerification/updateResendCooldown', payload: 30 });
      state = store.getState().emailVerification;

      expect(state.resendCooldown).toBe(30);
      expect(state.canResend).toBe(false);

      // Simulate cooldown completion
      store.dispatch({ type: 'emailVerification/updateResendCooldown', payload: 0 });
      state = store.getState().emailVerification;

      expect(state.resendCooldown).toBe(0);
      expect(state.canResend).toBe(true);
    });

    it('should handle success message display', () => {
      const mockResponse = {
        message: 'Custom success message',
        email: '<EMAIL>',
      };

      store.dispatch(sendVerificationEmail.fulfilled(mockResponse));
      const state = store.getState().emailVerification;

      expect(state.showSuccessMessage).toBe(true);
      expect(state.successMessage).toBe('Custom success message');
    });
  });
});

// Test the service functions
describe('Email Verification Service', () => {
  describe('Validation Functions', () => {
    it('should validate verification codes correctly', async () => {
      const { validateVerificationCode } = await import('../services/emailVerificationService');

      expect(validateVerificationCode('123456')).toBe(true);
      expect(validateVerificationCode('12345')).toBe(false);
      expect(validateVerificationCode('1234567')).toBe(false);
      expect(validateVerificationCode('12345a')).toBe(false);
      expect(validateVerificationCode('')).toBe(false);
      expect(validateVerificationCode(null)).toBe(false);
    });

    it('should format verification codes correctly', async () => {
      const { formatVerificationCode } = await import('../services/emailVerificationService');

      expect(formatVerificationCode('123456')).toBe('123 456');
      expect(formatVerificationCode('123')).toBe('123');
      expect(formatVerificationCode('12')).toBe('12');
      expect(formatVerificationCode('')).toBe('');
    });

    it('should clean verification codes correctly', async () => {
      const { cleanVerificationCode } = await import('../services/emailVerificationService');

      expect(cleanVerificationCode('123 456')).toBe('123456');
      expect(cleanVerificationCode('1a2b3c')).toBe('123');
      expect(cleanVerificationCode('!@#123$%^')).toBe('123');
      expect(cleanVerificationCode('')).toBe('');
    });
  });

  describe('Rate Limiting', () => {
    it('should check resend availability correctly', async () => {
      const { canResendEmail } = await import('../services/emailVerificationService');

      // No previous send
      expect(canResendEmail(null).canResend).toBe(true);
      expect(canResendEmail(null).remainingTime).toBe(0);

      // Recent send (within cooldown)
      const recentTime = new Date(Date.now() - 30000); // 30 seconds ago
      const result = canResendEmail(recentTime, 1); // 1 minute cooldown
      expect(result.canResend).toBe(false);
      expect(result.remainingTime).toBeGreaterThan(0);

      // Old send (outside cooldown)
      const oldTime = new Date(Date.now() - 120000); // 2 minutes ago
      const oldResult = canResendEmail(oldTime, 1); // 1 minute cooldown
      expect(oldResult.canResend).toBe(true);
      expect(oldResult.remainingTime).toBe(0);
    });
  });
});

// Test admin exemption functionality
describe('Admin Exemption', () => {
  describe('EmailVerificationGuard with Admin Users', () => {
    it('should exempt admin users from email verification', async () => {
      // Mock a user with admin role but unverified email
      const mockAdminUser = {
        id: 'admin-123',
        email: '<EMAIL>',
        user_type: 'admin',
        is_email_verified: false, // Admin is not verified
      };

      // The guard should still allow access because admin is exempt
      const exemptRoles = ['admin'];
      const isUserExempt = (user, exemptRoles) => {
        if (!user) return false;
        const userRole = user.user_type || user.role;
        if (!userRole) return false;
        return exemptRoles.some(role =>
          role.toLowerCase() === userRole.toLowerCase()
        );
      };

      expect(isUserExempt(mockAdminUser, exemptRoles)).toBe(true);
    });

    it('should not exempt non-admin users from email verification', async () => {
      const mockStudentUser = {
        id: 'student-123',
        email: '<EMAIL>',
        user_type: 'student',
        is_email_verified: false,
      };

      const exemptRoles = ['admin'];
      const isUserExempt = (user, exemptRoles) => {
        if (!user) return false;
        const userRole = user.user_type || user.role;
        if (!userRole) return false;
        return exemptRoles.some(role =>
          role.toLowerCase() === userRole.toLowerCase()
        );
      };

      expect(isUserExempt(mockStudentUser, exemptRoles)).toBe(false);
    });

    it('should handle case-insensitive role matching', async () => {
      const mockAdminUser = {
        id: 'admin-123',
        email: '<EMAIL>',
        user_type: 'ADMIN', // Uppercase
        is_email_verified: false,
      };

      const exemptRoles = ['admin']; // Lowercase
      const isUserExempt = (user, exemptRoles) => {
        if (!user) return false;
        const userRole = user.user_type || user.role;
        if (!userRole) return false;
        return exemptRoles.some(role =>
          role.toLowerCase() === userRole.toLowerCase()
        );
      };

      expect(isUserExempt(mockAdminUser, exemptRoles)).toBe(true);
    });

    it('should support multiple exempt roles', async () => {
      const mockSuperAdminUser = {
        id: 'superadmin-123',
        email: '<EMAIL>',
        user_type: 'superadmin',
        is_email_verified: false,
      };

      const exemptRoles = ['admin', 'superadmin'];
      const isUserExempt = (user, exemptRoles) => {
        if (!user) return false;
        const userRole = user.user_type || user.role;
        if (!userRole) return false;
        return exemptRoles.some(role =>
          role.toLowerCase() === userRole.toLowerCase()
        );
      };

      expect(isUserExempt(mockSuperAdminUser, exemptRoles)).toBe(true);
    });
  });

  describe('useEmailVerification Hook with Admin Exemption', () => {
    it('should treat exempt users as verified', async () => {
      // This would be tested in a React testing environment
      // For now, we test the logic directly

      const mockAdminUser = {
        user_type: 'admin',
        is_email_verified: false,
      };

      const isUserExempt = () => {
        const userRole = mockAdminUser.user_type || mockAdminUser.role;
        if (!userRole) return false;
        return ['admin'].some(role =>
          role.toLowerCase() === userRole.toLowerCase()
        );
      };

      const effectiveVerificationStatus = isUserExempt() ? true : mockAdminUser.is_email_verified;

      expect(effectiveVerificationStatus).toBe(true); // Should be true because admin is exempt
    });
  });
});

console.log('✅ Email Verification System Tests Completed (including Admin Exemption)');
