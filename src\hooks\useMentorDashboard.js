import { useSelector, useDispatch } from 'react-redux';
import { useEffect, useCallback, useMemo } from 'react';
import {
  fetchMentorAssignments,
  fetchAnswersToCheck,
  fetchMentorStatistics,
  fetchMentorDashboard,
  selectAssignments,
  selectAnswersToCheck,
  selectStatistics,
  selectDashboardData,
  selectAssignmentsLoading,
  selectAnswersLoading,
  selectStatisticsLoading,
  selectDashboardLoading,
  selectAssignmentsError,
  selectAnswersError,
  selectStatisticsError,
  selectDashboardError,
  selectMyProfile
} from '../store/slices/MentorsSlice';

/**
 * Custom hook for mentor dashboard data management
 * Provides comprehensive dashboard data and actions for mentors
 */
export const useMentorDashboard = (options = {}) => {
  const {
    autoFetch = true,
    timeRange = 'week',
    refreshInterval = null
  } = options;

  const dispatch = useDispatch();

  // Redux state selectors
  const assignments = useSelector(selectAssignments);
  const answersToCheck = useSelector(selectAnswersToCheck);
  const statistics = useSelector(selectStatistics);
  const dashboardData = useSelector(selectDashboardData);
  const myProfile = useSelector(selectMyProfile);

  // Loading states
  const assignmentsLoading = useSelector(selectAssignmentsLoading);
  const answersLoading = useSelector(selectAnswersLoading);
  const statisticsLoading = useSelector(selectStatisticsLoading);
  const dashboardLoading = useSelector(selectDashboardLoading);

  // Error states
  const assignmentsError = useSelector(selectAssignmentsError);
  const answersError = useSelector(selectAnswersError);
  const statisticsError = useSelector(selectStatisticsError);
  const dashboardError = useSelector(selectDashboardError);

  // Auto-fetch data on mount
  useEffect(() => {
    if (autoFetch) {
      dispatch(fetchMentorAssignments());
      dispatch(fetchAnswersToCheck());
      dispatch(fetchMentorStatistics({ time_range: timeRange }));
    }
  }, [dispatch, autoFetch, timeRange]);

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      const interval = setInterval(() => {
        dispatch(fetchMentorAssignments());
        dispatch(fetchAnswersToCheck());
        dispatch(fetchMentorStatistics({ time_range: timeRange }));
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [dispatch, refreshInterval, timeRange]);

  // Action creators
  const refreshDashboard = useCallback(() => {
    dispatch(fetchMentorAssignments());
    dispatch(fetchAnswersToCheck());
    dispatch(fetchMentorStatistics({ time_range: timeRange }));
    dispatch(fetchMentorDashboard({ time_range: timeRange }));
  }, [dispatch, timeRange]);

  const refreshAssignments = useCallback(() => {
    dispatch(fetchMentorAssignments());
  }, [dispatch]);

  const refreshAnswers = useCallback(() => {
    dispatch(fetchAnswersToCheck());
  }, [dispatch]);

  const refreshStatistics = useCallback((newTimeRange = timeRange) => {
    dispatch(fetchMentorStatistics({ time_range: newTimeRange }));
  }, [dispatch, timeRange]);

  // Computed dashboard metrics
  const dashboardMetrics = useMemo(() => {
    const pendingAssignments = assignments.filter(a => a.status === 'pending').length;
    const activeAssignments = assignments.filter(a => a.status === 'approved' || a.status === 'accepted' || a.status === 'active').length;
    const completedAssignments = assignments.filter(a => a.status === 'completed').length;
    const rejectedAssignments = assignments.filter(a => a.status === 'rejected').length;

    const pendingAnswers = answersToCheck.filter(a => a.mentor_score === null).length;
    const checkedAnswers = answersToCheck.filter(a => a.mentor_score !== null).length;

    const totalEarnings = assignments
      .filter(a => a.status === 'completed')
      .reduce((sum, a) => sum + (a.payment_amount || 0), 0);

    const scoredAnswers = answersToCheck.filter(a => a.mentor_score !== null);
    const averageScore = scoredAnswers.length > 0 
      ? scoredAnswers.reduce((sum, a) => sum + a.mentor_score, 0) / scoredAnswers.length
      : 0;

    const responseTime = assignments
      .filter(a => a.responded_at && a.created_at)
      .reduce((sum, a) => {
        const responseTime = new Date(a.responded_at) - new Date(a.created_at);
        return sum + (responseTime / (1000 * 60 * 60)); // Convert to hours
      }, 0) / assignments.filter(a => a.responded_at && a.created_at).length || 0;

    return {
      assignments: {
        total: assignments.length,
        pending: pendingAssignments,
        active: activeAssignments,
        completed: completedAssignments,
        rejected: rejectedAssignments
      },
      answers: {
        total: answersToCheck.length,
        pending: pendingAnswers,
        checked: checkedAnswers
      },
      earnings: {
        total: totalEarnings,
        thisMonth: totalEarnings, // TODO: Calculate monthly earnings
        average: completedAssignments > 0 ? totalEarnings / completedAssignments : 0
      },
      performance: {
        averageScore: Math.round(averageScore * 100) / 100,
        responseTimeHours: Math.round(responseTime * 100) / 100,
        completionRate: assignments.length > 0 ? (completedAssignments / assignments.length) * 100 : 0
      }
    };
  }, [assignments, answersToCheck]);

  // Recent activity computation
  const recentActivity = useMemo(() => {
    const activities = [];

    // Add recent assignments
    assignments
      .filter(a => a.updated_at)
      .sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at))
      .slice(0, 3)
      .forEach(assignment => {
        activities.push({
          id: `assignment-${assignment.id}`,
          type: 'assignment',
          title: assignment.competition?.title || 'Assignment',
          description: `Status: ${assignment.status}`,
          timestamp: assignment.updated_at,
          data: assignment
        });
      });

    // Add recent answer checks
    answersToCheck
      .filter(a => a.updated_at && a.mentor_score !== null)
      .sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at))
      .slice(0, 2)
      .forEach(answer => {
        activities.push({
          id: `answer-${answer.id}`,
          type: 'answer_check',
          title: `Question ${answer.question_number}`,
          description: `Scored: ${answer.mentor_score}%`,
          timestamp: answer.updated_at,
          data: answer
        });
      });

    return activities
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, 5);
  }, [assignments, answersToCheck]);

  // Loading and error states
  const isLoading = assignmentsLoading || answersLoading || statisticsLoading || dashboardLoading;
  const hasErrors = assignmentsError || answersError || statisticsError || dashboardError;
  const errors = {
    assignments: assignmentsError,
    answers: answersError,
    statistics: statisticsError,
    dashboard: dashboardError
  };

  // Data availability checks
  const hasData = assignments.length > 0 || answersToCheck.length > 0 || statistics;
  const hasAssignments = assignments.length > 0;
  const hasAnswersToCheck = answersToCheck.length > 0;
  const hasStatistics = statistics && Object.keys(statistics).length > 0;

  return {
    // Core data
    assignments,
    answersToCheck,
    statistics,
    dashboardData,
    myProfile,

    // Computed data
    dashboardMetrics,
    recentActivity,

    // Loading states
    isLoading,
    assignmentsLoading,
    answersLoading,
    statisticsLoading,
    dashboardLoading,

    // Error states
    hasErrors,
    errors,
    assignmentsError,
    answersError,
    statisticsError,
    dashboardError,

    // Data availability
    hasData,
    hasAssignments,
    hasAnswersToCheck,
    hasStatistics,

    // Actions
    refreshDashboard,
    refreshAssignments,
    refreshAnswers,
    refreshStatistics,

    // Individual fetch functions for manual control
    fetchAssignments: () => dispatch(fetchMentorAssignments()),
    fetchAnswers: () => dispatch(fetchAnswersToCheck()),
    fetchStats: (params = {}) => dispatch(fetchMentorStatistics(params)),
    fetchDashboard: (params = {}) => dispatch(fetchMentorDashboard(params))
  };
};

/**
 * Hook for getting just the dashboard summary metrics
 */
export const useMentorSummary = (timeRange = 'week') => {
  const { dashboardMetrics, isLoading, hasErrors, refreshStatistics } = useMentorDashboard({
    autoFetch: true,
    timeRange
  });

  return {
    metrics: dashboardMetrics,
    isLoading,
    hasErrors,
    refresh: refreshStatistics
  };
};

/**
 * Hook for getting mentor performance data
 */
export const useMentorPerformance = (timeRange = 'week') => {
  const { dashboardMetrics, statistics, statisticsLoading, statisticsError, refreshStatistics } = useMentorDashboard({
    autoFetch: true,
    timeRange
  });

  return {
    performance: dashboardMetrics.performance,
    statistics,
    isLoading: statisticsLoading,
    error: statisticsError,
    refresh: refreshStatistics
  };
};

export default useMentorDashboard;
