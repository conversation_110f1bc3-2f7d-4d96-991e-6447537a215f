import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import { 
  FiX, 
  FiArrowLeft, 
  FiRefreshCw, 
  FiHome,
  FiCreditCard,
  FiAlertCircle
} from 'react-icons/fi';
import { getCurrentUser } from '../../utils/helpers/authHelpers';

const PaymentCancel = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // Local state
  const [currentUser, setCurrentUser] = useState(null);
  const [eventData, setEventData] = useState(null);

  // Get parameters from URL
  const paymentId = searchParams.get('payment_id');
  const eventId = searchParams.get('event_id');
  const reason = searchParams.get('reason') || 'cancelled';

  useEffect(() => {
    const user = getCurrentUser();
    setCurrentUser(user);

    // If we have event ID, we could fetch event details
    // For now, we'll just show a generic cancellation message
  }, []);

  const handleRetryPayment = () => {
    if (eventId) {
      navigate(`/events/${eventId}`);
    } else {
      navigate('/events');
    }
  };

  const handleGoToDashboard = () => {
    const userRole = currentUser?.user_type || currentUser?.role;
    if (userRole) {
      navigate(`/${userRole.toLowerCase()}/dashboard`);
    } else {
      navigate('/');
    }
  };

  const handleGoToEvents = () => {
    navigate('/events');
  };

  const getCancellationMessage = () => {
    switch (reason.toLowerCase()) {
      case 'timeout':
        return {
          title: 'Payment Timed Out',
          message: 'Your payment session expired. Please try again to complete your ticket purchase.',
          icon: FiAlertCircle,
          color: 'yellow'
        };
      case 'failed':
        return {
          title: 'Payment Failed',
          message: 'There was an issue processing your payment. Please check your payment details and try again.',
          icon: FiX,
          color: 'red'
        };
      case 'cancelled':
      default:
        return {
          title: 'Payment Cancelled',
          message: 'You cancelled the payment process. Your ticket has not been purchased.',
          icon: FiX,
          color: 'gray'
        };
    }
  };

  const cancellationInfo = getCancellationMessage();
  const IconComponent = cancellationInfo.icon;

  const getColorClasses = (color) => {
    switch (color) {
      case 'red':
        return {
          bg: 'from-red-500 to-red-600',
          iconBg: 'bg-red-100',
          iconColor: 'text-red-600',
          textColor: 'text-red-100'
        };
      case 'yellow':
        return {
          bg: 'from-yellow-500 to-orange-600',
          iconBg: 'bg-yellow-100',
          iconColor: 'text-yellow-600',
          textColor: 'text-yellow-100'
        };
      default:
        return {
          bg: 'from-gray-500 to-gray-600',
          iconBg: 'bg-gray-100',
          iconColor: 'text-gray-600',
          textColor: 'text-gray-100'
        };
    }
  };

  const colorClasses = getColorClasses(cancellationInfo.color);

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4">
        {/* Cancellation Header */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden mb-6">
          <div className={`bg-gradient-to-r ${colorClasses.bg} p-8 text-white text-center`}>
            <div className={`w-20 h-20 ${colorClasses.iconBg} rounded-full flex items-center justify-center mx-auto mb-4`}>
              <IconComponent className={`h-10 w-10 ${colorClasses.iconColor}`} />
            </div>
            <h1 className="text-2xl font-bold mb-2">{cancellationInfo.title}</h1>
            <p className={colorClasses.textColor}>
              {cancellationInfo.message}
            </p>
          </div>
        </div>

        {/* Payment Details */}
        {paymentId && (
          <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Payment ID</label>
                <p className="text-gray-900 font-mono text-sm">{paymentId}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Status</label>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  cancellationInfo.color === 'red' ? 'bg-red-100 text-red-800' :
                  cancellationInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  <IconComponent className="h-4 w-4 mr-1" />
                  {reason.charAt(0).toUpperCase() + reason.slice(1)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* What Happened */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">What Happened?</h2>
          
          <div className="space-y-3 text-sm text-gray-600">
            {reason.toLowerCase() === 'timeout' && (
              <>
                <div className="flex items-start">
                  <FiAlertCircle className="h-4 w-4 mr-3 mt-0.5 text-yellow-500" />
                  <p>Your payment session expired due to inactivity. This is a security measure to protect your payment information.</p>
                </div>
                <div className="flex items-start">
                  <FiRefreshCw className="h-4 w-4 mr-3 mt-0.5 text-blue-500" />
                  <p>You can start a new payment session by clicking "Try Again" below.</p>
                </div>
              </>
            )}
            
            {reason.toLowerCase() === 'failed' && (
              <>
                <div className="flex items-start">
                  <FiX className="h-4 w-4 mr-3 mt-0.5 text-red-500" />
                  <p>The payment could not be processed. This might be due to insufficient funds, incorrect card details, or a temporary issue with your bank.</p>
                </div>
                <div className="flex items-start">
                  <FiCreditCard className="h-4 w-4 mr-3 mt-0.5 text-blue-500" />
                  <p>Please check your payment details and try again. If the problem persists, contact your bank or try a different payment method.</p>
                </div>
              </>
            )}
            
            {reason.toLowerCase() === 'cancelled' && (
              <>
                <div className="flex items-start">
                  <FiX className="h-4 w-4 mr-3 mt-0.5 text-gray-500" />
                  <p>You chose to cancel the payment process. No charges have been made to your account.</p>
                </div>
                <div className="flex items-start">
                  <FiRefreshCw className="h-4 w-4 mr-3 mt-0.5 text-blue-500" />
                  <p>You can return to the event page to try purchasing tickets again.</p>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">What Can You Do?</h2>
          
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start">
              <FiRefreshCw className="h-4 w-4 mr-3 mt-0.5 text-blue-500" />
              <p><strong>Try Again:</strong> Return to the event page and attempt to purchase tickets again.</p>
            </div>
            
            <div className="flex items-start">
              <FiCreditCard className="h-4 w-4 mr-3 mt-0.5 text-blue-500" />
              <p><strong>Check Payment Method:</strong> Ensure your payment details are correct and your account has sufficient funds.</p>
            </div>
            
            <div className="flex items-start">
              <FiHome className="h-4 w-4 mr-3 mt-0.5 text-blue-500" />
              <p><strong>Browse Other Events:</strong> Explore other available events that might interest you.</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleRetryPayment}
            className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors"
          >
            <FiRefreshCw className="h-5 w-5 mr-2" />
            Try Again
          </button>
          
          <button
            onClick={handleGoToEvents}
            className="w-full flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-semibold hover:bg-gray-200 transition-colors"
          >
            <FiArrowLeft className="h-5 w-5 mr-2" />
            Browse Events
          </button>
          
          <button
            onClick={handleGoToDashboard}
            className="w-full flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 transition-colors"
          >
            <FiHome className="h-5 w-5 mr-2" />
            Go to Dashboard
          </button>
        </div>

        {/* Support Information */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            Need help? Contact our support team at{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentCancel;
