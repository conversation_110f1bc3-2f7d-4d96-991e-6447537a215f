/**
 * PayFast Payment Integration Tests
 * 
 * Comprehensive tests for PayFast payment integration,
 * including component tests, service tests, and flow tests.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import components and services to test
import { PaymentForm, PaymentStatus, PaymentConfirmation } from '../../components/payment';
import paymentService from '../../services/paymentService';
import eventService from '../../services/eventService';
import PaymentSlice from '../../store/slices/PaymentSlice';
import EventsSlice from '../../store/slices/EventsSlice';

// Mock external dependencies
jest.mock('../../services/paymentService');
jest.mock('../../services/eventService');
jest.mock('../../utils/helpers/logger');

// Test store setup
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      payment: PaymentSlice,
      events: EventsSlice
    },
    preloadedState: initialState
  });
};

// Test wrapper component
const TestWrapper = ({ children, store }) => (
  <Provider store={store}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
);

describe('PayFast Payment Integration', () => {
  
  describe('Payment Service', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('should create event payment successfully', async () => {
      const mockPaymentData = {
        registration_id: 'reg-123',
        amount: 299.99,
        currency: 'ZAR',
        user_email: '<EMAIL>',
        user_name: 'Test User',
        return_url: 'http://localhost:3000/payment/success',
        cancel_url: 'http://localhost:3000/payment/cancel'
      };

      const mockResponse = {
        payment_id: 'pay-123',
        payment_data: {
          merchant_id: '10000100',
          merchant_key: 'test-key',
          amount: '299.99',
          item_name: 'Event Registration'
        },
        payment_url: 'https://sandbox.payfast.co.za/eng/process'
      };

      paymentService.createEventPayment.mockResolvedValue(mockResponse);

      const result = await paymentService.createEventPayment(mockPaymentData);

      expect(paymentService.createEventPayment).toHaveBeenCalledWith(mockPaymentData);
      expect(result).toEqual(mockResponse);
      expect(result.payment_id).toBe('pay-123');
    });

    test('should handle payment creation failure', async () => {
      const mockPaymentData = {
        registration_id: 'reg-123',
        amount: 299.99,
        currency: 'ZAR',
        user_email: '<EMAIL>',
        user_name: 'Test User'
      };

      const mockError = new Error('Payment creation failed');
      paymentService.createEventPayment.mockRejectedValue(mockError);

      await expect(paymentService.createEventPayment(mockPaymentData))
        .rejects.toThrow('Payment creation failed');
    });

    test('should get payment status successfully', async () => {
      const paymentId = 'pay-123';
      const mockStatus = {
        payment_id: paymentId,
        status: 'completed',
        amount: 299.99,
        currency: 'ZAR',
        processed_at: '2024-01-15T10:30:00Z'
      };

      paymentService.getPaymentStatus.mockResolvedValue(mockStatus);

      const result = await paymentService.getPaymentStatus(paymentId);

      expect(paymentService.getPaymentStatus).toHaveBeenCalledWith(paymentId);
      expect(result).toEqual(mockStatus);
      expect(result.status).toBe('completed');
    });

    test('should poll payment status until completion', async () => {
      const paymentId = 'pay-123';
      const mockStatuses = [
        { payment_id: paymentId, status: 'pending' },
        { payment_id: paymentId, status: 'pending' },
        { payment_id: paymentId, status: 'completed' }
      ];

      let callCount = 0;
      paymentService.getPaymentStatus.mockImplementation(() => {
        return Promise.resolve(mockStatuses[callCount++]);
      });

      paymentService.pollPaymentStatus.mockImplementation(async (paymentId, maxAttempts, interval) => {
        for (let i = 0; i < maxAttempts; i++) {
          const status = await paymentService.getPaymentStatus(paymentId);
          if (['completed', 'failed', 'refunded'].includes(status.status)) {
            return status;
          }
          await new Promise(resolve => setTimeout(resolve, interval));
        }
        throw new Error('Payment status polling timeout');
      });

      const result = await paymentService.pollPaymentStatus(paymentId, 5, 100);

      expect(result.status).toBe('completed');
      expect(paymentService.getPaymentStatus).toHaveBeenCalledTimes(3);
    });
  });

  describe('PaymentForm Component', () => {
    test('should render payment form with event data', () => {
      const mockPaymentData = {
        amount: 299.99,
        currency: 'ZAR',
        user_email: '<EMAIL>',
        user_name: 'Test User'
      };

      const store = createTestStore();

      render(
        <TestWrapper store={store}>
          <PaymentForm
            paymentType="event"
            paymentData={mockPaymentData}
            onSuccess={jest.fn()}
            onCancel={jest.fn()}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Event Payment')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test User')).toBeInTheDocument();
      expect(screen.getByText('ZAR 299.99')).toBeInTheDocument();
    });

    test('should validate form data before submission', async () => {
      const mockPaymentData = {
        amount: 299.99,
        currency: 'ZAR',
        user_email: '',
        user_name: ''
      };

      const store = createTestStore();

      render(
        <TestWrapper store={store}>
          <PaymentForm
            paymentType="event"
            paymentData={mockPaymentData}
            onSuccess={jest.fn()}
            onCancel={jest.fn()}
          />
        </TestWrapper>
      );

      const submitButton = screen.getByRole('button', { name: /pay now/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Valid email is required')).toBeInTheDocument();
        expect(screen.getByText('Full name is required')).toBeInTheDocument();
      });
    });

    test('should submit payment form with valid data', async () => {
      const mockPaymentData = {
        amount: 299.99,
        currency: 'ZAR',
        user_email: '<EMAIL>',
        user_name: 'Test User'
      };

      const mockOnSuccess = jest.fn();
      const store = createTestStore();

      render(
        <TestWrapper store={store}>
          <PaymentForm
            paymentType="event"
            paymentData={mockPaymentData}
            onSuccess={mockOnSuccess}
            onCancel={jest.fn()}
          />
        </TestWrapper>
      );

      const submitButton = screen.getByRole('button', { name: /pay now/i });
      fireEvent.click(submitButton);

      // Mock successful payment creation
      await waitFor(() => {
        expect(submitButton).toBeDisabled();
      });
    });
  });

  describe('PaymentStatus Component', () => {
    test('should display payment status correctly', () => {
      const mockPaymentStatus = {
        payment_id: 'pay-123',
        status: 'completed',
        amount: 299.99,
        currency: 'ZAR',
        created_at: '2024-01-15T10:00:00Z',
        processed_at: '2024-01-15T10:30:00Z'
      };

      const store = createTestStore({
        payment: {
          paymentStatus: mockPaymentStatus,
          statusLoading: false,
          statusError: null
        }
      });

      render(
        <TestWrapper store={store}>
          <PaymentStatus paymentId="pay-123" />
        </TestWrapper>
      );

      expect(screen.getByText('Payment Successful')).toBeInTheDocument();
      expect(screen.getByText('ZAR 299.99')).toBeInTheDocument();
      expect(screen.getByText('Completed')).toBeInTheDocument();
    });

    test('should show loading state', () => {
      const store = createTestStore({
        payment: {
          paymentStatus: null,
          statusLoading: true,
          statusError: null
        }
      });

      render(
        <TestWrapper store={store}>
          <PaymentStatus paymentId="pay-123" />
        </TestWrapper>
      );

      expect(screen.getByText('Loading payment status...')).toBeInTheDocument();
    });

    test('should handle error state', () => {
      const store = createTestStore({
        payment: {
          paymentStatus: null,
          statusLoading: false,
          statusError: 'Payment not found'
        }
      });

      render(
        <TestWrapper store={store}>
          <PaymentStatus paymentId="pay-123" />
        </TestWrapper>
      );

      expect(screen.getByText('Error Loading Payment Status')).toBeInTheDocument();
      expect(screen.getByText('Payment not found')).toBeInTheDocument();
    });
  });

  describe('Event Registration with Payment Flow', () => {
    test('should handle free event registration', async () => {
      const mockEvent = {
        id: 'event-123',
        title: 'Free Workshop',
        registration_fee: 0,
        currency: 'ZAR'
      };

      const mockRegistrationData = {
        additional_info: 'Test registration'
      };

      eventService.registerForEvent = jest.fn().mockResolvedValue({
        registration_id: 'reg-123',
        status: 'confirmed'
      });

      // Test the registration flow
      const result = await eventService.registerForEvent(mockEvent.id, mockRegistrationData);

      expect(eventService.registerForEvent).toHaveBeenCalledWith(mockEvent.id, mockRegistrationData);
      expect(result.status).toBe('confirmed');
    });

    test('should handle paid event registration', async () => {
      const mockEvent = {
        id: 'event-123',
        title: 'Paid Workshop',
        registration_fee: 299.99,
        currency: 'ZAR'
      };

      const mockRegistrationData = {
        additional_info: 'Test registration'
      };

      const mockPaymentData = {
        user_email: '<EMAIL>',
        user_name: 'Test User'
      };

      // Mock the registration with payment flow
      eventService.registerForEventWithPayment = jest.fn().mockResolvedValue({
        registration: {
          registration_id: 'reg-123',
          payment_amount: 299.99,
          status: 'pending_payment'
        },
        payment: {
          payment_id: 'pay-123',
          payment_url: 'https://sandbox.payfast.co.za/eng/process'
        },
        requiresPayment: true
      });

      const result = await eventService.registerForEventWithPayment({
        eventId: mockEvent.id,
        registrationData: mockRegistrationData,
        paymentData: mockPaymentData
      });

      expect(result.requiresPayment).toBe(true);
      expect(result.payment.payment_id).toBe('pay-123');
      expect(result.registration.status).toBe('pending_payment');
    });
  });

  describe('Payment Utilities', () => {
    test('should format currency correctly', () => {
      const { formatCurrency } = require('../../utils/payment/paymentHelpers');
      
      expect(formatCurrency(299.99, 'ZAR')).toBe('R 299.99');
      expect(formatCurrency(1000, 'USD')).toBe('$1,000.00');
      expect(formatCurrency(0, 'ZAR')).toBe('R 0.00');
    });

    test('should validate payment amounts', () => {
      const { validatePaymentAmount } = require('../../utils/payment/paymentHelpers');
      
      const validAmount = validatePaymentAmount(299.99);
      expect(validAmount.isValid).toBe(true);
      expect(validAmount.roundedAmount).toBe(299.99);

      const invalidAmount = validatePaymentAmount(-10);
      expect(invalidAmount.isValid).toBe(false);
      expect(invalidAmount.errors).toContain('Amount must be greater than zero');

      const tooSmallAmount = validatePaymentAmount(2);
      expect(tooSmallAmount.isValid).toBe(false);
      expect(tooSmallAmount.errors).toContain('Minimum amount is R 5.00');
    });

    test('should generate payment references', () => {
      const { generatePaymentReference } = require('../../utils/payment/paymentHelpers');
      
      const reference = generatePaymentReference('PAY');
      expect(reference).toMatch(/^PAY-[A-Z0-9]+-[A-Z0-9]+$/);
      
      const customReference = generatePaymentReference('EVENT', { includeRandom: false });
      expect(customReference).toMatch(/^EVENT-[A-Z0-9]+$/);
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      const networkError = new Error('Network Error');
      networkError.code = 'NETWORK_ERROR';
      
      paymentService.createEventPayment.mockRejectedValue(networkError);

      await expect(paymentService.createEventPayment({}))
        .rejects.toThrow('Network Error');
    });

    test('should handle API validation errors', async () => {
      const validationError = {
        response: {
          status: 400,
          data: {
            error: 'Validation failed',
            details: {
              amount: 'Amount is required',
              email: 'Invalid email format'
            }
          }
        }
      };

      paymentService.createEventPayment.mockRejectedValue(validationError);

      await expect(paymentService.createEventPayment({}))
        .rejects.toMatchObject(validationError);
    });
  });

  describe('Integration Scenarios', () => {
    test('should complete full payment flow', async () => {
      // 1. Create event registration
      const registrationResult = {
        registration_id: 'reg-123',
        payment_amount: 299.99,
        status: 'pending_payment'
      };

      // 2. Create payment
      const paymentResult = {
        payment_id: 'pay-123',
        payment_data: { /* PayFast form data */ },
        payment_url: 'https://sandbox.payfast.co.za/eng/process'
      };

      // 3. Payment completion
      const completedPayment = {
        payment_id: 'pay-123',
        status: 'completed',
        amount: 299.99,
        processed_at: '2024-01-15T10:30:00Z'
      };

      // 4. Updated registration
      const confirmedRegistration = {
        registration_id: 'reg-123',
        status: 'confirmed',
        payment_status: 'completed'
      };

      // Mock the flow
      eventService.registerForEventWithPayment.mockResolvedValue({
        registration: registrationResult,
        payment: paymentResult,
        requiresPayment: true
      });

      paymentService.getPaymentStatus.mockResolvedValue(completedPayment);
      eventService.getRegistrationDetails.mockResolvedValue(confirmedRegistration);

      // Execute the flow
      const registrationResponse = await eventService.registerForEventWithPayment({
        eventId: 'event-123',
        registrationData: {},
        paymentData: {}
      });

      expect(registrationResponse.requiresPayment).toBe(true);

      const paymentStatus = await paymentService.getPaymentStatus('pay-123');
      expect(paymentStatus.status).toBe('completed');

      const finalRegistration = await eventService.getRegistrationDetails('reg-123');
      expect(finalRegistration.status).toBe('confirmed');
    });
  });
});

// Test utilities
export const createMockEvent = (overrides = {}) => ({
  id: 'event-123',
  title: 'Test Event',
  description: 'Test event description',
  start_date: '2024-02-01T10:00:00Z',
  end_date: '2024-02-01T18:00:00Z',
  location: 'Test Venue',
  max_participants: 100,
  current_participants: 25,
  registration_fee: 299.99,
  currency: 'ZAR',
  status: 'upcoming',
  ...overrides
});

export const createMockPayment = (overrides = {}) => ({
  payment_id: 'pay-123',
  status: 'pending',
  amount: 299.99,
  currency: 'ZAR',
  created_at: '2024-01-15T10:00:00Z',
  ...overrides
});

export const createMockRegistration = (overrides = {}) => ({
  registration_id: 'reg-123',
  event_id: 'event-123',
  user_id: 'user-123',
  status: 'pending',
  payment_status: 'pending',
  created_at: '2024-01-15T10:00:00Z',
  ...overrides
});
