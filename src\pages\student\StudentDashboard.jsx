import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getStudentUpcomingExams } from '../../store/slices/ExamSlice';
import { fetchMyClassrooms } from '../../store/slices/ClassroomSlice';
import { fetchCurrentUser } from '../../store/slices/userSlice';
import { useStudentDashboard } from '../../hooks/useStudentDashboard';
import { FluidPageContainer, Stack } from '../../components/ui/layout';
import { DashboardGrid, QuickActions } from '../../components/dashboard';
import {
  FiCalendar,
  FiBookOpen,
  FiTrendingUp,
  FiUsers,
  FiPlay,
  FiEye
} from 'react-icons/fi';

function StudentDashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { exams, loading: examsLoading } = useSelector((state) => state.exams);
  const { myClassrooms, loading: classroomsLoading } = useSelector((state) => state.classroom);
  const { currentUser } = useSelector((state) => state.users);

  const { summary, refreshData } = useStudentDashboard({
    autoFetch: false,
    fetchAll: true
  });

  useEffect(() => {
    if (!currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    if (currentUser?.id) {
      dispatch(getStudentUpcomingExams());
      dispatch(fetchMyClassrooms());
      refreshData();
    }
  }, [dispatch, currentUser]); // Removed refreshData from dependencies

  const stats = useMemo(() => {
    const upcomingExams = Array.isArray(exams) ? exams.filter(exam => {
      const now = new Date();
      const startTime = new Date(exam.start_time);
      return startTime > now;
    }).length : 0;

    const totalClasses = Array.isArray(myClassrooms) ? myClassrooms.length : 
                        myClassrooms?.results?.length || myClassrooms?.data?.length || 0;

    return [
      {
        key: 'classes',
        title: 'Total Classes',
        value: totalClasses,
        icon: FiUsers,
        color: 'blue',
        onClick: () => navigate('/student/classrooms')
      },
      {
        key: 'exams',
        title: 'Upcoming Exams',
        value: upcomingExams,
        icon: FiCalendar,
        color: 'green',
        onClick: () => navigate('/student/exams')
      },
      {
        key: 'performance',
        title: 'Average Score',
        value: summary?.overall_grade || '0%',
        icon: FiTrendingUp,
        color: 'purple'
      },
      {
        key: 'assignments',
        title: 'Assignments',
        value: summary?.total_assignments || 0,
        icon: FiBookOpen,
        color: 'indigo',
        onClick: () => navigate('/student/assignments')
      }
    ];
  }, [exams, myClassrooms, summary, navigate]);

  const quickActions = useMemo(() => [
    {
      key: 'join-exam',
      title: 'Join Exam',
      description: 'Start or continue an exam',
      icon: FiPlay,
      color: 'green',
      onClick: () => navigate('/student/exams')
    },
    {
      key: 'view-classes',
      title: 'View Classes',
      description: 'Browse your enrolled classes',
      icon: FiEye,
      color: 'blue',
      onClick: () => navigate('/student/classrooms')
    },
    {
      key: 'check-schedule',
      title: 'Check Schedule',
      description: 'View upcoming events',
      icon: FiCalendar,
      color: 'purple',
      onClick: () => navigate('/student/schedule')
    }
  ], [navigate]);

  const loading = examsLoading || classroomsLoading;

  return (
    <FluidPageContainer>
      <div className="mb-8">
        <div className="text-center md:text-left">
          <h1 className="text-3xl md:text-4xl text-gray-800 dark:text-gray-100 font-bold">
            Student Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-3 text-lg">
            Welcome back, {currentUser?.username || 'Student'}! Here's your learning overview.
          </p>
        </div>
      </div>

      <div className="space-y-8">
        <DashboardGrid stats={stats} loading={loading} />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-1">
            <QuickActions actions={quickActions} />
          </div>

          <div className="lg:col-span-2 space-y-6">
            {/* Recent Activity or Additional Content */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Recent Activity
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Completed Math Assignment
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">2 hours ago</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Joined Science Class
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">1 day ago</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Upcoming History Exam
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">Tomorrow</span>
                </div>
              </div>
            </div>

            {/* Progress Overview */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Learning Progress
              </h3>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-700 dark:text-gray-300">Mathematics</span>
                    <span className="text-gray-500">75%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{width: '75%'}}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-700 dark:text-gray-300">Science</span>
                    <span className="text-gray-500">60%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{width: '60%'}}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-700 dark:text-gray-300">History</span>
                    <span className="text-gray-500">85%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-purple-600 h-2 rounded-full" style={{width: '85%'}}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </FluidPageContainer>
  );
}

export default StudentDashboard;
