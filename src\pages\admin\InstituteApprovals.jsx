import React, { useState, useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FiRefreshCw,
  FiHome,
  FiEye,
  FiCheck,
  FiX,
  FiAlertTriangle,
} from "react-icons/fi";
import {
  fetchPendingVerificationInstitutes,
  approveInstituteAdmin,
  rejectInstituteAdmin,
  selectAdminApprovalLoading,
  selectAdminRejectionLoading,
} from "../../store/slices/InstituteProfileSlice";
import { LoadingSpinner, ActionModal } from "../../components/ui";
import Pagination from "../../components/ui/Pagination";
import InstituteDetailsView from "../../components/admin/InstituteDetailsView";

// 🔹 Small reusable card component
const InstituteCard = ({ data, onView, onApprove, onReject }) => (
  <article className="flex items-center justify-between p-6 bg-white border rounded-xl hover:shadow-lg transition-shadow">
    <div className="flex items-center space-x-4">
      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
        <FiHome className="h-6 w-6 text-blue-600" />
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-900">{data.name}</h3>
        <p className="text-sm text-gray-500">{data.type}</p>
        <p className="text-sm text-gray-600">
          📍 {data.city}, {data.state}
        </p>
      </div>
    </div>

    <div className="flex items-center space-x-3">
      <span
        className={`px-3 py-1 rounded-full text-sm font-medium ${
          data.status === "approved"
            ? "bg-green-100 text-green-800"
            : data.status === "rejected"
            ? "bg-red-100 text-red-800"
            : "bg-yellow-100 text-yellow-800"
        }`}
      >
        {data.status}
      </span>

      <button
        onClick={onView}
        className="p-2 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100"
        title="View Details"
      >
        <FiEye className="h-5 w-5" />
      </button>
      <button
        onClick={onApprove}
        className="p-2 bg-green-50 text-green-700 rounded-lg hover:bg-green-100"
        title="Approve"
      >
        <FiCheck className="h-5 w-5" />
      </button>
      <button
        onClick={onReject}
        className="p-2 bg-red-50 text-red-700 rounded-lg hover:bg-red-100"
        title="Reject"
      >
        <FiX className="h-5 w-5" />
      </button>
    </div>
  </article>
);

const InstituteApprovals = () => {
  const dispatch = useDispatch();
  const { pendingInstitutes, loading, error, pendingTotal } = useSelector(
    (state) => state.instituteProfile
  );
  const approvalLoading = useSelector(selectAdminApprovalLoading);
  const rejectionLoading = useSelector(selectAdminRejectionLoading);

  const [selectedInstitute, setSelectedInstitute] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);

  // 🔹 Unified modal state
  const [modal, setModal] = useState({
    open: false,
    type: null, // "approve" | "reject"
    notes: "",
    institute: null,
  });

  const itemsPerPage = 10;

  useEffect(() => {
    dispatch(
      fetchPendingVerificationInstitutes({ page: currentPage, limit: itemsPerPage })
    );
  }, [dispatch, currentPage]);

  const refresh = useCallback(() => {
    dispatch(
      fetchPendingVerificationInstitutes({ page: currentPage, limit: itemsPerPage })
    );
  }, [dispatch, currentPage]);

  const changePage = (page) => {
    setCurrentPage(page);
    setSelectedInstitute(null);
  };

  const getInstituteData = (institute) => ({
    id: institute.id,
    name:
      institute.user?.institute_profile?.institute_name ||
      institute.institute_name ||
      "Unnamed Institute",
    type:
      institute.user?.institute_profile?.institute_type ||
      institute.institute_type ||
      "Unknown Type",
    city:
      institute.user?.institute_profile?.city ||
      institute.city ||
      "Unknown City",
    state:
      institute.user?.institute_profile?.state ||
      institute.state ||
      "Unknown State",
    status:
      institute.user?.institute_profile?.verification_status ||
      institute.verification_status ||
      "pending",
  });

  const handleApprove = async () => {
    try {
      await dispatch(
        approveInstituteAdmin({
          institute_id: modal.institute.id,
          verification_notes: modal.notes.trim(),
        })
      ).unwrap();

      setModal({ open: false, type: null, notes: "", institute: null });
      refresh();
      alert("✅ Institute approved successfully!");
    } catch (error) {
      alert(`❌ Failed to approve: ${error.message || error}`);
    }
  };

  const handleReject = async () => {
    if (!modal.notes.trim()) {
      alert("Please provide a reason for rejection.");
      return;
    }
    try {
      await dispatch(
        rejectInstituteAdmin({
          institute_id: modal.institute.id,
          verification_notes: modal.notes.trim(),
        })
      ).unwrap();

      setModal({ open: false, type: null, notes: "", institute: null });
      refresh();
      alert("🚫 Institute rejected successfully!");
    } catch (error) {
      alert(`❌ Failed to reject: ${error.message || error}`);
    }
  };

  if (selectedInstitute) {
    return (
      <InstituteDetailsView
        institute={selectedInstitute.id || selectedInstitute}
        loading={loading}
        onBack={() => {
          setSelectedInstitute(null);
          refresh();
        }}
      />
    );
  }

  return (
    <main className="p-6 space-y-6 w-full">
      {/* Header */}
      <header className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Institute Approvals</h1>
          <p className="text-gray-600">
            Review and approve pending institute applications
          </p>
        </div>
        <button
          onClick={refresh}
          disabled={loading}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          <FiRefreshCw className={`mr-2 ${loading ? "animate-spin" : ""}`} />
          Refresh
        </button>
      </header>

      {/* Error */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      )}

      {/* Loading */}
      {loading && !pendingInstitutes?.length ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        <>
          {/* List */}
          <section className="space-y-4">
            {(pendingInstitutes || []).map((inst) => {
              const data = getInstituteData(inst);
              return (
                <InstituteCard
                  key={data.id}
                  data={data}
                  onView={() => setSelectedInstitute(inst)}
                  onApprove={() =>
                    setModal({ open: true, type: "approve", notes: "", institute: inst })
                  }
                  onReject={() =>
                    setModal({ open: true, type: "reject", notes: "", institute: inst })
                  }
                />
              );
            })}

            {!pendingInstitutes?.length && !loading && (
              <div className="text-center py-12 text-gray-500">
                No institutes found
              </div>
            )}
          </section>

          {/* Pagination */}
          {pendingTotal > itemsPerPage && (
            <Pagination
              currentPage={currentPage}
              totalItems={pendingTotal}
              itemsPerPage={itemsPerPage}
              onPageChange={changePage}
            />
          )}
        </>
      )}

      {/* 🔹 Single Modal that switches between Approve/Reject */}
      <ActionModal
        isOpen={modal.open}
        onClose={() => setModal({ open: false, type: null, notes: "", institute: null })}
        onConfirm={modal.type === "approve" ? handleApprove : handleReject}
        title={modal.type === "approve" ? "Approve Institute" : "Reject Institute"}
        message={
          modal.institute
            ? `Are you sure you want to ${
                modal.type === "approve" ? "approve" : "reject"
              } "${getInstituteData(modal.institute).name}"?`
            : ""
        }
        confirmText={modal.type === "approve" ? "Approve" : "Reject"}
        confirmColor={modal.type === "approve" ? "green" : "red"}
        icon={modal.type === "approve" ? FiCheck : FiAlertTriangle}
        loading={modal.type === "approve" ? approvalLoading : rejectionLoading}
      >
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {modal.type === "approve"
              ? "Approval Notes (Optional)"
              : "Reason for Rejection *"}
          </label>
          <textarea
            value={modal.notes}
            onChange={(e) => setModal({ ...modal, notes: e.target.value })}
            placeholder={
              modal.type === "approve"
                ? "Add any notes about the approval..."
                : "Please provide a reason for rejection..."
            }
            className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={3}
          />
          {modal.type === "reject" && !modal.notes.trim() && (
            <p className="text-red-500 text-sm mt-1">
              Rejection reason is required
            </p>
          )}
        </div>
      </ActionModal>
    </main>
  );
};

export default InstituteApprovals;
