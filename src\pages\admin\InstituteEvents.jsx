import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchInstituteEvents,
  deleteInstituteEvent,
  publishInstituteEvent,
  selectEvents,
  selectEventsLoading,
  selectEventsError,
  clearErrors
} from '../../store/slices/InstituteEventsSlice';
import { ErrorMessage } from '../../components/ui';
import SearchFilterCard from '../../components/ui/SearchFilterCard';
import EventActions from '../../components/events/EventActions';
import EventStats from '../../components/events/EventStats';
import EventList from '../../components/events/EventList';
import { getCategoryColor, getStatusBadgeColor, filterEvents, getFilterOptions } from '../../utils/eventUtils';

function InstituteEvents() {
  const dispatch = useDispatch();
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDate, setFilterDate] = useState('all');

  // Redux selectors
  const events = useSelector(selectEvents);
  const eventsLoading = useSelector(selectEventsLoading);
  const eventsError = useSelector(selectEventsError);

  // Filter events using utility function
  const filteredEvents = filterEvents(events.data || [], {
    searchTerm,
    category: filterCategory,
    status: filterStatus,
    date: filterDate
  });

  // Fetch events on component mount
  useEffect(() => {
    dispatch(fetchInstituteEvents({ skip: 0, limit: 20 }));
  }, [dispatch]);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await dispatch(fetchInstituteEvents({ skip: 0, limit: 20 }));
    } finally {
      setRefreshing(false);
    }
  };

  // Handle delete event
  const handleDeleteEvent = async (eventId) => {
    await dispatch(deleteInstituteEvent(eventId));
  };

  // Handle publish event
  const handlePublishEvent = async (eventId) => {
    await dispatch(publishInstituteEvent(eventId));
  };

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      dispatch(clearErrors());
    };
  }, [dispatch]);

  if (eventsError) {
    return <ErrorMessage message={eventsError} />;
  }

  return (
    <div className="space-y-6 max-w-full overflow-hidden">
      {/* Header Actions */}
      <EventActions 
        onRefresh={handleRefresh}
        refreshing={refreshing}
      />

      {/* Statistics */}
      <EventStats events={filteredEvents} />

      {/* Search and Filters */}
      <SearchFilterCard
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        searchPlaceholder="Search events by title, description, or location..."
        filters={[
          {
            label: 'Category',
            value: filterCategory,
            onChange: setFilterCategory,
            options: getFilterOptions().categories
          },
          {
            label: 'Status',
            value: filterStatus,
            onChange: setFilterStatus,
            options: getFilterOptions().statuses
          },
          {
            label: 'Date',
            value: filterDate,
            onChange: setFilterDate,
            options: getFilterOptions().dates
          }
        ]}
        resultsCount={filteredEvents.length}
        resultsType="events"
      />

      {/* Events List */}
      <EventList
        events={filteredEvents}
        loading={eventsLoading}
        onDeleteEvent={handleDeleteEvent}
        onPublishEvent={handlePublishEvent}
        getCategoryColor={getCategoryColor}
        getStatusBadgeColor={getStatusBadgeColor}
      />
    </div>
  );
}

export default InstituteEvents;
