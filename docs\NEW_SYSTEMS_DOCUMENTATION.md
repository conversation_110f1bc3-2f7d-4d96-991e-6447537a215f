curl -X 'POST' \
  'http://localhost:8000/api/events/' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "title": "string",
  "description": "string",
  "short_description": "string",
  "banner_image_url": "string",
  "gallery_images": [
    "string"
  ],
  "start_datetime": "2025-09-01T17:52:12.140Z",
  "end_datetime": "2025-09-01T17:52:12.140Z",
  "registration_start": "2025-09-01T17:52:12.140Z",
  "registration_end": "2025-09-01T17:52:12.140Z",
  "category": "workshop",
  "location_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "status": "draft",
  "is_featured": false,
  "is_public": true,
  "requires_approval": false,
  "max_attendees": 1,
  "min_attendees": 1,
  "agenda": [
    {
      "additionalProp1": {}
    }
  ],
  "requirements": "string",
  "tags": [
    "string"
  ],
  "external_links": {
    "additionalProp1": "string",
    "additionalProp2": "string",
    "additionalProp3": "string"
  },
  "is_competition": false,
  "competition_exam_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "competition_rules": "string",
  "prize_details": {
    "additionalProp1": {}
  },
  "organizer_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
}'

Example Value
Schema
{
  "title": "string",
  "description": "string",
  "short_description": "string",
  "banner_image_url": "string",
  "gallery_images": [
    "string"
  ],
  "start_datetime": "2025-09-01T17:52:14.437Z",
  "end_datetime": "2025-09-01T17:52:14.437Z",
  "registration_start": "2025-09-01T17:52:14.437Z",
  "registration_end": "2025-09-01T17:52:14.437Z",
  "category": "workshop",
  "location_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "status": "draft",
  "is_featured": false,
  "is_public": true,
  "requires_approval": false,
  "max_attendees": 1,
  "min_attendees": 1,
  "agenda": [
    {
      "additionalProp1": {}
    }
  ],
  "requirements": "string",
  "tags": [
    "string"
  ],
  "external_links": {
    "additionalProp1": "string",
    "additionalProp2": "string",
    "additionalProp3": "string"
  },
  "is_competition": false,
  "competition_exam_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "competition_rules": "string",
  "prize_details": {
    "additionalProp1": {}
  },
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "organizer_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "created_at": "2025-09-01T17:52:14.437Z",
  "updated_at": "2025-09-01T17:52:14.437Z"
}





GET
/api/events/my-events
Get My Events Endpoint


Get all events created by the current institute.

Only institutes can access this endpoint.

curl -X 'GET' \
  'http://localhost:8000/api/events/my-events?skip=0&limit=100' \
  -H 'accept: application/json'


Example Value
Schema
[
  {
    "title": "string",
    "description": "string",
    "short_description": "string",
    "banner_image_url": "string",
    "gallery_images": [
      "string"
    ],
    "start_datetime": "2025-09-01T17:53:12.722Z",
    "end_datetime": "2025-09-01T17:53:12.722Z",
    "registration_start": "2025-09-01T17:53:12.722Z",
    "registration_end": "2025-09-01T17:53:12.722Z",
    "category": "workshop",
    "location_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "status": "draft",
    "is_featured": false,
    "is_public": true,
    "requires_approval": false,
    "max_attendees": 1,
    "min_attendees": 1,
    "agenda": [
      {
        "additionalProp1": {}
      }
    ],
    "requirements": "string",
    "tags": [
      "string"
    ],
    "external_links": {
      "additionalProp1": "string",
      "additionalProp2": "string",
      "additionalProp3": "string"
    },
    "is_competition": false,
    "competition_exam_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "competition_rules": "string",
    "prize_details": {
      "additionalProp1": {}
    },
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "organizer_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "created_at": "2025-09-01T17:53:12.722Z",
    "updated_at": "2025-09-01T17:53:12.722Z"
  }
]


For ALL USERS
curl -X 'GET' \
  'http://localhost:8000/api/events/{event_id}' \
  -H 'accept: application/json'

{
  "title": "string",
  "description": "string",
  "short_description": "string",
  "banner_image_url": "string",
  "gallery_images": [
    "string"
  ],
  "start_datetime": "2025-09-01T17:53:51.397Z",
  "end_datetime": "2025-09-01T17:53:51.397Z",
  "registration_start": "2025-09-01T17:53:51.397Z",
  "registration_end": "2025-09-01T17:53:51.397Z",
  "category": "workshop",
  "location_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "status": "draft",
  "is_featured": false,
  "is_public": true,
  "requires_approval": false,
  "max_attendees": 1,
  "min_attendees": 1,
  "agenda": [
    {
      "additionalProp1": {}
    }
  ],
  "requirements": "string",
  "tags": [
    "string"
  ],
  "external_links": {
    "additionalProp1": "string",
    "additionalProp2": "string",
    "additionalProp3": "string"
  },
  "is_competition": false,
  "competition_exam_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "competition_rules": "string",
  "prize_details": {
    "additionalProp1": {}
  },
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "organizer_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "created_at": "2025-09-01T17:53:51.397Z",
  "updated_at": "2025-09-01T17:53:51.397Z",
  "location": {
    "name": "string",
    "address": "string",
    "city": "string",
    "state": "string",
    "country": "string",
    "postal_code": "string",
    "latitude": "string",
    "longitude": "string",
    "capacity": 0,
    "facilities": [
      "string"
    ],
    "contact_info": {
      "additionalProp1": {}
    },
    "is_virtual": false,
    "virtual_link": "string",
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "created_at": "2025-09-01T17:53:51.397Z",
    "updated_at": "2025-09-01T17:53:51.397Z"
  },
  "speakers": [],
  "tickets": [],
  "total_registrations": 0,
  "available_tickets": 0
}


PUT
/api/events/{event_id}
Update Event Endpoint


Update an existing event.

Only the event organizer (institute) can update the event.

curl -X 'PUT' \
  'http://localhost:8000/api/events/{event_id}' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "title": "string",
  "description": "string",
  "short_description": "string",
  "banner_image_url": "string",
  "gallery_images": [
    "string"
  ],
  "start_datetime": "2025-09-01T17:54:47.904Z",
  "end_datetime": "2025-09-01T17:54:47.904Z",
  "registration_start": "2025-09-01T17:54:47.904Z",
  "registration_end": "2025-09-01T17:54:47.904Z",
  "category_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "location_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "status": "draft",
  "is_featured": true,
  "is_public": true,
  "requires_approval": true,
  "max_attendees": 1,
  "min_attendees": 1,
  "agenda": [
    {
      "additionalProp1": {}
    }
  ],
  "requirements": "string",
  "tags": [
    "string"
  ],
  "external_links": {
    "additionalProp1": "string",
    "additionalProp2": "string",
    "additionalProp3": "string"
  },
  "is_competition": true,
  "competition_exam_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "competition_rules": "string",
  "prize_details": {
    "additionalProp1": {}
  }
}'


Delete an event.
Only the event organizer (institute) can delete the event. Cannot delete events with existing registrations.
curl -X 'DELETE' \
  'http://localhost:8000/api/events/{event_id}' \
  -H 'accept: */*'