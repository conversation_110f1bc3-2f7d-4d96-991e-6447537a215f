import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { PayFastService } from '../../services/payfast';
import { LoadingSpinner } from '../ui';
import {
  FiCreditCard,
  FiShield,
  FiCheckCircle,
  FiXCircle,
  FiArrowLeft,
  FiInfo
} from 'react-icons/fi';

const PayFastPayment = ({ 
  eventId, 
  ticketId, 
  ticketData, 
  onSuccess, 
  onCancel, 
  onError 
}) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState(null);
  const [error, setError] = useState(null);

  // Check for payment return parameters
  useEffect(() => {
    const paymentId = searchParams.get('payment_id');
    const status = searchParams.get('status');
    
    if (paymentId && status) {
      handlePaymentReturn(paymentId, status);
    }
  }, [searchParams]);

  const handlePaymentReturn = async (paymentId, status) => {
    setLoading(true);
    try {
      const verification = await PayFastService.verifyPayment(paymentId);
      
      if (verification.status === 'COMPLETE') {
        setPaymentStatus('success');
        if (onSuccess) onSuccess(verification);
      } else {
        setPaymentStatus('failed');
        if (onError) onError(verification);
      }
    } catch (err) {
      setError(err.message);
      setPaymentStatus('failed');
      if (onError) onError(err);
    } finally {
      setLoading(false);
    }
  };

  const initiatePayment = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const paymentData = {
        event_id: eventId,
        ticket_id: ticketId,
        quantity: ticketData.quantity || 1,
        amount: ticketData.total_amount,
        return_url: `${window.location.origin}/payment/return`,
        cancel_url: `${window.location.origin}/payment/cancel`,
        notify_url: `${window.location.origin}/api/payments/payfast/notify`
      };

      const response = await PayFastService.initializePayment(paymentData);
      
      // Redirect to PayFast
      PayFastService.redirectToPayment(response.payment_form_data);
      
    } catch (err) {
      setError(err.message);
      if (onError) onError(err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) onCancel();
    navigate(-1);
  };

  // Success state
  if (paymentStatus === 'success') {
    return (
      <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 p-8 text-center">
        <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
          <FiCheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Payment Successful!</h2>
        <p className="text-gray-600 mb-6">
          Your ticket has been purchased successfully. You will receive a confirmation email shortly.
        </p>
        <button
          onClick={() => navigate('/student/events')}
          className="w-full px-6 py-3 bg-green-600 text-white rounded-xl font-semibold hover:bg-green-700 transition-colors duration-200"
        >
          Back to Events
        </button>
      </div>
    );
  }

  // Failed state
  if (paymentStatus === 'failed') {
    return (
      <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 p-8 text-center">
        <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
          <FiXCircle className="h-8 w-8 text-red-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Payment Failed</h2>
        <p className="text-gray-600 mb-6">
          {error || 'Your payment could not be processed. Please try again.'}
        </p>
        <div className="space-y-3">
          <button
            onClick={initiatePayment}
            className="w-full px-6 py-3 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200"
          >
            Try Again
          </button>
          <button
            onClick={handleCancel}
            className="w-full px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 transition-colors duration-200"
          >
            Cancel
          </button>
        </div>
      </div>
    );
  }

  // Payment form
  return (
    <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Secure Payment</h2>
          <FiShield className="h-6 w-6" />
        </div>
        <p className="text-blue-100">
          Complete your ticket purchase securely with PayFast
        </p>
      </div>

      {/* Payment Details */}
      <div className="p-6">
        <div className="space-y-4 mb-6">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Ticket Type:</span>
            <span className="font-semibold">{ticketData.name}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Quantity:</span>
            <span className="font-semibold">{ticketData.quantity || 1}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Price per ticket:</span>
            <span className="font-semibold">R{ticketData.price}</span>
          </div>
          <hr className="border-gray-200" />
          <div className="flex justify-between items-center text-lg">
            <span className="font-semibold">Total Amount:</span>
            <span className="font-bold text-blue-600">R{ticketData.total_amount}</span>
          </div>
        </div>

        {/* Security Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
          <div className="flex items-start">
            <FiInfo className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
            <div className="text-sm text-blue-800">
              <p className="font-semibold mb-1">Secure Payment</p>
              <p>Your payment is processed securely by PayFast, South Africa's leading payment gateway.</p>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={initiatePayment}
            disabled={loading}
            className="w-full flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <LoadingSpinner size="sm" className="mr-2" />
            ) : (
              <FiCreditCard className="h-5 w-5 mr-2" />
            )}
            {loading ? 'Processing...' : 'Pay with PayFast'}
          </button>
          
          <button
            onClick={handleCancel}
            disabled={loading}
            className="w-full flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50"
          >
            <FiArrowLeft className="h-5 w-5 mr-2" />
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default PayFastPayment;
