/**
 * Document Helper Utilities
 * Simplified utilities for document handling
 */

/**
 * Create a new document object
 * @param {File} file - The file object
 * @param {string} type - Document type
 * @param {string} description - Document description
 * @returns {Object} Document object
 */
export const createDocumentObject = (file, type = 'other', description = '') => ({
  file,
  type,
  description,
  id: Date.now() + Math.random()
});

/**
 * Filter documents that have valid files for upload
 * @param {Array} documents - Array of document objects
 * @returns {Array} Filtered documents with valid files
 */
export const getValidDocumentsForUpload = (documents) => {
  return documents.filter(doc => doc.file && doc.file instanceof File);
};

/**
 * Check if documents array has changes compared to original
 * @param {Array} current - Current documents array
 * @param {Array} original - Original documents array
 * @returns {boolean} True if documents have changed
 */
export const hasDocumentChanges = (current, original) => {
  return JSON.stringify(current) !== JSON.stringify(original);
};

/**
 * Update document at specific index
 * @param {Array} documents - Documents array
 * @param {number} index - Index to update
 * @param {Object} updates - Updates to apply
 * @returns {Array} Updated documents array
 */
export const updateDocumentAtIndex = (documents, index, updates) => {
  const updatedDocuments = [...documents];
  updatedDocuments[index] = { ...updatedDocuments[index], ...updates };
  return updatedDocuments;
};

/**
 * Remove document at specific index
 * @param {Array} documents - Documents array
 * @param {number} index - Index to remove
 * @returns {Array} Updated documents array
 */
export const removeDocumentAtIndex = (documents, index) => {
  return documents.filter((_, i) => i !== index);
};

export default {
  createDocumentObject,
  getValidDocumentsForUpload,
  hasDocumentChanges,
  updateDocumentAtIndex,
  removeDocumentAtIndex
};
