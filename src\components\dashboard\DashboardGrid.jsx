import React from 'react';
import { GridWrapper } from '../ui/layout';
import StatsCard from './StatsCard';

const DashboardGrid = ({ stats = [], loading = false }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 w-full">
      {stats.map((stat, index) => (
        <StatsCard
          key={stat.key || index}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
          trend={stat.trend}
          trendValue={stat.trendValue}
          color={stat.color}
          onClick={stat.onClick}
          loading={loading}
        />
      ))}
    </div>
  );
};

export default DashboardGrid;
