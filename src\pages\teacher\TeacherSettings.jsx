import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchTeacherByUserId } from '../../store/slices/TeacherSlice';
import { fetchCurrentUser } from '../../store/slices/userSlice';
import { PageContainer, Stack } from '../../components/ui/layout';
import SettingsTabs from '../../components/settings/SettingsTabs';
import ProfileSettings from '../../components/settings/ProfileSettings';
import { LoadingSpinner } from '../../components/ui';

const TeacherSettings = () => {
  const dispatch = useDispatch();
  const { currentUser, loading: userLoading } = useSelector((state) => state.users);
  const { teacher, loading: teacherLoading } = useSelector((state) => state.teacher);
  
  const [activeTab, setActiveTab] = useState('profile');

  useEffect(() => {
    if (!currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    if (currentUser?.id && !teacher) {
      dispatch(fetchTeacherByUserId(currentUser.id));
    }
  }, [dispatch, currentUser, teacher]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <ProfileSettings 
            user={currentUser} 
            loading={userLoading} 
          />
        );
      case 'teacher':
        return (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Teacher Information</h3>
            <p className="text-gray-600 dark:text-gray-400">
              Teacher profile settings coming soon...
            </p>
          </div>
        );
      case 'notifications':
        return (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Notification Preferences</h3>
            <p className="text-gray-600 dark:text-gray-400">
              Notification settings coming soon...
            </p>
          </div>
        );
      case 'security':
        return (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Security Settings</h3>
            <p className="text-gray-600 dark:text-gray-400">
              Security settings coming soon...
            </p>
          </div>
        );
      default:
        return null;
    }
  };

  if (userLoading && !currentUser) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <PageContainer>
      <Stack gap="lg">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your account and preferences
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <SettingsTabs 
            activeTab={activeTab} 
            onTabChange={setActiveTab} 
          />
          
          <div className="p-6">
            {renderTabContent()}
          </div>
        </div>
      </Stack>
    </PageContainer>
  );
};

export default TeacherSettings;
