/**
 * DocumentCard Component
 * Simple, reusable document card with download functionality only
 */

import React from 'react';
import { FiDownload, FiFile, FiTrash2 } from 'react-icons/fi';
import { useThemeProvider } from '../../providers/ThemeContext';

const DocumentCard = ({ 
  document, 
  onDownload, 
  onRemove, 
  showRemove = false,
  disabled = false,
  className = '' 
}) => {
  const { currentTheme } = useThemeProvider();
  const isDark = currentTheme === 'dark';

  // Extract document info
  const name = document?.document_name || document?.name || 'Unknown Document';
  const size = document?.size_bytes || document?.size;
  const mimeType = document?.mime_type || document?.type || '';

  // Get file icon color based on type
  const getIconColor = () => {
    if (mimeType === 'application/pdf') return 'text-red-500';
    if (mimeType.startsWith('image/')) return 'text-blue-500';
    if (mimeType.includes('word')) return 'text-blue-600';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'text-green-600';
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'text-orange-600';
    if (mimeType.startsWith('text/') || mimeType === 'application/json') return 'text-yellow-600';
    return 'text-gray-500';
  };

  // Format file size
  const formatSize = (bytes) => {
    if (!bytes) return '';
    const kb = Math.round(bytes / 1024);
    return kb > 0 ? `${kb} KB` : '< 1 KB';
  };

  return (
    <div className={`
      relative p-4 border rounded-lg transition-all
      ${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}
      ${disabled ? 'opacity-60' : 'hover:shadow-md'}
      ${className}
    `}>
      {/* Remove button */}
      {showRemove && !disabled && (
        <button
          onClick={onRemove}
          className="absolute top-2 right-2 p-1 rounded-md bg-red-100 hover:bg-red-200 dark:bg-red-900/20 dark:hover:bg-red-900/40 text-red-600 transition-colors"
          title="Remove Document"
        >
          <FiTrash2 className="w-3 h-3" />
        </button>
      )}

      {/* Document info */}
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-1">
          <FiFile className={`w-5 h-5 ${getIconColor()}`} />
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className={`font-medium truncate ${isDark ? 'text-gray-100' : 'text-gray-900'}`}>
            {name}
          </h3>
          
          {size && (
            <p className="text-xs text-gray-500 mt-1">
              {formatSize(size)}
            </p>
          )}
          
          {mimeType && (
            <p className="text-xs text-gray-400 mt-1 truncate">
              {mimeType}
            </p>
          )}
        </div>
        
        {/* Download button */}
        <button
          onClick={onDownload}
          disabled={disabled}
          className={`
            flex-shrink-0 p-2 rounded-md transition-colors
            ${disabled 
              ? 'text-gray-400 cursor-not-allowed' 
              : 'text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/20'
            }
          `}
          title="Download Document"
        >
          <FiDownload className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default DocumentCard;
