import React from 'react';
import { FiMail, FiPhone, FiGlobe } from 'react-icons/fi';

const InstituteContactForm = ({
  formData,
  onChange,
  onFileChange,
  isEditing,
  fieldErrors,
  mandatoryFields,
  hasAttemptedSubmit
}) => {
  const getFieldError = (fieldName) => {
    return fieldErrors[fieldName];
  };

  const isFieldRequired = (fieldName) => {
    return mandatoryFields && mandatoryFields[fieldName];
  };

  const renderField = (fieldName, label, type = 'text', placeholder = '', icon = null) => {
    const error = getFieldError(fieldName);
    const required = isFieldRequired(fieldName);

    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <div className="relative">
          {icon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              {React.createElement(icon, { className: "h-4 w-4 text-gray-400" })}
            </div>
          )}
          <input
            type={type}
            name={fieldName}
            value={formData[fieldName] || ''}
            onChange={onChange}
            disabled={!isEditing}
            placeholder={placeholder}
            {...(type === 'url' && { pattern: 'https?://.*' })}
            className={`w-full ${icon ? 'pl-10' : 'pl-3'} pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              !isEditing ? 'bg-gray-50 text-gray-600' : 'bg-white'
            } ${error ? 'border-red-300' : 'border-gray-300'}`}
          />
        </div>
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  };

  const renderFileField = (fieldName, label, accept = 'image/*') => {
    const error = getFieldError(fieldName);
    const currentFile = formData[fieldName];

    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>

        {/* Current file preview */}
        {currentFile && (
          <div className="mb-2 p-3 bg-gray-50 rounded-md border">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {currentFile.filename || 'Uploaded image'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {currentFile.content_type}
                  </p>
                </div>
              </div>
              {isEditing && (
                <button
                  type="button"
                  onClick={() => onFileChange({ target: { files: [] } }, fieldName)}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Remove
                </button>
              )}
            </div>
          </div>
        )}

        {/* File input */}
        {isEditing && (
          <input
            type="file"
            accept={accept}
            onChange={(e) => onFileChange(e, fieldName)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              error ? 'border-red-300' : 'border-gray-300'
            }`}
          />
        )}

        {!isEditing && !currentFile && (
          <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-500 text-sm">
            No {label.toLowerCase()} uploaded
          </div>
        )}

        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}

        <p className="mt-1 text-xs text-gray-500">
          Supported formats: JPEG, PNG, GIF, WebP, SVG. Max size: 5MB.
        </p>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center">
          <FiMail className="h-5 w-5 text-green-600 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">Contact Information</h3>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Website */}
        {renderField(
          'website',
          'Website',
          'url',
          'https://www.example.edu',
          FiGlobe
        )}

        {/* Note: Phone and email are managed in user registration */}

        {/* Logo and Banner Images */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {renderFileField('logo', 'Institute Logo', 'image/*')}
          {renderFileField('banner', 'Banner Image', 'image/*')}
        </div>

        {/* File upload components already include previews */}
      </div>
    </div>
  );
};

export default InstituteContactForm;
