import React from 'react';
import AdminEventCard from './AdminEventCard';
import { LoadingSpinner } from '../ui';

const EventList = ({
  events = [],
  loading = false,
  onDeleteEvent,
  onPublishEvent,
  getCategoryColor,
  getStatusBadgeColor
}) => {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Events (0)</h3>
        </div>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (events.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
          <h3 className="text-xl font-bold text-gray-900 flex items-center">
            <span className="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm font-semibold mr-3">
              0
            </span>
            Events
          </h3>
        </div>
        <div className="p-6">
          <div className="text-center py-16">
            <div className="mx-auto h-16 w-16 text-gray-400 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-8 w-8">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No events found</h3>
            <p className="text-gray-500 mb-6 max-w-sm mx-auto">
              Get started by creating your first event to engage with your community
            </p>
            <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
              Create Your First Event
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div className="px-6 py-5 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
        <h3 className="text-xl font-bold text-gray-900 flex items-center">
          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold mr-3">
            {events.length}
          </span>
          Events
        </h3>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6">
          {events.map((event) => (
            <AdminEventCard
              key={event.id}
              event={event}
              onDeleteEvent={onDeleteEvent}
              onPublishEvent={onPublishEvent}
              getCategoryColor={getCategoryColor}
              getStatusBadgeColor={getStatusBadgeColor}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default EventList;
