/**
 * PaymentForm Component
 * 
 * Handles payment form creation and submission to PayFast
 * for event registrations and subscriptions.
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiCreditCard,
  FiShield,
  FiLoader,
  FiAlertCircle,
  FiCheck,
  FiX
} from 'react-icons/fi';
import {
  createEventPayment,
  createSubscriptionPayment,
  selectCreatePaymentLoading,
  selectCreatePaymentError,
  selectCreatePaymentSuccess,
  selectCurrentPayment,
  clearPaymentErrors
} from '../../store/slices/PaymentSlice';
import paymentService from '../../services/paymentService';
import { LoadingSpinner } from '../ui';

const PaymentForm = ({
  paymentType = 'event', // 'event' or 'subscription'
  paymentData,
  onSuccess,
  onCancel,
  onError,
  className = ''
}) => {
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState({});

  // Redux selectors
  const createPaymentLoading = useSelector(selectCreatePaymentLoading);
  const createPaymentError = useSelector(selectCreatePaymentError);
  const createPaymentSuccess = useSelector(selectCreatePaymentSuccess);
  const currentPayment = useSelector(selectCurrentPayment);

  // Form state
  const [formData, setFormData] = useState({
    amount: paymentData?.amount || 0,
    currency: paymentData?.currency || 'ZAR',
    user_email: paymentData?.user_email || '',
    user_name: paymentData?.user_name || '',
    ...paymentData
  });

  // Clear errors on mount
  useEffect(() => {
    dispatch(clearPaymentErrors());
  }, [dispatch]);

  // Handle successful payment creation
  useEffect(() => {
    if (createPaymentSuccess && currentPayment) {
      handlePaymentSubmission();
    }
  }, [createPaymentSuccess, currentPayment]);

  // Handle payment creation error
  useEffect(() => {
    if (createPaymentError) {
      setIsSubmitting(false);
      if (onError) {
        onError(createPaymentError);
      }
    }
  }, [createPaymentError, onError]);

  // Validate form data
  const validateForm = () => {
    const errors = {};

    if (!formData.amount || formData.amount <= 0) {
      errors.amount = 'Amount must be greater than 0';
    }

    if (!formData.user_email || !/\S+@\S+\.\S+/.test(formData.user_email)) {
      errors.user_email = 'Valid email is required';
    }

    if (!formData.user_name || formData.user_name.trim().length < 2) {
      errors.user_name = 'Full name is required';
    }

    if (paymentType === 'event' && !formData.registration_id) {
      errors.registration_id = 'Registration ID is required';
    }

    if (paymentType === 'subscription' && !formData.subscription_id) {
      errors.subscription_id = 'Subscription ID is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate return URLs
      const baseUrl = window.location.origin;
      const returnUrls = paymentService.generateReturnUrls(baseUrl, 'temp');
      
      const paymentPayload = {
        ...formData,
        ...returnUrls
      };

      // Create payment based on type
      if (paymentType === 'event') {
        await dispatch(createEventPayment(paymentPayload)).unwrap();
      } else {
        await dispatch(createSubscriptionPayment(paymentPayload)).unwrap();
      }
    } catch (error) {
      setIsSubmitting(false);
      console.error('Payment creation failed:', error);
    }
  };

  // Handle PayFast form submission
  const handlePaymentSubmission = () => {
    try {
      if (currentPayment && currentPayment.payment_data) {
        // Submit form to PayFast
        paymentService.submitPaymentForm(
          currentPayment.payment_data,
          currentPayment.payment_url
        );
        
        if (onSuccess) {
          onSuccess(currentPayment);
        }
      }
    } catch (error) {
      setIsSubmitting(false);
      console.error('Payment submission failed:', error);
      if (onError) {
        onError('Failed to submit payment form');
      }
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear specific field error
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <FiCreditCard className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {paymentType === 'event' ? 'Event Payment' : 'Subscription Payment'}
            </h3>
            <p className="text-sm text-gray-500">
              Secure payment powered by PayFast
            </p>
          </div>
        </div>
        <button
          onClick={handleCancel}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <FiX className="w-5 h-5" />
        </button>
      </div>

      {/* Payment Summary */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Amount:</span>
          <span className="text-xl font-bold text-gray-900">
            {formData.currency} {formData.amount?.toFixed(2)}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {createPaymentError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-2">
            <FiAlertCircle className="w-5 h-5 text-red-500" />
            <span className="text-red-700">{createPaymentError}</span>
          </div>
        </div>
      )}

      {/* Payment Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email Address
          </label>
          <input
            type="email"
            name="user_email"
            value={formData.user_email}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              formErrors.user_email ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter your email address"
            required
          />
          {formErrors.user_email && (
            <p className="text-red-500 text-sm mt-1">{formErrors.user_email}</p>
          )}
        </div>

        {/* Full Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Full Name
          </label>
          <input
            type="text"
            name="user_name"
            value={formData.user_name}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              formErrors.user_name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter your full name"
            required
          />
          {formErrors.user_name && (
            <p className="text-red-500 text-sm mt-1">{formErrors.user_name}</p>
          )}
        </div>

        {/* Security Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-2">
            <FiShield className="w-5 h-5 text-blue-500 mt-0.5" />
            <div className="text-sm text-blue-700">
              <p className="font-medium">Secure Payment</p>
              <p>Your payment is processed securely through PayFast. We do not store your payment information.</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3 pt-4">
          <button
            type="button"
            onClick={handleCancel}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            disabled={isSubmitting || createPaymentLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting || createPaymentLoading}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            {(isSubmitting || createPaymentLoading) ? (
              <>
                <LoadingSpinner size="sm" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <FiCreditCard className="w-4 h-4" />
                <span>Pay Now</span>
              </>
            )}
          </button>
        </div>
      </form>

      {/* PayFast Logo */}
      <div className="text-center mt-6 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          Powered by PayFast - South Africa's leading payment gateway
        </p>
      </div>
    </div>
  );
};

export default PaymentForm;
