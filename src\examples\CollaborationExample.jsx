import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { Provider } from 'react-redux';
import store from '../store';
import InstituteCollaborations from '../pages/institute/InstituteCollaborations';
import MentorCollaborations from '../pages/mentor/MentorCollaborations';

/**
 * Example of how to integrate the Collaboration system
 * This shows the complete setup for both institute and mentor users
 */

const CollaborationExample = () => {
  return (
    <Provider store={store}>
      <Router>
        <div className="min-h-screen bg-gray-50">
          {/* Navigation Example */}
          <nav className="bg-white shadow-sm border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between h-16">
                <div className="flex items-center space-x-8">
                  <h1 className="text-xl font-bold text-gray-900">EduFair</h1>
                  <div className="flex space-x-4">
                    <Link 
                      to="/institute/collaborations" 
                      className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Institute Collaborations
                    </Link>
                    <Link 
                      to="/mentor/collaborations" 
                      className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Mentor Collaborations
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </nav>

          {/* Routes */}
          <Routes>
            <Route path="/institute/collaborations" element={<InstituteCollaborations />} />
            <Route path="/mentor/collaborations" element={<MentorCollaborations />} />
            <Route path="/" element={
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="text-center">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">
                    Collaboration System Demo
                  </h2>
                  <p className="text-gray-600 mb-8">
                    Choose your user type to see the collaboration dashboard
                  </p>
                  <div className="flex justify-center space-x-4">
                    <Link 
                      to="/institute/collaborations"
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Institute Dashboard
                    </Link>
                    <Link 
                      to="/mentor/collaborations"
                      className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
                    >
                      Mentor Dashboard
                    </Link>
                  </div>
                </div>
              </div>
            } />
          </Routes>
        </div>
      </Router>
    </Provider>
  );
};

export default CollaborationExample;

/**
 * Integration Instructions:
 * 
 * 1. Add to your main App.jsx routes:
 * ```jsx
 * import InstituteCollaborations from './pages/institute/InstituteCollaborations';
 * import MentorCollaborations from './pages/mentor/MentorCollaborations';
 * 
 * // In your Routes component:
 * <Route path="/institute/collaborations" element={<InstituteCollaborations />} />
 * <Route path="/mentor/collaborations" element={<MentorCollaborations />} />
 * ```
 * 
 * 2. Add navigation links in your sidebar/navbar:
 * ```jsx
 * // For Institute users
 * <Link to="/institute/collaborations">Collaborations</Link>
 * 
 * // For Mentor users  
 * <Link to="/mentor/collaborations">Collaborations</Link>
 * ```
 * 
 * 3. The Redux store is already configured with the collaboration slice
 * 
 * 4. All 4 APIs are integrated:
 *    - GET /api/institute/mentors/invites/sent
 *    - GET /api/institute/mentors/invites/received
 *    - POST /api/institute/mentors/invite
 *    - POST /api/institute/mentors/invite/{id}/respond
 * 
 * 5. Features included:
 *    - Send invitations with hourly rate and hours per week
 *    - View sent and received invitations
 *    - Accept/reject received invitations
 *    - Real-time updates and error handling
 *    - Professional UI with loading states
 */
