import React from 'react';
import { FiHome, FiArrowLeft } from 'react-icons/fi';
import { Card } from '../../ui/layout';

const InstituteHeader = ({ institute, profile, onBack }) => {
  const getStatusBadge = (status) => {
    const styles = {
      approved: 'bg-green-500 text-white',
      pending: 'bg-yellow-500 text-white',
      rejected: 'bg-red-500 text-white'
    };
    return styles[status] || 'bg-gray-500 text-white';
  };

  return (
    <>
      <button
        onClick={onBack}
        className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white mb-6"
      >
        <FiArrowLeft className="h-4 w-4 mr-2" />
        Back to Institutes
      </button>

      <Card className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 rounded-lg bg-white bg-opacity-20 flex items-center justify-center">
            <FiHome className="h-8 w-8" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">
              {profile.institute_name || institute.institute_name || 'Unnamed Institute'}
            </h1>
            <p className="text-blue-100 text-lg">
              {profile.institute_type || institute.institute_type || 'Unknown Type'}
            </p>
            <div className="flex items-center mt-2 space-x-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusBadge(profile.verification_status)}`}>
                {profile.verification_status?.charAt(0).toUpperCase() + profile.verification_status?.slice(1) || 'Unknown'}
              </span>
              <span className="text-blue-100 text-sm">
                ID: {institute.id}
              </span>
            </div>
          </div>
        </div>
      </Card>
    </>
  );
};

export default InstituteHeader;
