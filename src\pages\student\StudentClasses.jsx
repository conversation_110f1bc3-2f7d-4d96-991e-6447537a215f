import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchMyClassrooms,
  fetchAllRequestsForStudent,
  acceptStudentRequest,
  removeStudentRequest,
  clearSuccess,
  clearError
} from "../../store/slices/ClassroomSlice";
import { fetchCurrentUser } from "../../store/slices/userSlice";
import { useThemeProvider } from "../../providers/ThemeContext";
import { useNavigate } from "react-router-dom";
import StudentClassInfo from "./StudentClassInfo";
import { getErrorMessage } from "../../utils/helpers/errorHandler";
import {
  FiUsers,
  FiBookOpen,
  FiCalendar,
  FiClock,
  FiCheck,
  FiX,
  FiEye,
  FiUserPlus,
  FiMail,
  FiRefreshCw,
  FiSearch,
  FiAlertCircle,
  FiCheckCircle,
  FiInfo
} from "react-icons/fi";

function StudentClasses() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();
  const {
    myClassrooms,
    studentRequests,
    loading,
    error,
    success
  } = useSelector((state) => state.classroom);
  const { currentUser } = useSelector((state) => state.users);

  const [selectedClassId, setSelectedClassId] = useState(null);
  const [activeTab, setActiveTab] = useState("classes"); // classes, requests
  const [searchTerm, setSearchTerm] = useState("");
  const [accepting, setAccepting] = useState({});
  const [declining, setDeclining] = useState({});
  const [refreshing, setRefreshing] = useState(false);

  // Theme classes
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const inputBg = currentTheme === "dark" ? "bg-gray-700 border-gray-600" : "bg-white border-gray-300";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";

  // Fetch current user first, then fetch classrooms and requests
  useEffect(() => {
    if (!currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    if (currentUser?.id) {
      dispatch(fetchMyClassrooms());
      dispatch(fetchAllRequestsForStudent());
    }
  }, [dispatch, currentUser]);

  // Clear success/error messages after 3 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        dispatch(clearSuccess());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [success, dispatch]);



  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        dispatch(fetchMyClassrooms()).unwrap(),
        dispatch(fetchAllRequestsForStudent()).unwrap()
      ]);
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle accepting a request
  const handleAcceptRequest = async (requestId) => {
    setAccepting(prev => ({ ...prev, [requestId]: true }));
    try {
      await dispatch(acceptStudentRequest(requestId)).unwrap();
      // Refresh both lists after accepting
      dispatch(fetchMyClassrooms());
      dispatch(fetchAllRequestsForStudent());
    } catch (error) {
      console.error('Error accepting request:', error);
    } finally {
      setAccepting(prev => ({ ...prev, [requestId]: false }));
    }
  };

  // Handle declining a request
  const handleDeclineRequest = async (requestId, classroomId) => {
    setDeclining(prev => ({ ...prev, [requestId]: true }));
    try {
      await dispatch(removeStudentRequest({ request_id: requestId, classroom_id: classroomId })).unwrap();
      dispatch(fetchAllRequestsForStudent());
    } catch (error) {
      console.error('Error declining request:', error);
    } finally {
      setDeclining(prev => ({ ...prev, [requestId]: false }));
    }
  };

  // Filter classes based on search term
  const filteredClasses = Array.isArray(myClassrooms) ? myClassrooms.filter(classroom =>
    classroom.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    classroom.subject?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    classroom.teacher?.username?.toLowerCase().includes(searchTerm.toLowerCase())
  ) : [];

  // Filter requests based on search term
  const filteredRequests = Array.isArray(studentRequests) ? studentRequests.filter(request => {
    if (!searchTerm) return true; // Show all if no search term

    const searchLower = searchTerm.toLowerCase();

    // Search in student information
    const studentMatch = (
      request.student_user?.username?.toLowerCase().includes(searchLower) ||
      request.student_user?.email?.toLowerCase().includes(searchLower) ||
      request.student_user?.country?.toLowerCase().includes(searchLower)
    );

    // Search in classroom ID
    const classroomMatch = (
      request.classroom?.toLowerCase().includes(searchLower) ||
      request.classroom_id?.toLowerCase().includes(searchLower)
    );

    // Search in request ID
    const requestMatch = request.id?.toLowerCase().includes(searchLower);

    return studentMatch || classroomMatch || requestMatch;
  }) : [];

  const pendingRequestsCount = Array.isArray(studentRequests) ? studentRequests.length : 0;

  // Loading state
  if (loading && !refreshing) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-8 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
          <p className={textSecondary}>Loading your classes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      <div className="w-full max-w-none">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold mb-2">
              My Classes
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your enrolled classes and join requests
            </p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="mt-4 sm:mt-0 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors duration-200 flex items-center gap-2"
          >
            <FiRefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-center gap-3">
            <FiCheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-green-700 dark:text-green-400">{success}</span>
          </div>
        )}

        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center gap-3">
            <FiAlertCircle className="w-5 h-5 text-red-500" />
            <span className="text-red-700 dark:text-red-400">
              {getErrorMessage(error)}
            </span>
          </div>
        )}



        {/* Tabs */}
        <div className="flex space-x-1 mb-8 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab("classes")}
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-colors ${
              activeTab === "classes"
                ? 'bg-white dark:bg-gray-800 text-violet-600 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
            }`}
          >
            <FiBookOpen className="w-4 h-4" />
            <span>My Classes</span>
            <span className="bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-300 px-2 py-1 rounded-full text-xs font-medium">
              {filteredClasses.length}
            </span>
          </button>

          <button
            onClick={() => setActiveTab("requests")}
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-colors ${
              activeTab === "requests"
                ? 'bg-white dark:bg-gray-800 text-violet-600 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
            }`}
          >
            <FiUserPlus className="w-4 h-4" />
            <span>Join Requests</span>
            {pendingRequestsCount > 0 && (
              <span className="bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-full text-xs font-medium">
                {pendingRequestsCount}
              </span>
            )}
          </button>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={`Search ${activeTab === "classes" ? "classes" : "requests"}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-violet-500 ${inputBg}`}
            />
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === "classes" && (
          <div>
            {filteredClasses.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredClasses.map((classroom) => (
                  <div
                    key={classroom.id}
                    className={`${cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all duration-200 cursor-pointer group`}
                    onClick={() => navigate(`/student/classroom/${classroom.id}`)}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-violet-700 dark:text-violet-400 mb-2 group-hover:text-violet-800 dark:group-hover:text-violet-300 transition-colors">
                          {classroom.name}
                        </h3>
                        <p className={`text-sm ${textSecondary} mb-3 line-clamp-2`}>
                          {classroom.description || "No description available"}
                        </p>
                      </div>
                      <FiEye className="w-5 h-5 text-gray-400 group-hover:text-violet-600 transition-colors" />
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center gap-2 text-sm">
                        <FiBookOpen className="w-4 h-4 text-blue-500" />
                        <span className={textSecondary}>Subject:</span>
                        <span className={textPrimary}>{classroom.subject?.name || "Not specified"}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <FiUsers className="w-4 h-4 text-green-500" />
                        <span className={textSecondary}>Teacher:</span>
                        <span className={textPrimary}>
                          {classroom.teacher?.username || classroom.teacher_user?.username || "Unknown"}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <FiCalendar className="w-4 h-4 text-purple-500" />
                        <span className={textSecondary}>Joined:</span>
                        <span className={textPrimary}>
                          {new Date(classroom.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                      <span className="inline-flex items-center gap-1 px-3 py-1 bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-300 rounded-full text-xs font-medium">
                        <FiUsers className="w-3 h-3" />
                        {classroom.students?.length || 0} students
                      </span>
                      <button className="text-violet-600 hover:text-violet-700 text-sm font-medium transition-colors">
                        View Details →
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={`${cardBg} rounded-xl p-12 text-center shadow-sm border border-gray-200 dark:border-gray-700`}>
                <FiBookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {searchTerm ? "No classes found" : "No classes enrolled"}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {searchTerm
                    ? "Try adjusting your search criteria"
                    : "You haven't joined any classes yet. Teachers can invite you to join their classrooms."
                  }
                </p>
                {!searchTerm && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mt-4">
                    <div className="flex items-start gap-3">
                      <FiInfo className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div className="text-left">
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">How to join a class:</h4>
                        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                          <li>• Ask your teacher for a classroom invitation</li>
                          <li>• Check the "Join Requests" tab for pending invitations</li>
                          <li>• Contact your school administrator for help</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Requests Tab Content */}
        {activeTab === "requests" && (
          <div>


            {filteredRequests.length > 0 ? (
              <div className="space-y-4">
                {filteredRequests.map((request) => (
                  <div
                    key={request.id}
                    className={`${cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                            <FiMail className="w-5 h-5 text-orange-600" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                              Join Request for Classroom
                            </h3>
                            <p className={`text-sm ${textSecondary}`}>
                              Classroom ID: {request.classroom_id || request.classroom}
                            </p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div className="flex items-center gap-2 text-sm">
                            <FiUsers className="w-4 h-4 text-blue-500" />
                            <span className={textSecondary}>Student:</span>
                            <span className={textPrimary}>
                              {request.student_user?.username || 'Unknown Student'}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 text-sm">
                            <FiMail className="w-4 h-4 text-green-500" />
                            <span className={textSecondary}>Email:</span>
                            <span className={textPrimary}>
                              {request.student_user?.email || 'Unknown Email'}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 text-sm">
                            <FiClock className="w-4 h-4 text-purple-500" />
                            <span className={textSecondary}>Status:</span>
                            <span className={textPrimary}>
                              {request.status || 'Pending'}
                            </span>
                          </div>
                        </div>

                        {/* Additional request details */}
                        <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                          <p className={`text-sm ${textSecondary}`}>
                            <strong>Request ID:</strong> {request.id}
                          </p>
                          <p className={`text-sm ${textSecondary}`}>
                            <strong>Classroom ID:</strong> {request.classroom_id || request.classroom}
                          </p>
                          {request.student_user?.country && (
                            <p className={`text-sm ${textSecondary}`}>
                              <strong>Country:</strong> {request.student_user.country}
                            </p>
                          )}
                          <div className="flex gap-4 mt-2">
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              request.student_user?.is_email_verified
                                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                                : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                            }`}>
                              Email: {request.student_user?.is_email_verified ? 'Verified' : 'Not Verified'}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              request.student_user?.is_mobile_verified
                                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                                : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                            }`}>
                              Mobile: {request.student_user?.is_mobile_verified ? 'Verified' : 'Not Verified'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <button
                        onClick={() => handleDeclineRequest(request.id, request.classroom_id || request.classroom)}
                        disabled={declining[request.id]}
                        className="px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 flex items-center gap-2"
                      >
                        {declining[request.id] ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                            Declining...
                          </>
                        ) : (
                          <>
                            <FiX className="w-4 h-4" />
                            Decline
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => handleAcceptRequest(request.id)}
                        disabled={accepting[request.id]}
                        className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2"
                      >
                        {accepting[request.id] ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            Accepting...
                          </>
                        ) : (
                          <>
                            <FiCheck className="w-4 h-4" />
                            Accept Invitation
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={`${cardBg} rounded-xl p-12 text-center shadow-sm border border-gray-200 dark:border-gray-700`}>
                <FiUserPlus className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {searchTerm ? "No requests found" : "No pending requests"}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {searchTerm
                    ? "Try adjusting your search criteria"
                    : "You don't have any pending classroom join requests at the moment."
                  }
                </p>
              </div>
            )}
          </div>
        )}

        {/* Class Info Modal */}
        {selectedClassId && (
          <StudentClassInfo
            classroomId={selectedClassId}
            onClose={() => setSelectedClassId(null)}
          />
        )}
      </div>
    </div>
  );
}

export default StudentClasses;