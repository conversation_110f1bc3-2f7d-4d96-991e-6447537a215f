import { getAuthToken, getCurrentUser } from '../../../utils/helpers/authHelpers';
import API_BASE_URL from '../../../utils/api/API_URL';

/**
 * AI Checking Service - Simplified API using exam_id and student_id
 * Updated to match the new simplified API endpoints
 */
class AICheckingService {
  constructor() {
    this.baseUrl = `${API_BASE_URL}/api/exams/checking`;
  }

  /**
   * Test connection to AI checking service
   */
  async testConnection() {
    try {
      const response = await fetch(`${this.baseUrl}/test/gemini-connection`);
      const result = await response.json();
      return result;
    } catch (error) {

      throw new Error('Failed to connect to AI checking service');
    }
  }

  /**
   * Check exam with AI (Students and Teachers)
   * @param {string} examId - The exam ID
   * @param {string} studentId - The student ID (null for student's own exam)
   * @param {string} token - Authentication token
   * @returns {Promise<Object>} AI checking result
   */
  async checkExamWithAI(examId, studentId = null, token = null) {
    try {
      // 🚨 CRITICAL VALIDATION: Check for valid exam ID
      if (!examId || examId === 'undefined' || examId === 'null' || examId === null || examId === undefined) {

        throw new Error(`Invalid exam ID: ${examId}. Cannot trigger AI checking.`);
      }

      const authToken = token || getAuthToken();

      if (!authToken) {
        throw new Error('No authentication token available');
      }

      let url;
      if (studentId) {
        // Teacher endpoint - check specific student
        url = `${this.baseUrl}/teacher/ai-check/${examId}/${studentId}`;

      } else {
        // Student endpoint - check own exam (automatic user detection)
        url = `${this.baseUrl}/ai-check/${examId}`;

      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Check if student is disqualified
      if (result.is_disqualified) {
        // Student is disqualified - AI checking prevented
      }

      return result;
    } catch (error) {

      throw error;
    }
  }

  /**
   * Get AI results for exam (Students and Teachers)
   * @param {string} examId - The exam ID
   * @param {string} studentId - The student ID (null for student's own results)
   * @param {string} token - Authentication token
   * @returns {Promise<Object>} AI results
   */
  async getAIResults(examId, studentId = null, token = null) {
    try {
      // 🚨 CRITICAL VALIDATION: Check for valid exam ID
      if (!examId || examId === 'undefined' || examId === 'null' || examId === null || examId === undefined) {

        throw new Error(`Invalid exam ID: ${examId}. Cannot fetch AI results.`);
      }

      const authToken = token || getAuthToken();

      if (!authToken) {
        throw new Error('No authentication token available');
      }

      let url;
      if (studentId) {
        // Teacher endpoint - get results for specific student
        url = `${this.baseUrl}/teacher/ai-results/${examId}/${studentId}`;

      } else {
        // Student endpoint - get own results (automatic user detection)
        url = `${this.baseUrl}/ai-results/${examId}`;

      }



      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Check if student is disqualified
      if (result.is_disqualified) {
        // Student is disqualified - returning disqualification info
      }

      return result;
    } catch (error) {

      throw error;
    }
  }

  /**
   * Get student's own AI results for specific exam (Student endpoint)
   * @param {string} examId - The exam ID
   * @param {string} token - Authentication token
   * @returns {Promise<Object>} Student AI results
   */
  async getStudentAIResults(examId, token = null) {
    try {
      // CRITICAL VALIDATION: Check for valid exam ID
      if (!examId || examId === 'undefined' || examId === 'null' || examId === null || examId === undefined) {
        throw new Error(`Invalid exam ID: ${examId}. Cannot fetch student AI results.`);
      }

      const authToken = token || getAuthToken();

      if (!authToken) {
        throw new Error('No authentication token available');
      }

      const url = `${this.baseUrl}/ai-results/${examId}`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if AI results exist for an exam and student
   * @param {string} examId - The exam ID
   * @param {string} studentId - The student ID (optional for students)
   * @param {string} token - Authentication token
   * @returns {Promise<boolean>} Whether AI results exist
   */
  async hasAIResults(examId, studentId = null, token = null) {
    try {
      // For students, use the student endpoint (no studentId needed)
      if (!studentId) {
        const result = await this.getStudentAIResults(examId, token);
        return result && result.success;
      }

      // For teachers, use the teacher endpoint
      const result = await this.getAIResults(examId, studentId, token);
      return result && result.success;
    } catch (error) {
      // If error is "not found" or similar, return false
      if (error.message.includes('not found') || error.message.includes('404')) {
        return false;
      }
      throw error;
    }
  }

  /**
   * Simplified method for students to check their own exam with AI
   * This is a convenience method that handles the student flow
   * @param {string} examId - The exam ID
   * @param {string} token - Authentication token
   * @returns {Promise<Object>} AI checking result
   */
  async checkMyExamWithAI(examId, token = null) {
    try {
      const user = getCurrentUser();
      if (!user || !user.id) {
        throw new Error('User information not available');
      }

      return await this.checkExamWithAI(examId, user.id, token);
    } catch (error) {

      throw error;
    }
  }

  /**
   * Wait for AI results with polling (for real-time updates)
   * @param {string} examId - The exam ID
   * @param {string} studentId - The student ID (optional for students)
   * @param {number} maxWaitTime - Maximum wait time in milliseconds
   * @param {number} pollInterval - Polling interval in milliseconds
   * @returns {Promise<Object>} AI results when available
   */
  async waitForAIResults(examId, studentId = null, maxWaitTime = 60000, pollInterval = 2000) {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const results = studentId
          ? await this.getAIResults(examId, studentId)
          : await this.getStudentAIResults(examId);

        if (results && results.success && results.result) {
          return results;
        }
      } catch (error) {
        // If error is "not found", continue polling
        if (!error.message.includes('not found') && !error.message.includes('404')) {
          throw error;
        }
      }

      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    throw new Error('Timeout waiting for AI results');
  }
}

// Export singleton instance
export default new AICheckingService();
