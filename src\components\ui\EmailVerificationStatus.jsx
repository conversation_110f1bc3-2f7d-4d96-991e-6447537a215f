import React from 'react';
import { FiCheck, FiX, FiClock, FiMail, FiAlertCircle } from 'react-icons/fi';

/**
 * EmailVerificationStatus Component
 * 
 * A reusable component for displaying email verification status
 * with appropriate icons and styling.
 * 
 * @param {Object} props
 * @param {boolean} props.isVerified - Whether email is verified
 * @param {string} props.email - Email address
 * @param {boolean} props.showEmail - Whether to show email address
 * @param {string} props.size - Size variant ('sm', 'md', 'lg')
 * @param {string} props.variant - Style variant ('badge', 'card', 'inline')
 * @param {string} props.className - Additional CSS classes
 */
const EmailVerificationStatus = ({
  isVerified,
  email,
  showEmail = true,
  size = 'md',
  variant = 'badge',
  className = '',
}) => {
  // Size configurations
  const sizeConfig = {
    sm: {
      icon: 'h-3 w-3',
      text: 'text-xs',
      padding: 'px-2 py-1',
      gap: 'space-x-1',
    },
    md: {
      icon: 'h-4 w-4',
      text: 'text-sm',
      padding: 'px-3 py-1.5',
      gap: 'space-x-2',
    },
    lg: {
      icon: 'h-5 w-5',
      text: 'text-base',
      padding: 'px-4 py-2',
      gap: 'space-x-2',
    },
  };

  const config = sizeConfig[size];

  // Status configurations
  const statusConfig = isVerified
    ? {
        icon: FiCheck,
        text: 'Verified',
        bgColor: 'bg-green-100 dark:bg-green-900/20',
        textColor: 'text-green-800 dark:text-green-200',
        borderColor: 'border-green-200 dark:border-green-800',
        iconColor: 'text-green-600 dark:text-green-400',
      }
    : {
        icon: FiX,
        text: 'Unverified',
        bgColor: 'bg-red-100 dark:bg-red-900/20',
        textColor: 'text-red-800 dark:text-red-200',
        borderColor: 'border-red-200 dark:border-red-800',
        iconColor: 'text-red-600 dark:text-red-400',
      };

  const StatusIcon = statusConfig.icon;

  // Render based on variant
  if (variant === 'badge') {
    return (
      <span
        className={`
          inline-flex items-center ${config.gap} ${config.padding} rounded-full
          ${config.text} font-medium ${statusConfig.bgColor} ${statusConfig.textColor}
          ${className}
        `}
      >
        <StatusIcon className={`${config.icon} ${statusConfig.iconColor}`} />
        {statusConfig.text}
      </span>
    );
  }

  if (variant === 'card') {
    return (
      <div
        className={`
          p-4 rounded-lg border ${statusConfig.borderColor} ${statusConfig.bgColor}
          ${className}
        `}
      >
        <div className={`flex items-center ${config.gap}`}>
          <StatusIcon className={`${config.icon} ${statusConfig.iconColor}`} />
          <div>
            <p className={`${config.text} font-medium ${statusConfig.textColor}`}>
              Email {statusConfig.text}
            </p>
            {showEmail && email && (
              <p className={`text-xs text-gray-600 dark:text-gray-400 mt-1`}>
                {email}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={`flex items-center ${config.gap} ${className}`}>
        {showEmail && email && (
          <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
            <FiMail className={config.icon} />
            <span className={config.text}>{email}</span>
          </div>
        )}
        <StatusIcon className={`${config.icon} ${statusConfig.iconColor}`} />
        <span className={`${config.text} ${statusConfig.textColor}`}>
          {statusConfig.text}
        </span>
      </div>
    );
  }

  return null;
};

/**
 * EmailVerificationAlert Component
 * 
 * A component for displaying email verification alerts/notifications.
 * 
 * @param {Object} props
 * @param {string} props.type - Alert type ('warning', 'error', 'success', 'info')
 * @param {string} props.title - Alert title
 * @param {string} props.message - Alert message
 * @param {React.ReactNode} props.action - Optional action button/link
 * @param {function} props.onDismiss - Optional dismiss callback
 * @param {string} props.className - Additional CSS classes
 */
export const EmailVerificationAlert = ({
  type = 'warning',
  title,
  message,
  action,
  onDismiss,
  className = '',
}) => {
  const alertConfig = {
    warning: {
      icon: FiAlertCircle,
      bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
      borderColor: 'border-yellow-200 dark:border-yellow-800',
      iconColor: 'text-yellow-600 dark:text-yellow-400',
      titleColor: 'text-yellow-800 dark:text-yellow-200',
      textColor: 'text-yellow-700 dark:text-yellow-300',
    },
    error: {
      icon: FiX,
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      borderColor: 'border-red-200 dark:border-red-800',
      iconColor: 'text-red-600 dark:text-red-400',
      titleColor: 'text-red-800 dark:text-red-200',
      textColor: 'text-red-700 dark:text-red-300',
    },
    success: {
      icon: FiCheck,
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      borderColor: 'border-green-200 dark:border-green-800',
      iconColor: 'text-green-600 dark:text-green-400',
      titleColor: 'text-green-800 dark:text-green-200',
      textColor: 'text-green-700 dark:text-green-300',
    },
    info: {
      icon: FiClock,
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-800',
      iconColor: 'text-blue-600 dark:text-blue-400',
      titleColor: 'text-blue-800 dark:text-blue-200',
      textColor: 'text-blue-700 dark:text-blue-300',
    },
  };

  const config = alertConfig[type];
  const AlertIcon = config.icon;

  return (
    <div
      className={`
        p-4 rounded-lg border ${config.borderColor} ${config.bgColor}
        ${className}
      `}
    >
      <div className="flex items-start space-x-3">
        <AlertIcon className={`h-5 w-5 ${config.iconColor} mt-0.5 flex-shrink-0`} />
        <div className="flex-1 min-w-0">
          {title && (
            <h3 className={`text-sm font-medium ${config.titleColor}`}>
              {title}
            </h3>
          )}
          {message && (
            <p className={`text-sm ${config.textColor} ${title ? 'mt-1' : ''}`}>
              {message}
            </p>
          )}
          {action && (
            <div className="mt-3">
              {action}
            </div>
          )}
        </div>
        {onDismiss && (
          <button
            onClick={onDismiss}
            className={`${config.iconColor} hover:opacity-75 transition-opacity`}
          >
            <FiX className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default EmailVerificationStatus;
