import React, { useState, useEffect } from 'react';
import { useUserEventStatus } from '../hooks/useUserEventStatus';
import { PublicEventsWithUserStatus } from '../components/events';
import { 
  FiCalendar, 
  FiUsers, 
  FiTrendingUp, 
  FiDollarSign,
  FiRefreshCw,
  FiFilter
} from 'react-icons/fi';

/**
 * Example of how to use the enhanced events functionality
 * This shows how users can easily see their registered events alongside public events
 */
const EnhancedEventsPageExample = () => {
  const {
    // Data
    events,
    registeredEvents,
    unregisteredEvents,
    upcomingRegisteredEvents,
    pendingPaymentEvents,
    stats,
    categories,

    // Loading states
    loading,
    updateRegistrationLoading,
    cancelRegistrationLoading,

    // Actions
    loadEventsWithUserStatus,
    updateRegistration,
    cancelRegistration,
    filterEvents,

    // Helper functions
    isUserRegisteredForEvent,
    getUserRegistrationForEvent
  } = useUserEventStatus();

  const [filters, setFilters] = useState({
    registered: null, // null = all, true = registered only, false = available only
    category: '',
    search: '',
    upcoming: false
  });

  // Load events on component mount
  useEffect(() => {
    loadEventsWithUserStatus();
  }, [loadEventsWithUserStatus]);

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Get filtered events
  const filteredEvents = filterEvents(filters);

  // Handle registration actions
  const handleUpdateRegistration = async (eventId, registrationData) => {
    try {
      await updateRegistration(eventId, registrationData);
      // Refresh events to get updated status
      loadEventsWithUserStatus();
    } catch (error) {
      console.error('Failed to update registration:', error);
    }
  };

  const handleCancelRegistration = async (eventId) => {
    try {
      await cancelRegistration(eventId);
      // Refresh events to get updated status
      loadEventsWithUserStatus();
    } catch (error) {
      console.error('Failed to cancel registration:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Events</h1>
          <p className="mt-2 text-gray-600">
            Discover events and manage your registrations
          </p>
        </div>

        {/* Quick Stats Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiCalendar className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Events</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalEvents}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiUsers className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">My Registrations</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalRegistered}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiTrendingUp className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Upcoming Events</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalUpcoming}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiDollarSign className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Payments</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalPendingPayment}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Actions */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            
            {/* Search */}
            <div className="flex-1 max-w-md">
              <input
                type="text"
                placeholder="Search events..."
                value={filters.search}
                onChange={(e) => handleFilterChange({ search: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filter buttons */}
            <div className="flex flex-wrap gap-2">
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange({ category: e.target.value })}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>

              <select
                value={filters.registered === null ? 'all' : filters.registered ? 'registered' : 'available'}
                onChange={(e) => {
                  const value = e.target.value === 'all' ? null : e.target.value === 'registered';
                  handleFilterChange({ registered: value });
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Events</option>
                <option value="registered">My Events</option>
                <option value="available">Available Events</option>
              </select>

              <button
                onClick={() => handleFilterChange({ upcoming: !filters.upcoming })}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  filters.upcoming
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                Upcoming Only
              </button>

              <button
                onClick={() => loadEventsWithUserStatus()}
                disabled={loading}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 flex items-center"
              >
                <FiRefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Registration Status Summary */}
        {stats.totalRegistered > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">My Registration Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.registrationStats.confirmed}</div>
                <div className="text-sm text-gray-600">Confirmed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{stats.registrationStats.pending}</div>
                <div className="text-sm text-gray-600">Pending</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{stats.registrationStats.cancelled}</div>
                <div className="text-sm text-gray-600">Cancelled</div>
              </div>
            </div>
          </div>
        )}

        {/* Main Events Component */}
        <PublicEventsWithUserStatus />

        {/* Quick Actions for Registered Events */}
        {pendingPaymentEvents.length > 0 && (
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">
              Action Required: Pending Payments
            </h3>
            <p className="text-yellow-700 mb-4">
              You have {pendingPaymentEvents.length} event(s) with pending payments.
            </p>
            <div className="space-y-2">
              {pendingPaymentEvents.slice(0, 3).map(event => (
                <div key={event.id} className="flex items-center justify-between bg-white rounded-lg p-3">
                  <div>
                    <div className="font-medium text-gray-900">{event.title}</div>
                    <div className="text-sm text-gray-600">
                      {new Date(event.start_datetime).toLocaleDateString()}
                    </div>
                  </div>
                  <button className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                    Complete Payment
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedEventsPageExample;
