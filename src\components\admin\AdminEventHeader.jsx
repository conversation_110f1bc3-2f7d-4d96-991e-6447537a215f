import React from 'react';
import { Fi<PERSON>rrowLeft, FiEye, FiEdit, FiTrash2 } from 'react-icons/fi';

const AdminEventHeader = ({ 
  event, 
  onBack, 
  onEdit, 
  onDelete, 
  onPublicView 
}) => {
  return (
    <div className="flex justify-between items-start mb-8">
      <div className="flex items-center">
        <button
          onClick={onBack}
          className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
        >
          <FiArrowLeft className="h-5 w-5" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Event View</h1>
          <p className="text-gray-600 mt-2">Detailed view and management</p>
        </div>
      </div>
      <div className="flex space-x-3">
        <button
          onClick={onPublicView}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <FiEye className="h-4 w-4 mr-2" />
          Public View
        </button>
        <button
          onClick={onEdit}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700"
        >
          <FiEdit className="h-4 w-4 mr-2" />
          Edit Event
        </button>
        <button
          onClick={onDelete}
          className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700"
        >
          <FiTrash2 className="h-4 w-4 mr-2" />
          Delete
        </button>
      </div>
    </div>
  );
};

export default AdminEventHeader;
