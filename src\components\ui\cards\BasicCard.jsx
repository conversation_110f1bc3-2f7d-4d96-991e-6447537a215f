import React from 'react';
import LoadingSpinner from '../LoadingSpinner';

/**
 * BasicCard - A reusable card component for all pages
 * Provides consistent styling and reduces div usage
 */
const BasicCard = ({
  children,
  title,
  subtitle,
  icon: Icon,
  actions = [],
  loading = false,
  error = null,
  className = '',
  headerClassName = '',
  contentClassName = '',
  variant = 'default', // 'default', 'outlined', 'elevated', 'flat'
  size = 'default', // 'compact', 'default', 'large'
  padding = 'default', // 'none', 'sm', 'default', 'lg'
  rounded = 'default', // 'none', 'sm', 'default', 'lg', 'full'
  shadow = 'default', // 'none', 'sm', 'default', 'lg'
  hover = false,
  clickable = false,
  onClick,
  ...props
}) => {
  // Variant styles
  const variantClasses = {
    default: 'bg-white border border-gray-200',
    outlined: 'bg-white border-2 border-gray-300',
    elevated: 'bg-white border border-gray-100',
    flat: 'bg-gray-50 border-0'
  };

  // Size configurations
  const sizeClasses = {
    compact: 'text-sm',
    default: 'text-base',
    large: 'text-lg'
  };

  // Padding configurations
  const paddingClasses = {
    none: '',
    sm: 'p-3',
    default: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8'
  };

  // Rounded configurations
  const roundedClasses = {
    none: '',
    sm: 'rounded-sm',
    default: 'rounded-lg',
    lg: 'rounded-xl',
    full: 'rounded-full'
  };

  // Shadow configurations
  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    default: 'shadow',
    lg: 'shadow-lg'
  };

  // Build class names
  const cardClasses = `
    ${variantClasses[variant] || variantClasses.default}
    ${sizeClasses[size] || sizeClasses.default}
    ${paddingClasses[padding] || paddingClasses.default}
    ${roundedClasses[rounded] || roundedClasses.default}
    ${shadowClasses[shadow] || shadowClasses.default}
    ${hover ? 'transition-shadow duration-200 hover:shadow-md' : ''}
    ${clickable ? 'cursor-pointer transition-all duration-200 hover:shadow-md' : ''}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  // Header component
  const renderHeader = () => {
    if (!title && !subtitle && !Icon && actions.length === 0) return null;

    return (
      <header className={`flex items-center justify-between mb-4 ${headerClassName}`}>
        <div className="flex items-center space-x-3 min-w-0 flex-1">
          {Icon && (
            <div className="flex-shrink-0">
              <Icon className="h-5 w-5 text-gray-600" />
            </div>
          )}
          <div className="min-w-0 flex-1">
            {title && (
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 truncate">
                {subtitle}
              </p>
            )}
          </div>
        </div>
        
        {actions.length > 0 && (
          <div className="flex items-center space-x-2 flex-shrink-0">
            {actions.map((action, index) => {
              if (React.isValidElement(action)) {
                return React.cloneElement(action, { key: index });
              }
              
              const ActionIcon = action.icon;
              return (
                <button
                  key={index}
                  onClick={action.onClick}
                  disabled={action.disabled}
                  className={`
                    p-2 rounded-md transition-colors duration-200
                    ${action.variant === 'primary' 
                      ? 'bg-blue-600 text-white hover:bg-blue-700' 
                      : action.variant === 'danger'
                      ? 'bg-red-600 text-white hover:bg-red-700'
                      : action.variant === 'success'
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }
                    ${action.disabled ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                  title={action.tooltip || action.label}
                >
                  {ActionIcon && <ActionIcon className="h-4 w-4" />}
                  {action.label && !ActionIcon && (
                    <span className="text-sm font-medium">{action.label}</span>
                  )}
                </button>
              );
            })}
          </div>
        )}
      </header>
    );
  };

  // Content component
  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="md" />
          <span className="ml-3 text-gray-600">Loading...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center py-8">
          <div className="text-red-600 mb-2">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-sm font-medium text-gray-900 mb-1">Something went wrong</h3>
          <p className="text-sm text-gray-600">{error}</p>
        </div>
      );
    }

    return (
      <div className={contentClassName}>
        {children}
      </div>
    );
  };

  return (
    <article 
      className={cardClasses}
      onClick={clickable ? onClick : undefined}
      {...props}
    >
      {renderHeader()}
      {renderContent()}
    </article>
  );
};

/**
 * Specialized BasicCard variants
 */

// List Card - for displaying lists of items
export const ListCard = ({ items = [], renderItem, emptyMessage = "No items found", ...props }) => {
  return (
    <BasicCard {...props}>
      {items.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {emptyMessage}
        </div>
      ) : (
        <ul className="space-y-3">
          {items.map((item, index) => (
            <li key={item.id || index}>
              {renderItem(item, index)}
            </li>
          ))}
        </ul>
      )}
    </BasicCard>
  );
};

// Stats Card - for displaying statistics
export const StatsCard = ({ stats = [], ...props }) => {
  return (
    <BasicCard {...props}>
      <dl className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {stats.map((stat, index) => (
          <div key={index} className="text-center">
            <dt className="text-sm font-medium text-gray-600">{stat.label}</dt>
            <dd className="text-2xl font-bold text-gray-900">{stat.value}</dd>
            {stat.change && (
              <dd className={`text-xs ${stat.change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {stat.change > 0 ? '+' : ''}{stat.change}%
              </dd>
            )}
          </div>
        ))}
      </dl>
    </BasicCard>
  );
};

// Form Card - for forms
export const FormCard = ({ onSubmit, children, ...props }) => {
  return (
    <BasicCard {...props}>
      <form onSubmit={onSubmit} className="space-y-4">
        {children}
      </form>
    </BasicCard>
  );
};

export default BasicCard;
