import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import API_BASE_URL from "../../utils/api/API_URL";

// Base URL for collaboration endpoints
const BASE_URL = `${API_BASE_URL}/api/institute/mentors`;
const getAuthToken = () => localStorage.getItem("token");

// NEW COLLABORATION API THUNKS

// Fetch sent invitations (replaces mentor applications)
export const fetchSentInvitations = createAsyncThunk(
  "instituteMentors/fetchSentInvitations",
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/sent`, {
        params: { page, size },
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// <PERSON><PERSON> received invitations
export const fetchReceivedInvitations = createAsyncThunk(
  "instituteMentors/fetchReceivedInvitations",
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/received`, {
        params: { page, size },
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Send invitation to mentor
export const sendMentorInvitation = createAsyncThunk(
  "instituteMentors/sendInvitation",
  async (invitationData, thunkAPI) => {
    try {
      // Transform the data to match API expectations
      const apiData = {
        receiver_id: invitationData.mentorId || invitationData.receiver_id,
        invitation_message: invitationData.invitationMessage || invitationData.invitation_message,
        proposed_hourly_rate: invitationData.offeredHourlyRate ? parseFloat(invitationData.offeredHourlyRate) : 0,
        proposed_hours_per_week: invitationData.expectedHoursPerWeek ? parseInt(invitationData.expectedHoursPerWeek) : 0,
        subjects_to_cover: invitationData.subjectsToCover || [],
        start_date: invitationData.startDate || undefined
      };

      // Validate required fields
      if (!apiData.receiver_id) {
        throw new Error('Mentor selection is required');
      }
      if (!apiData.proposed_hourly_rate || apiData.proposed_hourly_rate <= 0) {
        throw new Error('Valid hourly rate is required');
      }
      if (!apiData.proposed_hours_per_week || apiData.proposed_hours_per_week <= 0) {
        throw new Error('Valid hours per week is required');
      }

      // Remove undefined values (but keep 0 values for required fields)
      Object.keys(apiData).forEach(key => {
        if (apiData[key] === undefined || apiData[key] === '') {
          delete apiData[key];
        }
      });

      console.log('Sending invitation with transformed data:', apiData);

      const res = await axios.post(`${BASE_URL}/send-to-mentor`, apiData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      return res.data;
    } catch (err) {
      console.error('Error sending mentor invitation:', err);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Respond to invitation (accept/reject)
export const respondToInvitation = createAsyncThunk(
  "instituteMentors/respondToInvitation",
  async ({ invitationId, response }, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/${invitationId}/respond`, response, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// LEGACY ALIASES FOR BACKWARD COMPATIBILITY
export const fetchMentorApplications = fetchReceivedInvitations;
export const fetchMentorInvitations = fetchSentInvitations;
export const fetchPendingApplications = fetchReceivedInvitations;
export const fetchAllApplications = fetchSentInvitations;
export const fetchApplicationsHistory = fetchSentInvitations;
export const fetchReceivedApplications = fetchReceivedInvitations;
export const fetchInstituteMentors = fetchSentInvitations;
export const approveMentorApplication = respondToInvitation;
export const rejectMentorApplication = respondToInvitation;
export const inviteMentor = sendMentorInvitation;
export const activateMentor = respondToInvitation;
export const deactivateMentor = respondToInvitation;
export const terminateMentorAssociation = respondToInvitation;

// SIMPLIFIED INITIAL STATE
const initialState = {
  // Sent invitations (replaces applications)
  sentInvitations: {
    data: [],
    total: 0,
    page: 1,
    size: 20,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null
  },
  
  // Received invitations
  receivedInvitations: {
    data: [],
    total: 0,
    page: 1,
    size: 20,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null
  },
  
  // Send invitation state
  sendInvitation: {
    loading: false,
    error: null,
    success: false
  },
  
  // Respond to invitation state
  respondInvitation: {
    loading: false,
    error: null,
    success: false
  },

  // Legacy state for backward compatibility
  applications: { data: [], total: 0 },
  applicationsLoading: false,
  applicationsError: null,
  approveLoading: false,
  approveError: null,
  approveSuccess: false,
  rejectLoading: false,
  rejectError: null,
  rejectSuccess: false,
  invitations: { data: [], total: 0 },
  invitationsLoading: false,
  invitationsError: null,
  inviteLoading: false,
  inviteError: null,
  inviteSuccess: false,
  mentors: { data: [], total: 0 },
  mentorsLoading: false,
  mentorsError: null,
  terminateLoading: false,
  terminateError: null,
  terminateSuccess: false
};

// SLICE
const instituteMentorsSlice = createSlice({
  name: "instituteMentors",
  initialState,
  reducers: {
    clearErrors: (state) => {
      state.sentInvitations.error = null;
      state.receivedInvitations.error = null;
      state.sendInvitation.error = null;
      state.respondInvitation.error = null;
      // Legacy
      state.applicationsError = null;
      state.approveError = null;
      state.rejectError = null;
      state.invitationsError = null;
      state.inviteError = null;
      state.mentorsError = null;
      state.terminateError = null;
    },
    clearSuccessStates: (state) => {
      state.sendInvitation.success = false;
      state.respondInvitation.success = false;
      // Legacy
      state.approveSuccess = false;
      state.rejectSuccess = false;
      state.inviteSuccess = false;
      state.terminateSuccess = false;
    }
  },
  extraReducers: (builder) => {
    // Fetch sent invitations
    builder
      .addCase(fetchSentInvitations.pending, (state) => {
        state.sentInvitations.loading = true;
        state.sentInvitations.error = null;
        // Legacy
        state.applicationsLoading = true;
        state.mentorsLoading = true;
      })
      .addCase(fetchSentInvitations.fulfilled, (state, action) => {
        state.sentInvitations.loading = false;
        // Handle the new API response structure
        const payload = action.payload || {};
        state.sentInvitations.data = payload.invitations || payload.data || payload.results || [];
        state.sentInvitations.total = payload.total || payload.count || 0;
        state.sentInvitations.page = payload.page || payload.current_page || 1;
        state.sentInvitations.size = payload.size || payload.page_size || 20;
        state.sentInvitations.hasNext = payload.has_next || payload.hasNext || false;
        state.sentInvitations.hasPrev = payload.has_prev || payload.hasPrev || false;
        // Legacy
        state.applicationsLoading = false;
        state.applications.data = action.payload.invitations || [];
        state.applications.total = action.payload.total || 0;
        state.mentorsLoading = false;
        state.mentors.data = action.payload.invitations || [];
        state.mentors.total = action.payload.total || 0;
      })
      .addCase(fetchSentInvitations.rejected, (state, action) => {
        state.sentInvitations.loading = false;
        state.sentInvitations.error = action.payload;
        // Legacy
        state.applicationsLoading = false;
        state.applicationsError = action.payload;
        state.mentorsLoading = false;
        state.mentorsError = action.payload;
      });

    // Fetch received invitations
    builder
      .addCase(fetchReceivedInvitations.pending, (state) => {
        state.receivedInvitations.loading = true;
        state.receivedInvitations.error = null;
      })
      .addCase(fetchReceivedInvitations.fulfilled, (state, action) => {
        state.receivedInvitations.loading = false;
        // Handle different possible response structures
        const payload = action.payload || {};
        state.receivedInvitations.data = payload.invitations || payload.data || payload.results || [];
        state.receivedInvitations.total = payload.total || payload.count || 0;
        state.receivedInvitations.page = payload.page || payload.current_page || 1;
        state.receivedInvitations.size = payload.size || payload.page_size || 20;
        state.receivedInvitations.hasNext = payload.has_next || payload.hasNext || false;
        state.receivedInvitations.hasPrev = payload.has_prev || payload.hasPrev || false;
      })
      .addCase(fetchReceivedInvitations.rejected, (state, action) => {
        state.receivedInvitations.loading = false;
        state.receivedInvitations.error = action.payload;
      });

    // Send invitation
    builder
      .addCase(sendMentorInvitation.pending, (state) => {
        state.sendInvitation.loading = true;
        state.sendInvitation.error = null;
        state.sendInvitation.success = false;
        // Legacy
        state.inviteLoading = true;
        state.inviteError = null;
        state.inviteSuccess = false;
      })
      .addCase(sendMentorInvitation.fulfilled, (state) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.success = true;
        // Legacy
        state.inviteLoading = false;
        state.inviteSuccess = true;
      })
      .addCase(sendMentorInvitation.rejected, (state, action) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.error = action.payload;
        // Legacy
        state.inviteLoading = false;
        state.inviteError = action.payload;
      });

    // Respond to invitation
    builder
      .addCase(respondToInvitation.pending, (state) => {
        state.respondInvitation.loading = true;
        state.respondInvitation.error = null;
        state.respondInvitation.success = false;
        // Legacy
        state.approveLoading = true;
        state.rejectLoading = true;
        state.terminateLoading = true;
      })
      .addCase(respondToInvitation.fulfilled, (state) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.success = true;
        // Legacy
        state.approveLoading = false;
        state.approveSuccess = true;
        state.rejectLoading = false;
        state.rejectSuccess = true;
        state.terminateLoading = false;
        state.terminateSuccess = true;
      })
      .addCase(respondToInvitation.rejected, (state, action) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.error = action.payload;
        // Legacy
        state.approveLoading = false;
        state.approveError = action.payload;
        state.rejectLoading = false;
        state.rejectError = action.payload;
        state.terminateLoading = false;
        state.terminateError = action.payload;
      });
  }
});

export const { clearErrors, clearSuccessStates } = instituteMentorsSlice.actions;

// SELECTORS (Legacy compatible)
export const selectApplications = (state) => state.instituteMentors.applications;
export const selectApplicationsLoading = (state) => state.instituteMentors.applicationsLoading;
export const selectApplicationsError = (state) => state.instituteMentors.applicationsError;
export const selectMentors = (state) => state.instituteMentors.mentors;
export const selectMentorsLoading = (state) => state.instituteMentors.mentorsLoading;
export const selectMentorsError = (state) => state.instituteMentors.mentorsError;
export const selectApproveLoading = (state) => state.instituteMentors.approveLoading;
export const selectApproveSuccess = (state) => state.instituteMentors.approveSuccess;
export const selectApproveError = (state) => state.instituteMentors.approveError;
export const selectRejectLoading = (state) => state.instituteMentors.rejectLoading;
export const selectRejectSuccess = (state) => state.instituteMentors.rejectSuccess;
export const selectRejectError = (state) => state.instituteMentors.rejectError;
export const selectInviteLoading = (state) => state.instituteMentors.inviteLoading;
export const selectInviteSuccess = (state) => state.instituteMentors.inviteSuccess;
export const selectInviteError = (state) => state.instituteMentors.inviteError;
export const selectTerminateLoading = (state) => state.instituteMentors.terminateLoading;
export const selectTerminateSuccess = (state) => state.instituteMentors.terminateSuccess;
export const selectTerminateError = (state) => state.instituteMentors.terminateError;
export const selectInvitations = (state) => state.instituteMentors.invitations;
export const selectInvitationsLoading = (state) => state.instituteMentors.invitationsLoading;
export const selectInvitationsError = (state) => state.instituteMentors.invitationsError;

// New selectors
export const selectSentInvitations = (state) => state.instituteMentors.sentInvitations;
export const selectReceivedInvitations = (state) => state.instituteMentors.receivedInvitations;
export const selectSendInvitationState = (state) => state.instituteMentors.sendInvitation;
export const selectRespondInvitationState = (state) => state.instituteMentors.respondInvitation;

export default instituteMentorsSlice.reducer;
