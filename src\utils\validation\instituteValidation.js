/**
 * Institute Profile Validation Utilities
 * Comprehensive validation for all institute profile fields
 */

// Regular expressions for validation
const VALIDATION_PATTERNS = {
  // URL validation (more permissive for various URL formats)
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  
  // LinkedIn URL validation
  linkedin: /^https?:\/\/(www\.)?linkedin\.com\/(company|in|school)\/[a-zA-Z0-9-]+\/?$/,
  
  // Facebook URL validation
  facebook: /^https?:\/\/(www\.)?facebook\.com\/[a-zA-Z0-9.-]+\/?$/,
  
  // Twitter URL validation
  twitter: /^https?:\/\/(www\.)?(twitter\.com|x\.com)\/[a-zA-Z0-9_]+\/?$/,
  
  // Postal code validation (flexible for international formats)
  postalCode: /^[a-zA-Z0-9\s-]{3,10}$/,
  
  // Institute name validation (letters, numbers, spaces, common punctuation)
  instituteName: /^[a-zA-Z0-9\s\-.,&()]+$/,
  
  // City/State validation (letters, spaces, hyphens, apostrophes)
  cityState: /^[a-zA-Z\s\-']+$/,
  
  // Accreditation validation (alphanumeric with common separators)
  accreditation: /^[a-zA-Z0-9\s\-.,()]+$/
};

// Institute types (you can expand this based on your requirements)
const VALID_INSTITUTE_TYPES = [
  'university',
  'college',
  'school',
  'training_center',
  'research_institute',
  'technical_institute',
  'vocational_school',
  'online_academy',
  'certification_body',
  'other'
];

// Field validation functions
export const validateField = (fieldName, value, allFields = {}) => {
  // Handle empty values
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return null; // Let required field validation handle this
  }

  const trimmedValue = typeof value === 'string' ? value.trim() : value;

  switch (fieldName) {
    case 'institute_name':
      if (trimmedValue.length < 2) {
        return 'Institute name must be at least 2 characters long';
      }
      if (trimmedValue.length > 200) {
        return 'Institute name must be less than 200 characters';
      }
      if (!VALIDATION_PATTERNS.instituteName.test(trimmedValue)) {
        return 'Institute name contains invalid characters';
      }
      break;

    case 'description':
      if (trimmedValue.length < 10) {
        return 'Description must be at least 10 characters long';
      }
      if (trimmedValue.length > 2000) {
        return 'Description must be less than 2000 characters';
      }
      break;

    case 'address':
      if (trimmedValue.length < 5) {
        return 'Address must be at least 5 characters long';
      }
      if (trimmedValue.length > 500) {
        return 'Address must be less than 500 characters';
      }
      break;

    case 'city':
      if (trimmedValue.length < 2) {
        return 'City must be at least 2 characters long';
      }
      if (trimmedValue.length > 100) {
        return 'City must be less than 100 characters';
      }
      if (!VALIDATION_PATTERNS.cityState.test(trimmedValue)) {
        return 'City contains invalid characters';
      }
      break;

    case 'state':
      if (trimmedValue.length < 2) {
        return 'State must be at least 2 characters long';
      }
      if (trimmedValue.length > 100) {
        return 'State must be less than 100 characters';
      }
      if (!VALIDATION_PATTERNS.cityState.test(trimmedValue)) {
        return 'State contains invalid characters';
      }
      break;

    case 'postal_code':
      if (!VALIDATION_PATTERNS.postalCode.test(trimmedValue)) {
        return 'Please enter a valid postal code';
      }
      break;

    case 'website':
      if (!VALIDATION_PATTERNS.url.test(trimmedValue)) {
        return 'Please enter a valid website URL (e.g., https://example.com)';
      }
      break;

    case 'established_year':
      const year = parseInt(value);
      const currentYear = new Date().getFullYear();
      if (isNaN(year)) {
        return 'Please enter a valid year';
      }
      if (year < 1800) {
        return 'Established year cannot be before 1800';
      }
      if (year > currentYear) {
        return 'Established year cannot be in the future';
      }
      break;

    case 'institute_type':
      if (!VALID_INSTITUTE_TYPES.includes(trimmedValue)) {
        return 'Please select a valid institute type';
      }
      break;

    case 'accreditation':
      if (trimmedValue.length > 200) {
        return 'Accreditation must be less than 200 characters';
      }
      if (!VALIDATION_PATTERNS.accreditation.test(trimmedValue)) {
        return 'Accreditation contains invalid characters';
      }
      break;

    case 'linkedin_url':
      if (!VALIDATION_PATTERNS.linkedin.test(trimmedValue)) {
        return 'Please enter a valid LinkedIn URL (e.g., https://linkedin.com/company/yourcompany)';
      }
      break;

    case 'facebook_url':
      if (!VALIDATION_PATTERNS.facebook.test(trimmedValue)) {
        return 'Please enter a valid Facebook URL (e.g., https://facebook.com/yourpage)';
      }
      break;

    case 'twitter_url':
      if (!VALIDATION_PATTERNS.twitter.test(trimmedValue)) {
        return 'Please enter a valid Twitter/X URL (e.g., https://twitter.com/youraccount)';
      }
      break;

    case 'logo':
    case 'banner':
      // File validation will be handled separately in the component
      // This case handles the file object validation
      if (value && typeof value === 'object') {
        if (!value.filename || !value.content_type || !value.data) {
          return 'Invalid image file format';
        }

        // Validate content type
        const validImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
        if (!validImageTypes.includes(value.content_type)) {
          return 'Please upload a valid image file (JPEG, PNG, GIF, WebP, SVG)';
        }

        // Validate filename extension
        const validExtensions = /\.(jpg|jpeg|png|gif|webp|svg)$/i;
        if (!validExtensions.test(value.filename)) {
          return 'Image filename must have a valid extension (jpg, png, gif, webp, svg)';
        }
      }
      break;

    default:
      break;
  }

  return null; // No validation error
};

// Validate all required fields
export const validateRequiredFields = (formData, requiredFields) => {
  const errors = {};
  
  Object.keys(requiredFields).forEach(field => {
    if (field === 'documents') {
      // Documents validation is handled separately
      return;
    }
    
    const value = formData[field];
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      errors[field] = `${requiredFields[field]} is required`;
    }
  });
  
  return errors;
};

// Validate entire form
export const validateInstituteForm = (formData, requiredFields = {}) => {
  const errors = {};
  
  // Check required fields first
  const requiredErrors = validateRequiredFields(formData, requiredFields);
  Object.assign(errors, requiredErrors);
  
  // Validate individual fields (even if not required)
  Object.keys(formData).forEach(field => {
    const value = formData[field];
    if (value && value !== '') { // Only validate if field has a value
      const fieldError = validateField(field, value, formData);
      if (fieldError) {
        errors[field] = fieldError;
      }
    }
  });
  
  return errors;
};

// Real-time validation for single field
export const validateSingleField = (fieldName, value, formData = {}, requiredFields = {}) => {
  // Check if required
  if (requiredFields[fieldName] && (!value || value.toString().trim() === '')) {
    return `${requiredFields[fieldName]} is required`;
  }
  
  // Validate format if value exists
  if (value && value !== '') {
    return validateField(fieldName, value, formData);
  }
  
  return null;
};

// File validation utilities
export const validateImageFile = (file, fieldName = 'image') => {
  if (!file) return null;

  // Check file size (max 5MB)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return `${fieldName} file size must be less than 5MB`;
  }

  // Check file type
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
  if (!validTypes.includes(file.type)) {
    return `${fieldName} must be a valid image file (JPEG, PNG, GIF, WebP, SVG)`;
  }

  // Check filename extension
  const validExtensions = /\.(jpg|jpeg|png|gif|webp|svg)$/i;
  if (!validExtensions.test(file.name)) {
    return `${fieldName} filename must have a valid extension`;
  }

  return null;
};

// Convert file to base64 for API
export const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      // Remove the data:image/type;base64, prefix
      const base64 = reader.result.split(',')[1];
      resolve({
        filename: file.name,
        content_type: file.type,
        data: base64
      });
    };
    reader.onerror = error => reject(error);
  });
};

// Validate image dimensions (optional)
export const validateImageDimensions = (file, minWidth = 0, minHeight = 0, maxWidth = 4000, maxHeight = 4000) => {
  return new Promise((resolve) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(url);

      if (img.width < minWidth || img.height < minHeight) {
        resolve(`Image dimensions must be at least ${minWidth}x${minHeight} pixels`);
      } else if (img.width > maxWidth || img.height > maxHeight) {
        resolve(`Image dimensions must not exceed ${maxWidth}x${maxHeight} pixels`);
      } else {
        resolve(null);
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      resolve('Invalid image file');
    };

    img.src = url;
  });
};

// Export validation patterns for use in components
export { VALIDATION_PATTERNS, VALID_INSTITUTE_TYPES };
