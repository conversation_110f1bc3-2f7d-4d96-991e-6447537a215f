import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { FiMail, FiRefreshCw, FiClock, FiArrowLeft } from 'react-icons/fi';
import {
  sendVerificationEmail,
  verifyEmailCode,
  resendVerificationEmail,
  clearErrors,
  clearSuccessStates,
  updateResendCooldown,
  selectEmailVerification,
  selectIsEmailVerified,
  selectVerificationEmail,
  selectSendingEmail,
  selectVerifyingCode,
  selectResendingEmail,
  selectCanResend,
  selectResendCooldown,
  selectVerificationErrors,
} from '../../store/slices/EmailVerificationSlice';
import { fetchCurrentUser } from '../../store/slices/userSlice';
import VerificationCodeInput from '../../components/ui/VerificationCodeInput';
import { EmailVerificationAlert } from '../../components/ui/EmailVerificationStatus';

const EmailVerification = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const [verificationCode, setVerificationCode] = useState('');
  const [autoSent, setAutoSent] = useState(false);
  
  const emailVerification = useSelector(selectEmailVerification);
  const isEmailVerified = useSelector(selectIsEmailVerified);
  const email = useSelector(selectVerificationEmail);
  const sendingEmail = useSelector(selectSendingEmail);
  const verifyingCode = useSelector(selectVerifyingCode);
  const resendingEmail = useSelector(selectResendingEmail);
  const canResend = useSelector(selectCanResend);
  const resendCooldown = useSelector(selectResendCooldown);
  const errors = useSelector(selectVerificationErrors);
  
  const userRole = localStorage.getItem('role');

  useEffect(() => {
    if (!autoSent) {
      dispatch(sendVerificationEmail());
      setAutoSent(true);
    }
  }, [dispatch, autoSent]);

  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setInterval(() => {
        dispatch(updateResendCooldown(resendCooldown - 1));
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [resendCooldown, dispatch]);

  useEffect(() => {
    if (isEmailVerified && userRole) {
      const rolePath = userRole.toLowerCase();
      navigate(`/${rolePath}/dashboard`, { replace: true });
    }
  }, [isEmailVerified, userRole, navigate]);

  useEffect(() => {
    return () => {
      dispatch(clearErrors());
      dispatch(clearSuccessStates());
    };
  }, [dispatch]);

  const handleCodeChange = (newCode) => {
    setVerificationCode(newCode);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (verificationCode.length === 6) {
      try {
        await dispatch(verifyEmailCode(verificationCode)).unwrap();
        dispatch(fetchCurrentUser());
      } catch (error) {
        console.error('Verification failed:', error);
      }
    }
  };

  const handleResend = async () => {
    if (canResend) {
      try {
        await dispatch(resendVerificationEmail()).unwrap();
        setVerificationCode('');
      } catch (error) {
        console.error('Resend failed:', error);
      }
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('role');
    localStorage.removeItem('userdata');
    navigate('/Login');
  };

  const isCodeComplete = verificationCode.length === 6;
  const isLoading = sendingEmail || verifyingCode || resendingEmail;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4 py-12">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4">
            <FiMail className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Verify Your Email</h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            We've sent a 6-digit verification code to
          </p>
          <p className="text-sm font-medium text-gray-900 dark:text-white">
            {email || 'your email address'}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Enter verification code
            </label>
            <VerificationCodeInput
              value={verificationCode}
              onChange={handleCodeChange}
              disabled={isLoading}
              error={!!errors.verifyError}
              autoFocus={true}
            />
          </div>

          {(errors.verifyError || errors.sendError || errors.resendError) && (
            <EmailVerificationAlert
              type="error"
              message={errors.verifyError || errors.sendError || errors.resendError}
            />
          )}

          {emailVerification.showSuccessMessage && emailVerification.successMessage && (
            <EmailVerificationAlert
              type="success"
              message={emailVerification.successMessage}
            />
          )}

          <button
            type="submit"
            disabled={!isCodeComplete || isLoading}
            className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-lg text-sm font-medium text-white transition-colors duration-200 ${
              isCodeComplete && !isLoading
                ? 'bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                : 'bg-gray-400 cursor-not-allowed'
            }`}
          >
            {verifyingCode ? (
              <div className="flex items-center">
                <FiRefreshCw className="animate-spin h-4 w-4 mr-2" />
                Verifying...
              </div>
            ) : (
              'Verify Email'
            )}
          </button>
        </form>

        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">Didn't receive the code?</p>
          {canResend ? (
            <button
              onClick={handleResend}
              disabled={resendingEmail}
              className="mt-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 disabled:opacity-50"
            >
              {resendingEmail ? (
                <span className="flex items-center justify-center">
                  <FiRefreshCw className="animate-spin h-4 w-4 mr-1" />
                  Sending...
                </span>
              ) : (
                'Resend verification email'
              )}
            </button>
          ) : (
            <div className="mt-2 flex items-center justify-center text-sm text-gray-500 dark:text-gray-400">
              <FiClock className="h-4 w-4 mr-1" />
              Resend in {resendCooldown}s
            </div>
          )}
        </div>

        <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleLogout}
            className="flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            <FiArrowLeft className="h-4 w-4 mr-1" />
            Sign out
          </button>
          <p className="text-xs text-gray-500 dark:text-gray-400">Code expires in 30 minutes</p>
        </div>
      </div>
    </div>
  );
};

export default EmailVerification;
