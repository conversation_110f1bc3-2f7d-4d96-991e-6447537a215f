import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import PayFastPayment from '../payments/PayFastPayment';
import { LoadingSpinner } from '../ui';
import {
  FiCalendar,
  FiMapPin,
  FiClock,
  FiUsers,
  FiDollarSign,
  FiInfo,
  FiCheckCircle,
  FiX,
  FiCreditCard
} from 'react-icons/fi';

const EventRegistration = ({ event, onClose, onSuccess }) => {
  const navigate = useNavigate();
  const { user } = useSelector(state => state.auth);
  
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [showPayment, setShowPayment] = useState(false);
  const [registrationData, setRegistrationData] = useState({
    firstName: user?.username || '',
    lastName: '',
    email: user?.email || '',
    phone: user?.mobile || '',
    organization: '',
    specialRequirements: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Auto-select first available ticket
    if (event.tickets && event.tickets.length > 0) {
      const availableTicket = event.tickets.find(ticket => 
        ticket.status === 'ACTIVE' && ticket.total_quantity > 0
      );
      if (availableTicket) {
        setSelectedTicket(availableTicket);
      }
    }
  }, [event.tickets]);

  const handleInputChange = (field, value) => {
    setRegistrationData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const calculateTotal = () => {
    if (!selectedTicket) return 0;
    return selectedTicket.price * quantity;
  };

  const handleRegistration = async () => {
    if (!selectedTicket) {
      setError('Please select a ticket type');
      return;
    }

    if (selectedTicket.price > 0) {
      // Paid ticket - proceed to payment
      setShowPayment(true);
    } else {
      // Free ticket - register directly
      await registerForEvent();
    }
  };

  const registerForEvent = async () => {
    setLoading(true);
    setError(null);

    try {
      // API call to register for event
      const registrationPayload = {
        event_id: event.id,
        ticket_id: selectedTicket.id,
        quantity: quantity,
        registration_data: registrationData
      };

      // This would be replaced with actual API call
      console.log('Registering for event:', registrationPayload);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (onSuccess) onSuccess();
      
    } catch (err) {
      setError(err.message || 'Registration failed');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = (paymentData) => {
    console.log('Payment successful:', paymentData);
    if (onSuccess) onSuccess();
  };

  const handlePaymentError = (error) => {
    setError(error.message || 'Payment failed');
    setShowPayment(false);
  };

  const handlePaymentCancel = () => {
    setShowPayment(false);
  };

  if (showPayment && selectedTicket) {
    return (
      <PayFastPayment
        eventId={event.id}
        ticketId={selectedTicket.id}
        ticketData={{
          name: selectedTicket.name,
          price: selectedTicket.price,
          quantity: quantity,
          total_amount: calculateTotal()
        }}
        onSuccess={handlePaymentSuccess}
        onError={handlePaymentError}
        onCancel={handlePaymentCancel}
      />
    );
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-0 border max-w-2xl shadow-lg rounded-2xl bg-white">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 rounded-t-2xl text-white">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-bold mb-2">Register for Event</h2>
              <h3 className="text-xl text-blue-100">{event.title}</h3>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 p-2"
            >
              <FiX className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Event Details */}
          <div className="bg-gray-50 rounded-xl p-4 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="flex items-center">
                <FiCalendar className="h-4 w-4 text-gray-500 mr-2" />
                <span>{new Date(event.start_datetime).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center">
                <FiClock className="h-4 w-4 text-gray-500 mr-2" />
                <span>{new Date(event.start_datetime).toLocaleTimeString()}</span>
              </div>
              <div className="flex items-center">
                <FiMapPin className="h-4 w-4 text-gray-500 mr-2" />
                <span>{event.location}</span>
              </div>
              <div className="flex items-center">
                <FiUsers className="h-4 w-4 text-gray-500 mr-2" />
                <span>{event.max_attendees} max attendees</span>
              </div>
            </div>
          </div>

          {/* Ticket Selection */}
          {event.tickets && event.tickets.length > 0 && (
            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Select Ticket Type</h4>
              <div className="space-y-3">
                {event.tickets.map((ticket) => (
                  <div
                    key={ticket.id}
                    className={`border rounded-xl p-4 cursor-pointer transition-all duration-200 ${
                      selectedTicket?.id === ticket.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedTicket(ticket)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h5 className="font-semibold text-gray-900">{ticket.name}</h5>
                        <p className="text-sm text-gray-600 mt-1">{ticket.description}</p>
                        <div className="flex items-center mt-2 text-sm text-gray-500">
                          <FiDollarSign className="h-4 w-4 mr-1" />
                          <span className="font-medium">
                            {ticket.price === 0 ? 'Free' : `R${ticket.price}`}
                          </span>
                          <span className="mx-2">•</span>
                          <span>{ticket.total_quantity} available</span>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          selectedTicket?.id === ticket.id
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-300'
                        }`}>
                          {selectedTicket?.id === ticket.id && (
                            <FiCheckCircle className="w-4 h-4 text-white" />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Quantity Selection */}
          {selectedTicket && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantity
              </label>
              <select
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {Array.from({ length: Math.min(selectedTicket.max_quantity_per_order, selectedTicket.total_quantity) }, (_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {i + 1} {i === 0 ? 'ticket' : 'tickets'}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Registration Form */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Registration Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First Name *
                </label>
                <input
                  type="text"
                  value={registrationData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Last Name *
                </label>
                <input
                  type="text"
                  value={registrationData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email *
                </label>
                <input
                  type="email"
                  value={registrationData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone *
                </label>
                <input
                  type="tel"
                  value={registrationData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Organization
                </label>
                <input
                  type="text"
                  value={registrationData.organization}
                  onChange={(e) => handleInputChange('organization', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Your organization or company"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Special Requirements
                </label>
                <textarea
                  value={registrationData.specialRequirements}
                  onChange={(e) => handleInputChange('specialRequirements', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Any dietary restrictions, accessibility needs, etc."
                />
              </div>
            </div>
          </div>

          {/* Total and Actions */}
          {selectedTicket && (
            <div className="border-t pt-6">
              <div className="flex justify-between items-center mb-4">
                <span className="text-lg font-semibold text-gray-900">Total:</span>
                <span className="text-2xl font-bold text-blue-600">
                  {calculateTotal() === 0 ? 'Free' : `R${calculateTotal().toFixed(2)}`}
                </span>
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                  <p className="text-red-800 text-sm">{error}</p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={onClose}
                  className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRegistration}
                  disabled={loading || !selectedTicket}
                  className="flex-1 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <LoadingSpinner size="sm" className="mr-2" />
                  ) : selectedTicket?.price > 0 ? (
                    <FiCreditCard className="h-5 w-5 mr-2" />
                  ) : (
                    <FiCheckCircle className="h-5 w-5 mr-2" />
                  )}
                  {loading ? 'Processing...' : selectedTicket?.price > 0 ? 'Proceed to Payment' : 'Register Now'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EventRegistration;
