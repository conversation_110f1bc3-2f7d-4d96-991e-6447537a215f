// React import not needed for this component
import { useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiUsers,
  FiMapPin,
  FiEdit,
  FiTrash2,
  FiEye
} from 'react-icons/fi';
import { format } from 'date-fns';

const AdminEventCard = ({
  event,
  onDeleteEvent,
  onPublishEvent,
  getCategoryColor,
  getStatusBadgeColor
}) => {
  const navigate = useNavigate();

  const formatDate = (dateString) => {
    if (!dateString) return 'TBD';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this event?')) {
      if (onDeleteEvent && typeof onDeleteEvent === 'function') {
        onDeleteEvent(event.id);
      }
    }
  };

  const handlePublish = () => {
    if (window.confirm('Are you sure you want to publish this event?')) {
      if (onPublishEvent && typeof onPublishEvent === 'function') {
        onPublishEvent(event.id);
      }
    }
  };

  return (
    <div className="border border-gray-200 rounded-xl hover:shadow-lg hover:border-blue-300
                    transition-all duration-300 overflow-hidden bg-white group h-fit
                    hover:scale-[1.02] transform-gpu">

      {/* Banner Image */}
      {event.banner_image_url && (
        <div className="aspect-w-16 aspect-h-9 bg-gray-200">
          <img
            src={event.banner_image_url}
            alt={event.title}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
            onError={(e) => {
              e.target.style.display = 'none';
            }}
          />
        </div>
      )}

      {/* Content */}
      <div className="p-5">
        {/* Title and Status Row */}
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1 min-w-0 pr-3">
            <h4 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors leading-tight mb-2">
              {event.title}
            </h4>
            <div className="flex items-center gap-2">
              <span className={`
                px-3 py-1 text-xs font-semibold rounded-full whitespace-nowrap shadow-sm
                ${getCategoryColor(event.category || 'other')}
              `}>
                {event.category ?
                  event.category.charAt(0).toUpperCase() + event.category.slice(1).toLowerCase() :
                  'Event'
                }
              </span>
            </div>
          </div>
          <span className={`
            inline-flex items-center px-3 py-1 rounded-full text-xs font-bold
            whitespace-nowrap shadow-sm ${getStatusBadgeColor(event.status)}
          `}>
            {event.status?.toUpperCase()}
          </span>
        </div>

        {/* Description */}
        <p className="text-sm text-gray-600 mb-4 line-clamp-2 leading-relaxed">
          {event.description}
        </p>

        {/* Event Details */}
        <div className="space-y-3 mb-5">
          <div className="flex items-center text-sm font-medium text-gray-700 bg-blue-50 px-3 py-2 rounded-lg">
            <FiCalendar className="h-4 w-4 mr-3 text-blue-600" />
            <span>{formatDate(event.start_datetime || event.startDateTime)}</span>
          </div>
          <div className="flex items-center text-sm font-medium text-gray-700 bg-green-50 px-3 py-2 rounded-lg">
            <FiMapPin className="h-4 w-4 mr-3 text-green-600" />
            <span className="truncate">{event.location || 'TBD'}</span>
          </div>
          <div className="flex items-center text-sm font-medium text-gray-700 bg-purple-50 px-3 py-2 rounded-lg">
            <FiUsers className="h-4 w-4 mr-3 text-purple-600" />
            <span>{event.max_attendees || event.attendeesCount || 0} max attendees</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <button
            onClick={() => navigate(`/institute/events/${event.id}`)}
            className="p-2.5 text-blue-600 hover:text-white hover:bg-blue-600 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
            title="View Details"
          >
            <FiEye className="h-4 w-4" />
          </button>
          <button
            onClick={() => navigate(`/institute/events/${event.id}/edit`)}
            className="p-2.5 text-gray-600 hover:text-white hover:bg-gray-600 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
            title="Edit Event"
          >
            <FiEdit className="h-4 w-4" />
          </button>
          <button
            onClick={handleDelete}
            className="p-2.5 text-red-600 hover:text-white hover:bg-red-600 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
            title="Delete Event"
          >
            <FiTrash2 className="h-4 w-4" />
          </button>
        </div>

        {event.status?.toLowerCase() === 'draft' && (
          <button
            onClick={handlePublish}
            className="px-4 py-2 text-sm bg-gradient-to-r from-green-500 to-green-600 text-white
                     rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200
                     whitespace-nowrap font-semibold shadow-sm hover:shadow-md transform hover:scale-105"
          >
            Publish
          </button>
        )}
        </div>
      </div>
    </div>
  );
};

export default AdminEventCard;
