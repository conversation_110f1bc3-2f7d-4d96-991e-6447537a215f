/**
 * Email Verification Service
 * 
 * This service provides functions for email verification operations
 * using the EduFair email verification API endpoints.
 */

import axios from 'axios';
import API_BASE_URL from '../utils/api/API_URL';
import { handleApiError } from '../utils/helpers/errorHandler';
import logger from '../utils/helpers/logger';

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Helper function to create auth headers
const getAuthHeaders = () => ({
  'Authorization': `Bearer ${getAuthToken()}`,
  'Content-Type': 'application/json'
});

/**
 * Send verification email to the current authenticated user
 * @returns {Promise<Object>} Response data with message and email
 */
export const sendVerificationEmail = async () => {
  try {
    logger.info('Sending verification email', {}, 'EmailVerificationService');
    
    const response = await axios.post(
      `${API_BASE_URL}/api/auth/send-verification`,
      {},
      { headers: getAuthHeaders() }
    );

    logger.info('Verification email sent successfully', { 
      email: response.data.email 
    }, 'EmailVerificationService');

    return response.data;
  } catch (error) {
    const errorData = handleApiError(error, 'EmailVerificationService');
    logger.error('Failed to send verification email', errorData, 'EmailVerificationService');
    throw error;
  }
};

/**
 * Verify email using 6-digit verification code
 * @param {string} verificationCode - 6-digit verification code
 * @returns {Promise<Object>} Response data with verification details
 */
export const verifyEmailCode = async (verificationCode) => {
  try {
    if (!verificationCode || verificationCode.length !== 6) {
      throw new Error('Verification code must be 6 digits');
    }

    logger.info('Verifying email code', { 
      codeLength: verificationCode.length 
    }, 'EmailVerificationService');
    
    const response = await axios.post(
      `${API_BASE_URL}/api/auth/verify-email`,
      { verification_code: verificationCode },
      { headers: getAuthHeaders() }
    );

    logger.info('Email verified successfully', { 
      email: response.data.email,
      userId: response.data.user_id 
    }, 'EmailVerificationService');

    return response.data;
  } catch (error) {
    const errorData = handleApiError(error, 'EmailVerificationService');
    logger.error('Failed to verify email code', errorData, 'EmailVerificationService');
    throw error;
  }
};

/**
 * Resend verification email to the current authenticated user
 * @returns {Promise<Object>} Response data with message and email
 */
export const resendVerificationEmail = async () => {
  try {
    logger.info('Resending verification email', {}, 'EmailVerificationService');
    
    const response = await axios.post(
      `${API_BASE_URL}/api/auth/resend-verification`,
      {},
      { headers: getAuthHeaders() }
    );

    logger.info('Verification email resent successfully', { 
      email: response.data.email 
    }, 'EmailVerificationService');

    return response.data;
  } catch (error) {
    const errorData = handleApiError(error, 'EmailVerificationService');
    logger.error('Failed to resend verification email', errorData, 'EmailVerificationService');
    throw error;
  }
};

/**
 * Check current user's email verification status
 * @returns {Promise<Object>} Verification status data
 */
export const checkVerificationStatus = async () => {
  try {
    logger.info('Checking verification status', {}, 'EmailVerificationService');
    
    const response = await axios.get(
      `${API_BASE_URL}/api/auth/verification-status`,
      { headers: { 'Authorization': `Bearer ${getAuthToken()}` } }
    );

    logger.info('Verification status retrieved', { 
      isVerified: response.data.is_email_verified,
      email: response.data.email 
    }, 'EmailVerificationService');

    return response.data;
  } catch (error) {
    const errorData = handleApiError(error, 'EmailVerificationService');
    logger.error('Failed to check verification status', errorData, 'EmailVerificationService');
    throw error;
  }
};

/**
 * Validate verification code format
 * @param {string} code - Verification code to validate
 * @returns {boolean} True if code is valid format
 */
export const validateVerificationCode = (code) => {
  if (!code) return false;
  
  // Remove any whitespace
  const cleanCode = code.replace(/\s/g, '');
  
  // Check if it's exactly 6 digits
  return /^\d{6}$/.test(cleanCode);
};

/**
 * Format verification code for display (adds spaces for readability)
 * @param {string} code - Verification code to format
 * @returns {string} Formatted code (e.g., "123 456")
 */
export const formatVerificationCode = (code) => {
  if (!code) return '';
  
  const cleanCode = code.replace(/\s/g, '');
  
  if (cleanCode.length <= 3) {
    return cleanCode;
  }
  
  return `${cleanCode.slice(0, 3)} ${cleanCode.slice(3)}`;
};

/**
 * Clean verification code (remove spaces and non-digits)
 * @param {string} code - Verification code to clean
 * @returns {string} Clean code with only digits
 */
export const cleanVerificationCode = (code) => {
  if (!code) return '';
  return code.replace(/\D/g, '');
};

/**
 * Check if user can resend verification email (rate limiting)
 * @param {Date} lastSentAt - Timestamp of last email sent
 * @param {number} cooldownMinutes - Cooldown period in minutes (default: 1)
 * @returns {Object} Object with canResend boolean and remaining time
 */
export const canResendEmail = (lastSentAt, cooldownMinutes = 1) => {
  if (!lastSentAt) {
    return { canResend: true, remainingTime: 0 };
  }

  const now = new Date();
  const lastSent = new Date(lastSentAt);
  const cooldownMs = cooldownMinutes * 60 * 1000;
  const timeSinceLastSent = now - lastSent;
  const remainingTime = Math.max(0, cooldownMs - timeSinceLastSent);

  return {
    canResend: remainingTime === 0,
    remainingTime: Math.ceil(remainingTime / 1000) // Return in seconds
  };
};

/**
 * Get user-friendly error message for verification errors
 * @param {Error} error - Error object from API
 * @returns {string} User-friendly error message
 */
export const getVerificationErrorMessage = (error) => {
  const errorMessage = error.response?.data?.detail || 
                      error.response?.data?.message || 
                      error.message;

  // Map common error messages to user-friendly ones
  const errorMap = {
    'Verification token has expired': 'Your verification code has expired. Please request a new one.',
    'Invalid verification token': 'Invalid verification code. Please check and try again.',
    'User not found': 'User account not found. Please contact support.',
    'Email already verified': 'Your email is already verified.',
    'Too many requests': 'Too many requests. Please wait before requesting another verification email.',
    'Rate limit exceeded': 'You\'ve reached the limit for verification emails. Please wait before trying again.',
  };

  return errorMap[errorMessage] || errorMessage || 'An unexpected error occurred. Please try again.';
};

// Export all functions as default object for easier importing
export default {
  sendVerificationEmail,
  verifyEmailCode,
  resendVerificationEmail,
  checkVerificationStatus,
  validateVerificationCode,
  formatVerificationCode,
  cleanVerificationCode,
  canResendEmail,
  getVerificationErrorMessage,
};
