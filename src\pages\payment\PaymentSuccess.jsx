import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { 
  FiCheck, 
  FiCalendar, 
  FiMapPin, 
  FiCreditCard, 
  FiDownload,
  FiArrowRight,
  FiHome,
  FiMail
} from 'react-icons/fi';
import { getPaymentStatus } from '../../store/slices/PaymentSlice';
import { useNotification } from '../../contexts/NotificationContext';
import { LoadingSpinner } from '../../components/ui';
import { getCurrentUser } from '../../utils/helpers/authHelpers';

const PaymentSuccess = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { showSuccess, showError } = useNotification();
  
  // Local state
  const [loading, setLoading] = useState(true);
  const [paymentData, setPaymentData] = useState(null);
  const [eventData, setEventData] = useState(null);
  const [error, setError] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);

  // Get payment ID from URL params
  const paymentId = searchParams.get('payment_id');
  const status = searchParams.get('status');

  useEffect(() => {
    const user = getCurrentUser();
    setCurrentUser(user);

    if (paymentId) {
      verifyPayment();
    } else {
      setError('Payment ID not found');
      setLoading(false);
    }
  }, [paymentId]);

  const verifyPayment = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await dispatch(getPaymentStatus(paymentId)).unwrap();
      
      if (result.status === 'COMPLETE' || result.status === 'complete') {
        setPaymentData(result);
        setEventData(result.event_details);
        showSuccess('Payment completed successfully!');
      } else {
        setError(`Payment verification failed. Status: ${result.status}`);
      }
    } catch (err) {
      setError(err.message || 'Failed to verify payment');
      showError('Payment verification failed');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount, currency = 'ZAR') => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleDownloadReceipt = () => {
    // Implement receipt download functionality
    showSuccess('Receipt download will be available soon');
  };

  const handleGoToEvent = () => {
    if (eventData?.id) {
      navigate(`/events/${eventData.id}`);
    }
  };

  const handleGoToDashboard = () => {
    const userRole = currentUser?.user_type || currentUser?.role;
    if (userRole) {
      navigate(`/${userRole.toLowerCase()}/dashboard`);
    } else {
      navigate('/');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <h2 className="text-xl font-semibold text-gray-900 mt-4">Verifying Payment</h2>
            <p className="text-gray-600 mt-2">Please wait while we confirm your payment...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FiCreditCard className="h-8 w-8 text-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Payment Verification Failed</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            
            <div className="space-y-3">
              <button
                onClick={verifyPayment}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={handleGoToDashboard}
                className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Go to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4">
        {/* Success Header */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden mb-6">
          <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-8 text-white text-center">
            <div className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
              <FiCheck className="h-10 w-10" />
            </div>
            <h1 className="text-2xl font-bold mb-2">Payment Successful!</h1>
            <p className="text-green-100">
              Your ticket purchase has been completed successfully
            </p>
          </div>
        </div>

        {/* Payment Details */}
        {paymentData && (
          <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Details</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Payment ID</label>
                <p className="text-gray-900 font-mono text-sm">{paymentData.payment_id}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Amount Paid</label>
                <p className="text-gray-900 font-semibold text-lg">
                  {formatCurrency(paymentData.amount, paymentData.currency)}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Payment Date</label>
                <p className="text-gray-900">{formatDate(paymentData.created_at)}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Status</label>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  <FiCheck className="h-4 w-4 mr-1" />
                  Completed
                </span>
              </div>
            </div>

            {paymentData.ticket_details && (
              <div className="border-t border-gray-200 pt-4">
                <h3 className="text-md font-medium text-gray-900 mb-2">Ticket Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Ticket Type</label>
                    <p className="text-gray-900">{paymentData.ticket_details.name}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Quantity</label>
                    <p className="text-gray-900">{paymentData.quantity || 1}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Event Details */}
        {eventData && (
          <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Event Details</h2>
            
            <div className="flex items-start space-x-4">
              {eventData.banner_image_url && (
                <img
                  src={eventData.banner_image_url}
                  alt={eventData.title}
                  className="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                />
              )}
              
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{eventData.title}</h3>
                
                <div className="space-y-2 text-sm text-gray-600">
                  {eventData.start_datetime && (
                    <div className="flex items-center">
                      <FiCalendar className="h-4 w-4 mr-2" />
                      {formatDate(eventData.start_datetime)}
                    </div>
                  )}
                  
                  {eventData.location && (
                    <div className="flex items-center">
                      <FiMapPin className="h-4 w-4 mr-2" />
                      {eventData.location}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Next Steps */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">What's Next?</h2>
          
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start">
              <FiMail className="h-4 w-4 mr-3 mt-0.5 text-blue-500" />
              <p>A confirmation email has been sent to your registered email address with your ticket details.</p>
            </div>
            
            <div className="flex items-start">
              <FiCalendar className="h-4 w-4 mr-3 mt-0.5 text-blue-500" />
              <p>Please save the event date and arrive on time. Check your email for any event updates.</p>
            </div>
            
            <div className="flex items-start">
              <FiDownload className="h-4 w-4 mr-3 mt-0.5 text-blue-500" />
              <p>You can download your receipt and ticket from your payment history.</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleDownloadReceipt}
            className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors"
          >
            <FiDownload className="h-5 w-5 mr-2" />
            Download Receipt
          </button>
          
          {eventData && (
            <button
              onClick={handleGoToEvent}
              className="w-full flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-semibold hover:bg-gray-200 transition-colors"
            >
              <FiArrowRight className="h-5 w-5 mr-2" />
              View Event Details
            </button>
          )}
          
          <button
            onClick={handleGoToDashboard}
            className="w-full flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 transition-colors"
          >
            <FiHome className="h-5 w-5 mr-2" />
            Go to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess;
