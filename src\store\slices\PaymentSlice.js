/**
 * Payment Redux Slice
 * 
 * Manages payment state including PayFast payments, status tracking,
 * and payment form data for the EduFair application.
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import paymentService from '../../services/paymentService';
import logger from '../../utils/helpers/logger';

// Async Thunks for Payment Operations

/**
 * Create event payment
 */
export const createEventPayment = createAsyncThunk(
  'payment/createEventPayment',
  async (paymentData, { rejectWithValue }) => {
    try {
      const response = await paymentService.createEventPayment(paymentData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to create event payment');
    }
  }
);

/**
 * Create subscription payment
 */
export const createSubscriptionPayment = createAsyncThunk(
  'payment/createSubscriptionPayment',
  async (paymentData, { rejectWithValue }) => {
    try {
      const response = await paymentService.createSubscriptionPayment(paymentData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to create subscription payment');
    }
  }
);

/**
 * Get payment form data
 */
export const getPaymentFormData = createAsyncThunk(
  'payment/getPaymentFormData',
  async (paymentId, { rejectWithValue }) => {
    try {
      const response = await paymentService.getPaymentFormData(paymentId);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to get payment form data');
    }
  }
);

/**
 * Get payment status
 */
export const getPaymentStatus = createAsyncThunk(
  'payment/getPaymentStatus',
  async (paymentId, { rejectWithValue }) => {
    try {
      const response = await paymentService.getPaymentStatus(paymentId);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to get payment status');
    }
  }
);

/**
 * Process payment refund
 */
export const processPaymentRefund = createAsyncThunk(
  'payment/processRefund',
  async ({ paymentId, refundData }, { rejectWithValue }) => {
    try {
      const response = await paymentService.processRefund(paymentId, refundData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to process refund');
    }
  }
);

/**
 * Get PayFast configuration
 */
export const getPayFastConfig = createAsyncThunk(
  'payment/getPayFastConfig',
  async (_, { rejectWithValue }) => {
    try {
      const response = await paymentService.getPayFastConfig();
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to get PayFast configuration');
    }
  }
);

/**
 * Poll payment status until completion
 */
export const pollPaymentStatus = createAsyncThunk(
  'payment/pollPaymentStatus',
  async ({ paymentId, maxAttempts = 30, interval = 2000 }, { rejectWithValue }) => {
    try {
      const response = await paymentService.pollPaymentStatus(paymentId, maxAttempts, interval);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Payment status polling failed');
    }
  }
);

/**
 * Get payment history for current user
 */
export const getPaymentHistory = createAsyncThunk(
  'payment/getPaymentHistory',
  async (filters = {}, { rejectWithValue }) => {
    try {
      const response = await paymentService.getPaymentHistory(filters);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to get payment history');
    }
  }
);

/**
 * Get user payment statistics
 */
export const getPaymentStats = createAsyncThunk(
  'payment/getPaymentStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await paymentService.getPaymentStats();
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to get payment statistics');
    }
  }
);

// Initial State
const initialState = {
  // Current payment data
  currentPayment: null,
  paymentFormData: null,
  paymentStatus: null,
  
  // PayFast configuration
  payFastConfig: null,
  
  // Payment history and stats
  paymentHistory: [],
  paymentStats: null,

  // Loading states
  createPaymentLoading: false,
  formDataLoading: false,
  statusLoading: false,
  refundLoading: false,
  configLoading: false,
  pollingLoading: false,
  historyLoading: false,
  statsLoading: false,

  // Error states
  createPaymentError: null,
  formDataError: null,
  statusError: null,
  refundError: null,
  configError: null,
  pollingError: null,
  historyError: null,
  statsError: null,
  
  // Success states
  createPaymentSuccess: false,
  refundSuccess: false,
  
  // UI state
  showPaymentModal: false,
  showRefundModal: false,
  selectedPaymentId: null,
  
  // Payment flow state
  paymentFlow: {
    step: 'idle', // idle, creating, redirecting, processing, completed, failed
    registrationId: null,
    eventId: null,
    amount: null,
    currency: 'ZAR'
  }
};

// Payment Slice
const paymentSlice = createSlice({
  name: 'payment',
  initialState,
  reducers: {
    // Clear payment errors
    clearPaymentErrors: (state) => {
      state.createPaymentError = null;
      state.formDataError = null;
      state.statusError = null;
      state.refundError = null;
      state.configError = null;
      state.pollingError = null;
    },
    
    // Clear payment success states
    clearPaymentSuccess: (state) => {
      state.createPaymentSuccess = false;
      state.refundSuccess = false;
    },
    
    // Set payment modal visibility
    setPaymentModalVisible: (state, action) => {
      state.showPaymentModal = action.payload;
    },
    
    // Set refund modal visibility
    setRefundModalVisible: (state, action) => {
      state.showRefundModal = action.payload;
    },
    
    // Set selected payment ID
    setSelectedPaymentId: (state, action) => {
      state.selectedPaymentId = action.payload;
    },
    
    // Update payment flow state
    updatePaymentFlow: (state, action) => {
      state.paymentFlow = { ...state.paymentFlow, ...action.payload };
    },
    
    // Reset payment flow
    resetPaymentFlow: (state) => {
      state.paymentFlow = {
        step: 'idle',
        registrationId: null,
        eventId: null,
        amount: null,
        currency: 'ZAR'
      };
    },
    
    // Add payment to history
    addPaymentToHistory: (state, action) => {
      const existingIndex = state.paymentHistory.findIndex(
        payment => payment.payment_id === action.payload.payment_id
      );
      
      if (existingIndex >= 0) {
        state.paymentHistory[existingIndex] = action.payload;
      } else {
        state.paymentHistory.unshift(action.payload);
      }
    },
    
    // Clear current payment data
    clearCurrentPayment: (state) => {
      state.currentPayment = null;
      state.paymentFormData = null;
      state.paymentStatus = null;
    }
  },
  extraReducers: (builder) => {
    // Create Event Payment
    builder
      .addCase(createEventPayment.pending, (state) => {
        state.createPaymentLoading = true;
        state.createPaymentError = null;
        state.createPaymentSuccess = false;
        state.paymentFlow.step = 'creating';
      })
      .addCase(createEventPayment.fulfilled, (state, action) => {
        state.createPaymentLoading = false;
        state.createPaymentSuccess = true;
        state.currentPayment = action.payload;
        state.paymentFlow.step = 'redirecting';
        
        // Add to history
        paymentSlice.caseReducers.addPaymentToHistory(state, action);
      })
      .addCase(createEventPayment.rejected, (state, action) => {
        state.createPaymentLoading = false;
        state.createPaymentError = action.payload;
        state.paymentFlow.step = 'failed';
      });

    // Create Subscription Payment
    builder
      .addCase(createSubscriptionPayment.pending, (state) => {
        state.createPaymentLoading = true;
        state.createPaymentError = null;
        state.createPaymentSuccess = false;
      })
      .addCase(createSubscriptionPayment.fulfilled, (state, action) => {
        state.createPaymentLoading = false;
        state.createPaymentSuccess = true;
        state.currentPayment = action.payload;
      })
      .addCase(createSubscriptionPayment.rejected, (state, action) => {
        state.createPaymentLoading = false;
        state.createPaymentError = action.payload;
      });

    // Get Payment Form Data
    builder
      .addCase(getPaymentFormData.pending, (state) => {
        state.formDataLoading = true;
        state.formDataError = null;
      })
      .addCase(getPaymentFormData.fulfilled, (state, action) => {
        state.formDataLoading = false;
        state.paymentFormData = action.payload;
      })
      .addCase(getPaymentFormData.rejected, (state, action) => {
        state.formDataLoading = false;
        state.formDataError = action.payload;
      });

    // Get Payment Status
    builder
      .addCase(getPaymentStatus.pending, (state) => {
        state.statusLoading = true;
        state.statusError = null;
      })
      .addCase(getPaymentStatus.fulfilled, (state, action) => {
        state.statusLoading = false;
        state.paymentStatus = action.payload;
        
        // Update payment flow based on status
        if (action.payload.status === 'completed') {
          state.paymentFlow.step = 'completed';
        } else if (action.payload.status === 'failed') {
          state.paymentFlow.step = 'failed';
        }
        
        // Update payment in history
        paymentSlice.caseReducers.addPaymentToHistory(state, action);
      })
      .addCase(getPaymentStatus.rejected, (state, action) => {
        state.statusLoading = false;
        state.statusError = action.payload;
      });

    // Process Payment Refund
    builder
      .addCase(processPaymentRefund.pending, (state) => {
        state.refundLoading = true;
        state.refundError = null;
        state.refundSuccess = false;
      })
      .addCase(processPaymentRefund.fulfilled, (state, action) => {
        state.refundLoading = false;
        state.refundSuccess = true;
        
        // Update payment status
        if (state.paymentStatus && state.paymentStatus.payment_id === action.payload.payment_id) {
          state.paymentStatus.status = 'refunded';
        }
      })
      .addCase(processPaymentRefund.rejected, (state, action) => {
        state.refundLoading = false;
        state.refundError = action.payload;
      });

    // Get PayFast Config
    builder
      .addCase(getPayFastConfig.pending, (state) => {
        state.configLoading = true;
        state.configError = null;
      })
      .addCase(getPayFastConfig.fulfilled, (state, action) => {
        state.configLoading = false;
        state.payFastConfig = action.payload;
      })
      .addCase(getPayFastConfig.rejected, (state, action) => {
        state.configLoading = false;
        state.configError = action.payload;
      });

    // Poll Payment Status
    builder
      .addCase(pollPaymentStatus.pending, (state) => {
        state.pollingLoading = true;
        state.pollingError = null;
        state.paymentFlow.step = 'processing';
      })
      .addCase(pollPaymentStatus.fulfilled, (state, action) => {
        state.pollingLoading = false;
        state.paymentStatus = action.payload;
        
        // Update payment flow based on final status
        if (action.payload.status === 'completed') {
          state.paymentFlow.step = 'completed';
        } else if (action.payload.status === 'failed') {
          state.paymentFlow.step = 'failed';
        }
        
        // Update payment in history
        paymentSlice.caseReducers.addPaymentToHistory(state, action);
      })
      .addCase(pollPaymentStatus.rejected, (state, action) => {
        state.pollingLoading = false;
        state.pollingError = action.payload;
        state.paymentFlow.step = 'failed';
      })

      // Get Payment History
      .addCase(getPaymentHistory.pending, (state) => {
        state.historyLoading = true;
        state.historyError = null;
      })
      .addCase(getPaymentHistory.fulfilled, (state, action) => {
        state.historyLoading = false;
        state.paymentHistory = action.payload;
      })
      .addCase(getPaymentHistory.rejected, (state, action) => {
        state.historyLoading = false;
        state.historyError = action.payload;
      })

      // Get Payment Stats
      .addCase(getPaymentStats.pending, (state) => {
        state.statsLoading = true;
        state.statsError = null;
      })
      .addCase(getPaymentStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.paymentStats = action.payload;
      })
      .addCase(getPaymentStats.rejected, (state, action) => {
        state.statsLoading = false;
        state.statsError = action.payload;
      });
  }
});

// Export actions
export const {
  clearPaymentErrors,
  clearPaymentSuccess,
  setPaymentModalVisible,
  setRefundModalVisible,
  setSelectedPaymentId,
  updatePaymentFlow,
  resetPaymentFlow,
  addPaymentToHistory,
  clearCurrentPayment
} = paymentSlice.actions;

// Selectors
export const selectCurrentPayment = (state) => state.payment.currentPayment;
export const selectPaymentFormData = (state) => state.payment.paymentFormData;
export const selectPaymentStatus = (state) => state.payment.paymentStatus;
export const selectPayFastConfig = (state) => state.payment.payFastConfig;
export const selectPaymentHistory = (state) => state.payment.paymentHistory;
export const selectPaymentStats = (state) => state.payment.paymentStats;
export const selectPaymentFlow = (state) => state.payment.paymentFlow;

// Loading selectors
export const selectCreatePaymentLoading = (state) => state.payment.createPaymentLoading;
export const selectFormDataLoading = (state) => state.payment.formDataLoading;
export const selectStatusLoading = (state) => state.payment.statusLoading;
export const selectRefundLoading = (state) => state.payment.refundLoading;
export const selectConfigLoading = (state) => state.payment.configLoading;
export const selectPollingLoading = (state) => state.payment.pollingLoading;
export const selectHistoryLoading = (state) => state.payment.historyLoading;
export const selectStatsLoading = (state) => state.payment.statsLoading;

// Error selectors
export const selectCreatePaymentError = (state) => state.payment.createPaymentError;
export const selectFormDataError = (state) => state.payment.formDataError;
export const selectStatusError = (state) => state.payment.statusError;
export const selectRefundError = (state) => state.payment.refundError;
export const selectConfigError = (state) => state.payment.configError;
export const selectPollingError = (state) => state.payment.pollingError;
export const selectHistoryError = (state) => state.payment.historyError;
export const selectStatsError = (state) => state.payment.statsError;

// Success selectors
export const selectCreatePaymentSuccess = (state) => state.payment.createPaymentSuccess;
export const selectRefundSuccess = (state) => state.payment.refundSuccess;

// UI selectors
export const selectShowPaymentModal = (state) => state.payment.showPaymentModal;
export const selectShowRefundModal = (state) => state.payment.showRefundModal;
export const selectSelectedPaymentId = (state) => state.payment.selectedPaymentId;

export default paymentSlice.reducer;
