import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  fetchPublicEvents,
  fetchFeaturedEvents,
  registerForEvent,
  selectPublicEvents,
  selectPublicEventsLoading,
  selectFeaturedEvents,
  selectFeaturedEventsLoading,
  selectRegistrationLoading,
  selectRegistrationSuccess,
  selectRegistrationError
} from '../../store/slices/EventsSlice';
import EventFilters from '../../components/events/EventFilters';
import EventTabs from '../../components/events/EventTabs';
import PublicEventList from '../../components/events/PublicEventList';
import { useNotification } from '../../contexts/NotificationContext';

const EventsPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();
  
  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [filters, setFilters] = useState({
    category: null,
    status: null,
    type: null
  });

  // Redux state with robust error handling
  const rawPublicEvents = useSelector(selectPublicEvents);
  const publicEvents = Array.isArray(rawPublicEvents) ? rawPublicEvents : [];
  const publicEventsLoading = useSelector(selectPublicEventsLoading);
  const rawFeaturedEvents = useSelector(selectFeaturedEvents);
  const featuredEvents = Array.isArray(rawFeaturedEvents) ? rawFeaturedEvents : [];
  const featuredEventsLoading = useSelector(selectFeaturedEventsLoading);
  const registrationLoading = useSelector(selectRegistrationLoading);
  const registrationSuccess = useSelector(selectRegistrationSuccess);
  const registrationError = useSelector(selectRegistrationError);

  // Load initial data
  useEffect(() => {
    dispatch(fetchPublicEvents());
    dispatch(fetchFeaturedEvents({ limit: 6 }));
  }, [dispatch]);

  // Handle registration success/error
  useEffect(() => {
    if (registrationSuccess) {
      console.log('Registration successful!');
      showSuccess('Successfully registered for event!');
    }
  }, [registrationSuccess, showSuccess]);

  useEffect(() => {
    if (registrationError) {
      console.error('Registration failed:', registrationError);

      // Handle specific error cases in useEffect as well
      if (registrationError.status === 404 || registrationError.detail === 'Not Found') {
        showError('Event registration is not available yet. The registration system is currently being set up.');
      } else {
        showError(`Registration failed: ${registrationError.message || registrationError.detail || 'Please try again'}`);
      }
    }
  }, [registrationError, showError]);

  // Filter and search logic
  const getFilteredEvents = () => {
    let events = [];
    
    switch (activeTab) {
      case 'featured':
        events = featuredEvents;
        break;
      case 'competitions':
        events = publicEvents.filter(event => event.is_competition);
        break;
      default:
        events = publicEvents;
        break;
    }

    // Apply search filter
    if (searchQuery.trim()) {
      events = events.filter(event =>
        event.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply other filters
    if (filters.category) {
      events = events.filter(event => event.category === filters.category);
    }

    if (filters.status) {
      events = events.filter(event => event.status === filters.status);
    }

    if (filters.type) {
      if (filters.type === 'featured') {
        events = events.filter(event => event.is_featured);
      } else if (filters.type === 'competition') {
        events = events.filter(event => event.is_competition);
      }
    }

    return Array.isArray(events) ? events : [];
  };

  // Event handlers
  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      category: null,
      status: null,
      type: null
    });
    setSearchQuery('');
  };

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleToggleFilters = () => {
    setShowFilters(!showFilters);
  };

  const handleViewEventDetails = (event) => {
    navigate(`/events/${event.id}`);
  };

  const handleRegisterForEvent = async (event) => {
    try {
      console.log('Registering for event:', event);

      // Basic registration data - you can customize this based on your needs
      const registrationData = {
        event_id: event.id,
        user_notes: '', // Optional user notes
        special_requirements: '', // Optional special requirements
      };

      await dispatch(registerForEvent({
        eventId: event.id,
        registrationData
      })).unwrap();

      console.log('Successfully registered for event:', event.title);

    } catch (error) {
      console.error('Failed to register for event:', error);

      // Handle specific error cases
      if (error.status === 404 || error.detail === 'Not Found') {
        alert('Event registration is not available yet. The registration system is currently being set up.');
      } else if (error.status === 401) {
        alert('Please log in to register for events.');
      } else if (error.status === 403) {
        alert('You do not have permission to register for this event.');
      } else {
        alert(`Registration failed: ${error.message || error.detail || 'Please try again later'}`);
      }
    }
  };

  const handleReload = () => {
    dispatch(fetchPublicEvents());
    dispatch(fetchFeaturedEvents({ limit: 6 }));
  };

  // Calculate event counts for tabs
  const eventCounts = {
    all: publicEvents.length,
    featured: featuredEvents.length,
    competitions: publicEvents.filter(e => e.is_competition).length
  };

  const filteredEvents = getFilteredEvents();
  const isLoading = publicEventsLoading || featuredEventsLoading;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-semibold text-gray-900 mb-3">
            Events
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Browse workshops, conferences, competitions and networking events
          </p>
        </div>

        {/* Search and Filters */}
        <EventFilters
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          filters={filters}
          onFilterChange={handleFilterChange}
          showFilters={showFilters}
          onToggleFilters={handleToggleFilters}
          onClearFilters={handleClearFilters}
          onReload={handleReload}
          isLoading={isLoading}
        />

        {/* Tabs */}
        <EventTabs
          activeTab={activeTab}
          onTabChange={handleTabChange}
          eventCounts={eventCounts}
        />

        {/* Events List */}
        <PublicEventList
          events={filteredEvents}
          loading={isLoading}
          title={`${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Events`}
          onViewDetails={handleViewEventDetails}
          onRegister={handleRegisterForEvent}
        />
      </div>
    </div>
  );
};

export default EventsPage;
