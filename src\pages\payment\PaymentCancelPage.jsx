/**
 * PaymentCancelPage Component
 * 
 * Handles the cancel return flow from PayFast payments.
 * Provides options to retry payment or return to previous page.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiX,
  FiArrowLeft,
  FiRefreshCw,
  FiHome,
  FiCalendar,
  FiAlertCircle,
  FiInfo
} from 'react-icons/fi';
import { LoadingSpinner } from '../../components/ui';
import {
  getPaymentStatus,
  selectPaymentStatus,
  selectStatusLoading,
  clearPaymentErrors
} from '../../store/slices/PaymentSlice';

const PaymentCancelPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  
  // Get URL parameters
  const paymentId = searchParams.get('payment_id');
  const eventId = searchParams.get('event_id');
  const registrationId = searchParams.get('registration_id');
  
  // Local state
  const [isLoading, setIsLoading] = useState(true);
  const [cancellationReason, setCancellationReason] = useState('user_cancelled');

  // Redux selectors
  const paymentStatus = useSelector(selectPaymentStatus);
  const statusLoading = useSelector(selectStatusLoading);

  // Check payment status on mount
  useEffect(() => {
    const checkStatus = async () => {
      if (paymentId) {
        try {
          await dispatch(getPaymentStatus(paymentId)).unwrap();
        } catch (error) {
          console.error('Failed to get payment status:', error);
          setCancellationReason('payment_failed');
        }
      }
      setIsLoading(false);
    };

    checkStatus();
  }, [dispatch, paymentId]);

  // Determine cancellation reason based on payment status
  useEffect(() => {
    if (paymentStatus) {
      if (paymentStatus.status === 'failed') {
        setCancellationReason('payment_failed');
      } else if (paymentStatus.status === 'pending') {
        setCancellationReason('payment_timeout');
      } else {
        setCancellationReason('user_cancelled');
      }
    }
  }, [paymentStatus]);

  // Get cancellation info based on reason
  const getCancellationInfo = (reason) => {
    switch (reason) {
      case 'payment_failed':
        return {
          title: 'Payment Failed',
          description: 'Your payment could not be processed successfully.',
          icon: FiX,
          color: 'red',
          suggestions: [
            'Check your card details and try again',
            'Ensure you have sufficient funds',
            'Try using a different payment method',
            'Contact your bank if the issue persists'
          ]
        };
      case 'payment_timeout':
        return {
          title: 'Payment Timeout',
          description: 'The payment session expired before completion.',
          icon: FiAlertCircle,
          color: 'yellow',
          suggestions: [
            'Complete the payment process more quickly',
            'Ensure you have a stable internet connection',
            'Try the payment again'
          ]
        };
      default:
        return {
          title: 'Payment Cancelled',
          description: 'You cancelled the payment process.',
          icon: FiX,
          color: 'gray',
          suggestions: [
            'You can try the payment again',
            'Contact support if you need assistance',
            'Check our payment methods and requirements'
          ]
        };
    }
  };

  // Handle retry payment
  const handleRetryPayment = () => {
    if (eventId && registrationId) {
      const amount = paymentStatus?.amount || searchParams.get('amount');
      navigate(`/payment?event_id=${eventId}&registration_id=${registrationId}&amount=${amount}&type=event`);
    } else {
      navigate(-1); // Go back to previous page
    }
  };

  // Handle navigation
  const handleGoBack = () => {
    if (eventId) {
      navigate(`/events/${eventId}`);
    } else {
      navigate(-1);
    }
  };

  const handleGoToEvents = () => {
    navigate('/events');
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const cancellationInfo = getCancellationInfo(cancellationReason);
  const IconComponent = cancellationInfo.icon;

  if (isLoading || statusLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-600 mt-4">Checking payment status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleGoBack}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <FiArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Payment {cancellationInfo.title}
              </h1>
              <p className="text-sm text-gray-500">
                Your payment was not completed
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* Status Header */}
              <div className={`bg-${cancellationInfo.color}-50 border-b border-${cancellationInfo.color}-200 p-6`}>
                <div className="flex items-center space-x-4">
                  <div className={`p-3 bg-${cancellationInfo.color}-100 rounded-full`}>
                    <IconComponent className={`w-8 h-8 text-${cancellationInfo.color}-600`} />
                  </div>
                  <div>
                    <h2 className={`text-2xl font-bold text-${cancellationInfo.color}-800`}>
                      {cancellationInfo.title}
                    </h2>
                    <p className={`text-${cancellationInfo.color}-600 mt-1`}>
                      {cancellationInfo.description}
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6 space-y-6">
                {/* Payment Details */}
                {paymentStatus && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Payment Details
                    </h3>
                    
                    <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Payment ID:</span>
                        <span className="font-mono text-sm">
                          {paymentStatus.payment_id?.slice(-12)}
                        </span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-gray-600">Amount:</span>
                        <span className="font-semibold">
                          {paymentStatus.currency} {paymentStatus.amount?.toFixed(2)}
                        </span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-gray-600">Status:</span>
                        <span className={`font-semibold capitalize text-${cancellationInfo.color}-600`}>
                          {paymentStatus.status}
                        </span>
                      </div>
                      
                      {paymentStatus.failure_reason && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Reason:</span>
                          <span className="text-red-600">
                            {paymentStatus.failure_reason}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* What happened */}
                <div className={`bg-${cancellationInfo.color}-50 border border-${cancellationInfo.color}-200 rounded-lg p-4`}>
                  <h4 className={`font-semibold text-${cancellationInfo.color}-800 mb-2`}>
                    What happened?
                  </h4>
                  <p className={`text-${cancellationInfo.color}-700 text-sm`}>
                    {cancellationInfo.description}
                  </p>
                </div>

                {/* Suggestions */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                    <FiInfo className="w-5 h-5" />
                    <span>What you can do</span>
                  </h3>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <ul className="space-y-2">
                      {cancellationInfo.suggestions.map((suggestion, index) => (
                        <li key={index} className="flex items-start space-x-2 text-blue-700">
                          <span className="text-blue-500 mt-1">•</span>
                          <span className="text-sm">{suggestion}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 pt-4">
                  <button
                    onClick={handleRetryPayment}
                    className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
                  >
                    <FiRefreshCw className="w-4 h-4" />
                    <span>Try Payment Again</span>
                  </button>
                  
                  <button
                    onClick={handleGoBack}
                    className="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2"
                  >
                    <FiArrowLeft className="w-4 h-4" />
                    <span>Go Back</span>
                  </button>
                </div>

                {/* Additional Help */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-3">
                    Still having trouble?
                  </h4>
                  
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>• Check our payment FAQ for common issues</p>
                    <p>• Contact your bank or card issuer</p>
                    <p>• Try using a different browser or device</p>
                    <p>• Contact our support team for assistance</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Quick Actions
              </h3>
              
              <div className="space-y-3">
                <button
                  onClick={handleRetryPayment}
                  className="w-full flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <FiRefreshCw className="w-4 h-4" />
                  <span>Retry Payment</span>
                </button>
                
                <button
                  onClick={handleGoToEvents}
                  className="w-full flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <FiCalendar className="w-4 h-4" />
                  <span>Browse Events</span>
                </button>
                
                <button
                  onClick={handleGoHome}
                  className="w-full flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <FiHome className="w-4 h-4" />
                  <span>Go Home</span>
                </button>
              </div>
            </div>

            {/* Support */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-semibold text-red-800 mb-2">
                Need Help?
              </h4>
              <p className="text-sm text-red-700 mb-3">
                If you continue to experience payment issues, our support team can help.
              </p>
              <div className="space-y-1 text-sm text-red-600">
                <p>Email: <EMAIL></p>
                <p>Phone: +27 12 345 6789</p>
              </div>
            </div>

            {/* Payment Methods */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">
                Accepted Payment Methods
              </h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p>• Visa and Mastercard</p>
                <p>• EFT and Instant EFT</p>
                <p>• Credit and Debit Cards</p>
                <p>• Secure online banking</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentCancelPage;
