import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiUserPlus,
  FiUsers,
  FiMail,
  FiPhone,
  FiStar,
  FiCheck,
  FiX,
  FiEye,
  FiFilter,
  FiRefreshCw,
  FiUser<PERSON>heck,
  FiUserX
} from 'react-icons/fi';
import {
  fetchSentInvitations,
  fetchReceivedInvitations,
  sendMentorInvitation,
  respondToInvitation,
  selectSentInvitations,
  selectReceivedInvitations,
  selectSendInvitationState,
  selectRespondInvitationState,
  selectApplications,
  selectApplicationsLoading,
  selectApplicationsError,
  selectMentors,
  selectMentorsLoading,
  selectMentorsError,
  selectApproveLoading,
  selectApproveSuccess,
  selectRejectLoading,
  selectRejectSuccess,
  selectInviteLoading,
  selectInviteSuccess,
  selectTerminateLoading,
  selectTerminateSuccess,
  clearErrors,
  clearSuccessStates
} from '../../store/slices/InstituteMentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { useNavigate } from 'react-router-dom';

function InstituteMentors() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('received');
  const [filter, setFilter] = useState('all');

  // New collaboration selectors
  const sentInvitations = useSelector(selectSentInvitations);
  const receivedInvitations = useSelector(selectReceivedInvitations);
  const sendInvitationState = useSelector(selectSendInvitationState);
  const respondInvitationState = useSelector(selectRespondInvitationState);

  // Legacy compatibility
  const applications = { data: receivedInvitations.data || [], total: receivedInvitations.total || 0 };
  const applicationsLoading = receivedInvitations.loading;
  const applicationsError = receivedInvitations.error;
  const mentors = { data: sentInvitations.data || [], total: sentInvitations.total || 0 };
  const mentorsLoading = sentInvitations.loading;
  const mentorsError = sentInvitations.error;
  const approveLoading = respondInvitationState.loading;
  const approveSuccess = respondInvitationState.success;
  const rejectLoading = respondInvitationState.loading;
  const rejectSuccess = respondInvitationState.success;
  const inviteLoading = sendInvitationState.loading;
  const inviteSuccess = sendInvitationState.success;
  const terminateLoading = respondInvitationState.loading;
  const terminateSuccess = respondInvitationState.success;

  // Load data on component mount
  useEffect(() => {
    if (activeTab === 'received' || activeTab === 'applications') {
      dispatch(fetchReceivedInvitations({ page: 1, size: 20 }));
    } else if (activeTab === 'sent' || activeTab === 'mentors') {
      dispatch(fetchSentInvitations({ page: 1, size: 20 }));
    }
  }, [dispatch, activeTab]);

  // Handle success states
  useEffect(() => {
    if (approveSuccess || rejectSuccess || inviteSuccess || terminateSuccess) {
      dispatch(clearSuccessStates());
      // Refresh data
      if (activeTab === 'received') {
        dispatch(fetchReceivedInvitations({ page: 1, size: 20 }));
      } else if (activeTab === 'sent') {
        dispatch(fetchSentInvitations({ page: 1, size: 20 }));
      }
    }
  }, [approveSuccess, rejectSuccess, inviteSuccess, terminateSuccess, dispatch, activeTab]);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (activeTab === 'received' || activeTab === 'applications') {
        await dispatch(fetchReceivedInvitations({ page: 1, size: 20 }));
      } else if (activeTab === 'sent' || activeTab === 'mentors') {
        await dispatch(fetchSentInvitations({ page: 1, size: 20 }));
      }
    } finally {
      setRefreshing(false);
    }
  };

  // Handle approve invitation (accept)
  const handleApproveApplication = async (invitation) => {
    try {
      await dispatch(respondToInvitation({
        invitationId: invitation.id,
        response: { status: 'accept' }
      })).unwrap();

      alert('Invitation accepted successfully!');
      handleRefresh();
    } catch (error) {
      console.error('Failed to accept invitation:', error);
      alert('Failed to accept invitation. Please try again.');
    }
  };

  // Handle reject invitation
  const handleRejectApplication = async (invitation) => {
    const reason = prompt('Enter rejection reason:');
    if (!reason) return;

    try {
      await dispatch(respondToInvitation({
        invitationId: invitation.id,
        response: { status: 'reject', reason }
      })).unwrap();

      alert('Invitation rejected successfully!');
      handleRefresh();
    } catch (error) {
      console.error('Failed to reject invitation:', error);
      alert('Failed to reject invitation. Please try again.');
    }
  };

  // Handle terminate mentor (reject invitation)
  const handleTerminateMentor = async (invitation) => {
    const reason = prompt('Please provide a reason for termination (optional):');
    if (window.confirm('Are you sure you want to terminate this collaboration?')) {
      try {
        await dispatch(respondToInvitation({
          invitationId: invitation.id,
          response: { status: 'reject', reason: reason || "Institute decision" }
        })).unwrap();

        alert('Collaboration terminated successfully!');
        handleRefresh();
      } catch (error) {
        console.error('Failed to terminate collaboration:', error);
        alert('Failed to terminate collaboration. Please try again.');
      }
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      dispatch(clearErrors());
    };
  }, [dispatch]);

  const currentError = activeTab === 'applications' ? applicationsError : mentorsError;
  if (currentError) {
    return <ErrorMessage message={currentError} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Mentors</h1>
          <p className="text-gray-600">Manage mentor applications and active mentors</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <FiRefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={() => navigate('/institute/mentors/invite')}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiUserPlus className="h-4 w-4 mr-2" />
            Invite Mentor
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('received')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'received'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Received Invitations ({receivedInvitations.total || 0})
            </button>
            <button
              onClick={() => setActiveTab('sent')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'sent'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Sent Invitations ({sentInvitations.total || 0})
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'received' ? (
            // Applications Tab
            applicationsLoading ? (
              <div className="flex justify-center items-center h-64">
                <LoadingSpinner size="lg" />
              </div>
            ) : applications.data && applications.data.length > 0 ? (
              <div className="space-y-4">
                {applications.data.map((application) => (
                  <div key={application.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-lg font-medium text-gray-900">
                            {application.applicantName}
                          </h4>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(application.status)}`}>
                            {application.status}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                          <div className="flex items-center text-sm text-gray-600">
                            <FiMail className="h-4 w-4 mr-2" />
                            {application.applicantEmail}
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <FiUsers className="h-4 w-4 mr-2" />
                            {application.expertise?.join(', ') || 'No expertise listed'}
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-3">{application.applicationMessage}</p>
                        
                        <div className="text-xs text-gray-500">
                          Applied on {new Date(application.applicationDate).toLocaleDateString()}
                        </div>
                      </div>
                      
                      {application.status === 'pending' && (
                        <div className="flex space-x-2 ml-4">
                          <button
                            onClick={() => handleApproveApplication(application)}
                            disabled={approveLoading}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                          >
                            <FiCheck className="h-3 w-3 mr-1" />
                            Approve
                          </button>
                          <button
                            onClick={() => handleRejectApplication(application.id)}
                            disabled={rejectLoading}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                          >
                            <FiX className="h-3 w-3 mr-1" />
                            Reject
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <FiUsers className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No applications found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  New mentor applications will appear here.
                </p>
              </div>
            )
          ) : (
            // Mentors Tab
            mentorsLoading ? (
              <div className="flex justify-center items-center h-64">
                <LoadingSpinner size="lg" />
              </div>
            ) : mentors.data && mentors.data.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {mentors.data.map((mentor) => (
                  <div key={mentor.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="text-lg font-medium text-gray-900">
                        {mentor.firstName} {mentor.lastName}
                      </h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(mentor.status)}`}>
                        {mentor.status}
                      </span>
                    </div>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <FiMail className="h-4 w-4 mr-2" />
                        {mentor.email}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <FiUsers className="h-4 w-4 mr-2" />
                        {mentor.expertise?.join(', ') || 'No expertise listed'}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <FiStar className="h-4 w-4 mr-2" />
                        {mentor.averageRating || 'No rating'} rating
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <button
                        onClick={() => navigate(`/institute/mentors/${mentor.id}`)}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        <FiEye className="h-4 w-4 inline mr-1" />
                        View Details
                      </button>
                      
                      <div className="flex space-x-2">
                        {mentor.associationStatus === 'active' && (
                          <button
                            onClick={() => handleTerminateMentor(mentor)}
                            className="text-red-600 hover:text-red-800"
                            title="Terminate Association"
                            disabled={terminateLoading}
                          >
                            <FiUserX className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <FiUsers className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No mentors found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Approved mentors will appear here.
                </p>
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
}

export default InstituteMentors;
