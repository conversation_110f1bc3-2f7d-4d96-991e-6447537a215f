import {
  HomeIcon,
  UserIcon,
  Cog6ToothIcon,
  BookOpenIcon,
  UsersIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  AcademicCapIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  UserGroupIcon,
  ChartBarIcon,
  CreditCardIcon,
  CogIcon,
  BellIcon,
  EnvelopeIcon,
  SparklesIcon,
  ClipboardDocumentCheckIcon,
  CalendarDaysIcon,
  TrophyIcon,
  UserPlusIcon,
  StarIcon
} from "@heroicons/react/24/outline";

export const sidebarConfigs = {
  admin: [
    { label: "Dashboard", icon: HomeIcon, path: "/admin/dashboard" },
    { label: "Users", icon: UsersIcon, path: "/admin/users" },
    { label: "Institute Approvals", icon: BuildingOfficeIcon, path: "/admin/institute-approvals" },
    { label: "Events", icon: CalendarDaysIcon, path: "/admin/events" },
    { label: "Payment Management", icon: CreditCardIcon, path: "/admin/payment-management" },
    {
      label: "Config",
      icon: DocumentTextIcon,
      children: [
        { label: "Material", icon: DocumentTextIcon, path: "/admin/material" },
      ],
    },
    { label: "Plans", icon: CreditCardIcon, path: "/admin/plans" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/admin/settings" },
  ],
  student: [
    { label: "Dashboard", icon: HomeIcon, path: "/student/dashboard" },
    { label: "Classes", icon: BookOpenIcon, path: "/student/classes" },
    { label: "Tasks", icon: ClipboardDocumentCheckIcon, path: "/student/tasks" },
    { label: "Exams", icon: AcademicCapIcon, path: "/student/exams" },
    { label: "Events", icon: CalendarDaysIcon, path: "/student/events" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/student/settings" },
  ],
  teacher: [
    { label: "Dashboard", icon: HomeIcon, path: "/teacher/dashboard" },
    { label: "My Classes", icon: AcademicCapIcon, path: "/teacher/classes" },
    { label: "Tasks", icon: ClipboardDocumentCheckIcon, path: "/teacher/tasks" },
    { label: "Exams", icon: AcademicCapIcon, path: "/teacher/exams" },
    { label: "Events", icon: CalendarDaysIcon, path: "/teacher/events" },
    { label: "Competitions", icon: TrophyIcon, path: "/teacher/competitions" },
    { label: "Mentorship", icon: UserPlusIcon, path: "/teacher/mentorship" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/teacher/settings" },
  ],
  mentor: [
    { label: "Dashboard", icon: HomeIcon, path: "/mentor/dashboard" },
    { label: "Collaborations", icon: UsersIcon, path: "/mentor/collaborations" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/mentor/settings" },
  ],
  institute: [
    { label: "Dashboard", icon: HomeIcon, path: "/institute/dashboard" },
    { label: "Events", icon: CalendarDaysIcon, path: "/institute/events" },
    { label: "Mentors", icon: UserPlusIcon, path: "/institute/mentors" },
    { label: "Collaborations", icon: UsersIcon, path: "/institute/collaborations" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/institute/settings" },
  ],
  sponsor: [
    { label: "Dashboard", icon: HomeIcon, path: "/sponsor/dashboard" },
    { label: "Institutes", icon: BuildingOfficeIcon, path: "/sponsor/institutes" },
    { label: "Funding", icon: CurrencyDollarIcon, path: "/sponsor/funding" },
    { label: "Reports", icon: ChartBarIcon, path: "/sponsor/reports" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/sponsor/settings" },
  ],
}; 