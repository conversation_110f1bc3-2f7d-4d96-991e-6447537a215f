import React, { useEffect, useState } from 'react';
import {
  Routes,
  Route,
  useLocation,
  Navigate
} from 'react-router-dom';
import axios from "axios";
import API_BASE_URL from './utils/api/API_URL';
import { UXProvider } from './providers/UXProvider';
import { NotificationProvider } from './contexts/NotificationContext';
import logger from './utils/helpers/logger';
import { handleApiError } from './utils/helpers/errorHandler';

import './css/style.css';

import './components/charts/ChartjsConfig';

// Import pages
import Home from './pages/common/LandingPage';
import Login from './pages/auth/Login';
import Signup from './pages/auth/Signup';
import SignupMenu from './pages/auth/SignupMenu';
import Unauthorized from './pages/auth/Unauthorized';
import EmailVerification from './pages/auth/EmailVerification';
import DashboardLayout from './pages/dashboard/DashboardLayout';
import ExamFormTest from './components/test/ExamFormTest';
import StudentAssignmentTest from './pages/test/StudentAssignmentTest';
import EmailVerificationDemo from './pages/test/EmailVerificationDemo';
import PublicEventDetailsPage from './pages/events/PublicEventDetailsPage';
import PaymentSuccess from './pages/payment/PaymentSuccess';
import PaymentCancel from './pages/payment/PaymentCancel';
import UserPaymentHistory from './pages/payment/UserPaymentHistory';
import PaymentSystemTest from './pages/test/PaymentSystemTest';

// Payment pages
import {
  PaymentPage,
  PaymentSuccessPage,
  PaymentCancelPage,
  PaymentStatusPage
} from './pages/payment';

// Authentication check component
const AuthCheck = ({ children }) => {
  const [isChecking, setIsChecking] = useState(true);
  const [shouldRedirect, setShouldRedirect] = useState(false);
  const [redirectPath, setRedirectPath] = useState('');

  useEffect(() => {
    let didCancel = false;
    
    const validateToken = async () => {
      const token = localStorage.getItem('token');
      const role = localStorage.getItem('role');

      if (!token || !role) {
        // No token or role found, user should stay on landing page
        logger.debug('No token or role found, staying on landing page', null, 'AuthCheck');
        if (!didCancel) setIsChecking(false);
        return;
      }

      try {
        // Validate token by making a /me request with Authorization header
        const response = await axios.get(`${API_BASE_URL}/api/users/me`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        // Token is valid, redirect to dashboard
        const rolePath = role.toLowerCase();
        logger.info(`Token valid, redirecting to /${rolePath}/dashboard`, { role: rolePath }, 'AuthCheck');
        if (!didCancel) {
          setRedirectPath(`/${rolePath}/dashboard`);
          setShouldRedirect(true);
          setIsChecking(false);
        }
      } catch (error) {
        const errorData = handleApiError(error, 'AuthCheck');
        logger.warn('Token validation error', errorData, 'AuthCheck');

        if (error.response?.status === 403 || error.response?.status === 401) {
          // Token is invalid, clear auth data and stay on landing page
          logger.info('Token invalid, clearing auth data', null, 'AuthCheck');
          localStorage.removeItem('token');
          localStorage.removeItem('role');
          localStorage.removeItem('userdata');
        } else if (error.code === 'NETWORK_ERROR' || !error.response) {
          // Network error, stay on landing page but don't clear token
          logger.warn('Network error during token validation, staying on landing page', null, 'AuthCheck');
        } else {
          // Other error, stay on landing page
          logger.warn('Token validation failed, staying on landing page', errorData, 'AuthCheck');
        }
        
        if (!didCancel) setIsChecking(false);
      }
    };

    validateToken();

    return () => {
      didCancel = true;
    };
  }, []);

  if (isChecking) {
    // Show loading state while checking authentication
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (shouldRedirect) {
    return <Navigate to={redirectPath} replace />;
  }

  return children;
};

function App() {

  const location = useLocation();

  useEffect(() => {
    document.querySelector('html').style.scrollBehavior = 'auto'
    window.scroll({ top: 0 })
    document.querySelector('html').style.scrollBehavior = ''
  }, [location.pathname]); // triggered on route change

  return (
    <UXProvider>
      <NotificationProvider>
        <Routes>
        {/* Landing page with authentication check */}
        <Route exact path="/" element={
          <AuthCheck>
            <Home/>
          </AuthCheck>
        }/>

        {/* Auth Routes */}
        <Route exact path="/Login" element={<Login/>} />
        <Route exact path="/Signup-Menu" element={<SignupMenu/>} />
        <Route exact path="/signup/:role" element={<Signup/>} />
        <Route exact path="/verify-email" element={<EmailVerification/>} />
        <Route exact path="/unauthorized" element={<Unauthorized/>} />

        {/* Payment Routes */}
        <Route exact path="/payment/:eventId" element={<PaymentPage/>} />
        <Route exact path="/payment/success" element={<PaymentSuccess/>} />
        <Route exact path="/payment/cancel" element={<PaymentCancel/>} />
        <Route exact path="/payment/history" element={<UserPaymentHistory/>} />
        <Route exact path="/payment/status/:paymentId" element={<PaymentStatusPage/>} />
        <Route exact path="/payment/status" element={<PaymentStatusPage/>} />

        {/* Public Events Routes */}
        <Route exact path="/events/:eventId" element={<PublicEventDetailsPage/>} />

        {/* Test Routes */}
        <Route exact path="/test-exam-form" element={<ExamFormTest/>} />
        <Route exact path="/test-student-assignment" element={<StudentAssignmentTest/>} />
        <Route exact path="/test-email-verification" element={<EmailVerificationDemo/>} />
        <Route exact path="/test-payment-system" element={<PaymentSystemTest/>} />

        {/* Dashboard Routes - All handled by DashboardLayout */}
        <Route path="/admin/*" element={<DashboardLayout />} />
        <Route path="/student/*" element={<DashboardLayout />} />
        <Route path="/teacher/*" element={<DashboardLayout />} />
        <Route path="/institute/*" element={<DashboardLayout />} />
        <Route path="/sponsor/*" element={<DashboardLayout />} />
        <Route path="/mentor/*" element={<DashboardLayout />} />

        {/* Legacy routes with capital letters - redirect to lowercase */}
        <Route path="/Admin" element={<DashboardLayout />} />
        <Route path="/Student" element={<DashboardLayout />} />
        <Route path="/Teacher" element={<DashboardLayout />} />
        <Route path="/Institute" element={<DashboardLayout />} />
        <Route path="/Sponsor" element={<DashboardLayout />} />
        <Route path="/Mentor" element={<DashboardLayout />} />
        </Routes>
      </NotificationProvider>
    </UXProvider>
  );
}

export default App;
