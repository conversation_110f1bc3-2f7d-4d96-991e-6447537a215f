import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  FiX,
  FiCalendar,
  FiMapPin,
  FiDollarSign,
  FiUser,
  FiMail,
  FiPhone,
  FiInfo,
  FiCreditCard,
  FiCheck,
  FiMinus,
  FiPlus,
  FiTag,
  FiUsers,
  FiClock,
  FiArrowLeft,
  FiArrowRight
} from 'react-icons/fi';
import { format } from 'date-fns';

const EventRegistrationModal = ({ isOpen, onClose, event }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Local state
  const [step, setStep] = useState('tickets'); // 'tickets', 'details', 'payment', 'confirmation'
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    additional_info: '',
    dietary_requirements: '',
    emergency_contact: {
      name: '',
      phone: '',
      relationship: ''
    },
    user_email: '',
    user_name: ''
  });
  const [formErrors, setFormErrors] = useState({});

  // Use event tickets or fallback to mock data
  const eventTickets = event?.tickets || [
    {
      id: 'ticket-1',
      name: 'General Admission',
      description: 'Standard access to the event',
      price: 0,
      currency: 'ZAR',
      max_quantity_per_order: 5,
      available_quantity: 100,
      status: 'ACTIVE'
    },
    {
      id: 'ticket-2',
      name: 'VIP Access',
      description: 'Premium access with exclusive benefits',
      price: 299.99,
      currency: 'ZAR',
      max_quantity_per_order: 2,
      available_quantity: 20,
      status: 'ACTIVE'
    },
    {
      id: 'ticket-3',
      name: 'Student Discount',
      description: 'Special pricing for students (ID required)',
      price: 149.99,
      currency: 'ZAR',
      max_quantity_per_order: 1,
      available_quantity: 50,
      status: 'ACTIVE'
    }
  ];

  // Initialize form with user data
  useEffect(() => {
    if (isOpen) {
      const userData = JSON.parse(localStorage.getItem('userdata') || '{}');
      setFormData(prev => ({
        ...prev,
        user_email: userData.email || '',
        user_name: `${userData.first_name || ''} ${userData.last_name || ''}`.trim()
      }));
      setStep('tickets');
      setSelectedTicket(null);
      setQuantity(1);
      setFormErrors({});
    }
  }, [isOpen]);

  // Handle ticket selection
  const handleTicketSelect = (ticket) => {
    setSelectedTicket(ticket);
    setQuantity(1);
  };

  // Handle quantity change
  const handleQuantityChange = (change) => {
    if (!selectedTicket) return;
    
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= selectedTicket.max_quantity_per_order) {
      setQuantity(newQuantity);
    }
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const errors = {};
    
    if (!formData.user_name.trim()) {
      errors.user_name = 'Full name is required';
    }
    
    if (!formData.user_email.trim()) {
      errors.user_email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.user_email)) {
      errors.user_email = 'Please enter a valid email';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle next step
  const handleNextStep = () => {
    if (step === 'tickets' && selectedTicket) {
      setStep('details');
    } else if (step === 'details') {
      if (validateForm()) {
        if (selectedTicket.price > 0) {
          setStep('payment');
        } else {
          handleSubmit();
        }
      }
    }
  };

  // Handle previous step
  const handlePrevStep = () => {
    if (step === 'details') {
      setStep('tickets');
    } else if (step === 'payment') {
      setStep('details');
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock registration data
      const registrationData = {
        event_id: event.id,
        ticket_id: selectedTicket.id,
        quantity: quantity,
        total_amount: selectedTicket.price * quantity,
        currency: selectedTicket.currency,
        user_details: formData,
        registration_number: `REG-${Date.now()}`,
        status: 'confirmed'
      };

      console.log('Registration successful:', registrationData);
      setStep('confirmation');
      
    } catch (error) {
      console.error('Registration failed:', error);
      alert('Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle close
  const handleClose = () => {
    if (!loading) {
      setStep('tickets');
      setSelectedTicket(null);
      setQuantity(1);
      setFormData({
        additional_info: '',
        dietary_requirements: '',
        emergency_contact: { name: '', phone: '', relationship: '' },
        user_email: '',
        user_name: ''
      });
      setFormErrors({});
      onClose();
    }
  };

  // Format currency
  const formatCurrency = (amount, currency = 'ZAR') => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {step === 'confirmation' ? 'Registration Confirmed!' : 'Register for Event'}
            </h2>
            <p className="text-sm text-gray-500 mt-1">{event?.title}</p>
          </div>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <FiX className="h-6 w-6" />
          </button>
        </div>

        {/* Progress Steps */}
        {step !== 'confirmation' && (
          <div className="px-6 py-4 border-b bg-gray-50">
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 ${step === 'tickets' ? 'text-blue-600' : 'text-gray-500'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                  step === 'tickets' ? 'bg-blue-600 text-white' : 'bg-gray-200'
                }`}>
                  1
                </div>
                <span className="text-sm font-medium">Select Tickets</span>
              </div>
              <div className="flex-1 h-px bg-gray-200"></div>
              <div className={`flex items-center space-x-2 ${step === 'details' ? 'text-blue-600' : 'text-gray-500'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                  step === 'details' ? 'bg-blue-600 text-white' : 'bg-gray-200'
                }`}>
                  2
                </div>
                <span className="text-sm font-medium">Details</span>
              </div>
              {selectedTicket?.price > 0 && (
                <>
                  <div className="flex-1 h-px bg-gray-200"></div>
                  <div className={`flex items-center space-x-2 ${step === 'payment' ? 'text-blue-600' : 'text-gray-500'}`}>
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                      step === 'payment' ? 'bg-blue-600 text-white' : 'bg-gray-200'
                    }`}>
                      3
                    </div>
                    <span className="text-sm font-medium">Payment</span>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6">
          {step === 'tickets' && (
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select Ticket Type</h3>
                <p className="text-gray-600">Choose your preferred ticket option</p>
              </div>

              <div className="space-y-4">
                {eventTickets.map((ticket) => (
                  <div
                    key={ticket.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedTicket?.id === ticket.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleTicketSelect(ticket)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <FiTag className="h-5 w-5 text-gray-500" />
                          <div>
                            <h4 className="font-medium text-gray-900">{ticket.name}</h4>
                            <p className="text-sm text-gray-600">{ticket.description}</p>
                          </div>
                        </div>
                        <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                          <span className="flex items-center">
                            <FiUsers className="h-4 w-4 mr-1" />
                            {ticket.available_quantity} available
                          </span>
                          <span>Max {ticket.max_quantity_per_order} per order</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-semibold text-gray-900">
                          {ticket.price === 0 ? 'Free' : formatCurrency(ticket.price, ticket.currency)}
                        </div>
                        {selectedTicket?.id === ticket.id && (
                          <div className="flex items-center space-x-2 mt-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleQuantityChange(-1);
                              }}
                              className="p-1 rounded border border-gray-300 hover:bg-gray-50"
                              disabled={quantity <= 1}
                            >
                              <FiMinus className="h-3 w-3" />
                            </button>
                            <span className="px-3 py-1 bg-white border border-gray-300 rounded text-sm">
                              {quantity}
                            </span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleQuantityChange(1);
                              }}
                              className="p-1 rounded border border-gray-300 hover:bg-gray-50"
                              disabled={quantity >= ticket.max_quantity_per_order}
                            >
                              <FiPlus className="h-3 w-3" />
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {selectedTicket && (
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">Total</h4>
                      <p className="text-sm text-gray-600">
                        {quantity} × {selectedTicket.name}
                      </p>
                    </div>
                    <div className="text-xl font-semibold text-gray-900">
                      {selectedTicket.price === 0
                        ? 'Free'
                        : formatCurrency(selectedTicket.price * quantity, selectedTicket.currency)
                      }
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {step === 'details' && (
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Registration Details</h3>
                <p className="text-gray-600">Please provide your information</p>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      name="user_name"
                      value={formData.user_name}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        formErrors.user_name ? 'border-red-300' : 'border-gray-300'
                      }`}
                      required
                    />
                    {formErrors.user_name && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.user_name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      name="user_email"
                      value={formData.user_email}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        formErrors.user_email ? 'border-red-300' : 'border-gray-300'
                      }`}
                      required
                    />
                    {formErrors.user_email && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.user_email}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Additional Information
                  </label>
                  <textarea
                    name="additional_info"
                    value={formData.additional_info}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Any special requirements or notes..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Dietary Requirements
                  </label>
                  <input
                    type="text"
                    name="dietary_requirements"
                    value={formData.dietary_requirements}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., Vegetarian, Vegan, Allergies..."
                  />
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">Emergency Contact</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Contact Name
                      </label>
                      <input
                        type="text"
                        name="emergency_contact.name"
                        value={formData.emergency_contact.name}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Contact Phone
                      </label>
                      <input
                        type="tel"
                        name="emergency_contact.phone"
                        value={formData.emergency_contact.phone}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {step === 'payment' && (
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Payment Information</h3>
                <p className="text-gray-600">Complete your registration with payment</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Order Summary</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>{selectedTicket?.name} × {quantity}</span>
                    <span>{formatCurrency(selectedTicket?.price * quantity, selectedTicket?.currency)}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between font-semibold">
                    <span>Total</span>
                    <span>{formatCurrency(selectedTicket?.price * quantity, selectedTicket?.currency)}</span>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <FiInfo className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-blue-800 font-medium">Payment Integration Coming Soon</p>
                    <p className="text-blue-700 text-sm">
                      Payment processing will be integrated once the backend API is ready.
                      For now, this will complete the registration as a free event.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {step === 'confirmation' && (
            <div className="text-center">
              <div className="mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FiCheck className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Registration Successful!</h3>
                <p className="text-gray-600">
                  You have successfully registered for {event?.title}
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                <h4 className="font-medium text-gray-900 mb-3">Registration Details</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Registration Number:</span>
                    <span className="font-medium">REG-{Date.now()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Ticket Type:</span>
                    <span className="font-medium">{selectedTicket?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Quantity:</span>
                    <span className="font-medium">{quantity}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Amount:</span>
                    <span className="font-medium">
                      {selectedTicket?.price === 0
                        ? 'Free'
                        : formatCurrency(selectedTicket?.price * quantity, selectedTicket?.currency)
                      }
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <p className="text-blue-800 text-sm">
                  A confirmation email will be sent to {formData.user_email} with your registration details and event information.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {step !== 'confirmation' && (
          <div className="flex items-center justify-between p-6 border-t bg-gray-50">
            <button
              onClick={handlePrevStep}
              disabled={step === 'tickets' || loading}
              className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FiArrowLeft className="h-4 w-4" />
              <span>Previous</span>
            </button>

            <div className="flex items-center space-x-3">
              <button
                onClick={handleClose}
                disabled={loading}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={step === 'payment' ? handleSubmit : handleNextStep}
                disabled={
                  loading ||
                  (step === 'tickets' && !selectedTicket) ||
                  (step === 'details' && (!formData.user_name || !formData.user_email))
                }
                className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <span>
                      {step === 'tickets' ? 'Continue' :
                       step === 'details' ? (selectedTicket?.price > 0 ? 'Proceed to Payment' : 'Complete Registration') :
                       'Complete Registration'}
                    </span>
                    <FiArrowRight className="h-4 w-4" />
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {step === 'confirmation' && (
          <div className="p-6 border-t bg-gray-50">
            <button
              onClick={handleClose}
              className="w-full px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Close
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default EventRegistrationModal;
