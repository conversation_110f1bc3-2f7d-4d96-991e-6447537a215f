import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiPlus,
  FiRefreshCw,
  FiAward
} from 'react-icons/fi';

const EventActions = ({ 
  onRefresh, 
  refreshing = false 
}) => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
      {/* Header Info */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Event Management</h1>
        <p className="text-gray-600 mt-1">
          Manage all your institute events including workshops, conferences, webinars, and competitions
        </p>
        <div className="mt-2 flex items-center text-sm text-blue-600">
          <FiAward className="h-4 w-4 mr-1" />
          <span>Competitions are managed here as event categories</span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3">
        <button
          onClick={onRefresh}
          disabled={refreshing}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm 
                   text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 
                   focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 
                   disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <FiRefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </button>
        <button
          onClick={() => navigate('/institute/events/create')}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm 
                   text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 
                   focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                   transition-colors"
        >
          <FiPlus className="h-4 w-4 mr-2" />
          Create Event
        </button>
      </div>
    </div>
  );
};

export default EventActions;
