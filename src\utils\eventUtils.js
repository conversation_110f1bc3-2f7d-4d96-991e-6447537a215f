// Event category color mapping - Only 4 categories supported
export const getCategoryColor = (category) => {
  const colors = {
    workshop: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white',
    conference: 'bg-gradient-to-r from-purple-500 to-purple-600 text-white',
    webinar: 'bg-gradient-to-r from-green-500 to-green-600 text-white',
    competition: 'bg-gradient-to-r from-orange-500 to-orange-600 text-white'
  };

  return colors[category?.toLowerCase()] || colors.workshop; // Default to workshop
};

// Event status badge color mapping
export const getStatusBadgeColor = (status) => {
  const colors = {
    published: 'bg-gradient-to-r from-green-500 to-green-600 text-white',
    draft: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white',
    cancelled: 'bg-gradient-to-r from-red-500 to-red-600 text-white',
    completed: 'bg-gradient-to-r from-gray-500 to-gray-600 text-white',
    ongoing: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
  };

  return colors[status?.toLowerCase()] || colors.draft;
};

// Filter events based on search term and filters
export const filterEvents = (events, { searchTerm, category, status, date }) => {
  return events.filter(event => {
    // Search filter
    const matchesSearch = !searchTerm ||
      event.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.location?.name?.toLowerCase().includes(searchTerm.toLowerCase());

    // Category filter
    const matchesCategory = category === 'all' || event.category?.toLowerCase() === category;
    
    // Status filter
    const matchesStatus = status === 'all' || event.status?.toLowerCase() === status;

    // Date filter
    let matchesDate = true;
    if (date !== 'all') {
      const eventDate = new Date(event.start_datetime);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

      switch (date) {
        case 'today':
          matchesDate = eventDate >= today && eventDate < tomorrow;
          break;
        case 'tomorrow':
          matchesDate = eventDate >= tomorrow && eventDate < new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000);
          break;
        case 'this_week':
          matchesDate = eventDate >= today && eventDate < nextWeek;
          break;
        case 'upcoming':
          matchesDate = eventDate >= today;
          break;
        default:
          matchesDate = true;
      }
    }

    return matchesSearch && matchesCategory && matchesStatus && matchesDate;
  });
};

// Get filter options for dropdowns - Only 4 categories supported
export const getFilterOptions = () => ({
  categories: [
    { value: 'all', label: 'All Categories' },
    { value: 'workshop', label: 'Workshops' },
    { value: 'conference', label: 'Conferences' },
    { value: 'webinar', label: 'Webinars' },
    { value: 'competition', label: 'Competitions' }
  ],
  statuses: [
    { value: 'all', label: 'All Status' },
    { value: 'published', label: 'Published' },
    { value: 'draft', label: 'Draft' },
    { value: 'cancelled', label: 'Cancelled' }
  ],
  dates: [
    { value: 'all', label: 'All Dates' },
    { value: 'today', label: 'Today' },
    { value: 'tomorrow', label: 'Tomorrow' },
    { value: 'this_week', label: 'This Week' },
    { value: 'upcoming', label: 'Upcoming' }
  ]
});
