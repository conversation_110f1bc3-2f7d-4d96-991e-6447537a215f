import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiClock,
  FiArrowLeft,
  FiStar,
  FiAward,
  FiExternalLink,
  FiShare2
} from 'react-icons/fi';
import {
  fetchEventDetails,
  registerForEvent,
  selectRegistrationLoading,
  selectRegistrationSuccess,
  selectRegistrationError
} from '../../store/slices/EventsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { useNotification } from '../../contexts/NotificationContext';

const PublicEventDetailsPage = () => {
  const { eventId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { showSuccess, showError } = useNotification();
  
  const [event, setEvent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Redux selectors for registration
  const registrationLoading = useSelector(selectRegistrationLoading);
  const registrationSuccess = useSelector(selectRegistrationSuccess);
  const registrationError = useSelector(selectRegistrationError);

  // Helper function to validate UUID
  const isValidUUID = (str) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  };

  // Load event details
  useEffect(() => {
    const loadEvent = async () => {
      if (!eventId || !isValidUUID(eventId)) {
        setError('Invalid event ID');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await dispatch(fetchEventDetails(eventId)).unwrap();
        setEvent(response);
        setError(null);
      } catch (err) {
        setError(err.message || 'Failed to load event details');
        setEvent(null);
      } finally {
        setLoading(false);
      }
    };

    loadEvent();
  }, [dispatch, eventId]);

  // Handle registration success/error
  useEffect(() => {
    if (registrationSuccess) {
      console.log('Registration successful!');
      showSuccess(`Successfully registered for "${event?.title}"!`);
    }
  }, [registrationSuccess, event?.title, showSuccess]);

  useEffect(() => {
    if (registrationError) {
      console.error('Registration failed:', registrationError);

      // Handle specific error cases in useEffect as well
      if (registrationError.status === 404 || registrationError.detail === 'Not Found') {
        showError('Event registration is not available yet. The registration system is currently being set up.');
      } else {
        showError(`Registration failed: ${registrationError.message || registrationError.detail || 'Please try again'}`);
      }
    }
  }, [registrationError, showError]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCategoryColor = (category) => {
    const colors = {
      WORKSHOP: 'bg-slate-100 text-slate-700 border-slate-200',
      CONFERENCE: 'bg-gray-100 text-gray-700 border-gray-200',
      WEBINAR: 'bg-neutral-100 text-neutral-700 border-neutral-200',
      COMPETITION: 'bg-stone-100 text-stone-700 border-stone-200',
      SEMINAR: 'bg-zinc-100 text-zinc-700 border-zinc-200'
    };
    return colors[category] || 'bg-gray-100 text-gray-700 border-gray-200';
  };

  const handleRegister = async () => {
    if (!event) return;

    try {
      console.log('Registering for event:', event);

      // Basic registration data
      const registrationData = {
        event_id: event.id,
        user_notes: '',
        special_requirements: '',
      };

      await dispatch(registerForEvent({
        eventId: event.id,
        registrationData
      })).unwrap();

      console.log('Successfully registered for event:', event.title);

    } catch (error) {
      console.error('Failed to register for event:', error);

      // Handle specific error cases
      if (error.status === 404 || error.detail === 'Not Found') {
        alert('Event registration is not available yet. The registration system is currently being set up.');
      } else if (error.status === 401) {
        alert('Please log in to register for events.');
      } else if (error.status === 403) {
        alert('You do not have permission to register for this event.');
      } else {
        alert(`Registration failed: ${error.message || error.detail || 'Please try again later'}`);
      }
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: event.title,
        text: event.short_description,
        url: window.location.href
      });
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      alert('Event link copied to clipboard!');
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Event</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => navigate('/events')}
            className="text-gray-600 hover:text-gray-800"
          >
            Back to Events
          </button>
        </div>
      </div>
    );
  }

  // Event not found
  if (!event) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Event not found</h2>
          <p className="text-gray-600 mb-4">The event you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/events')}
            className="text-gray-600 hover:text-gray-800"
          >
            Back to Events
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <button
          onClick={() => navigate(-1)}
          className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-6"
        >
          <FiArrowLeft className="h-4 w-4 mr-2" />
          Back
        </button>

        {/* Event Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-6">
          {/* Banner Image */}
          {event.banner_image_url && (
            <div className="h-64 bg-gray-200 overflow-hidden">
              <img
                src={event.banner_image_url}
                alt={event.title}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          <div className="p-6">
            {/* Header Info */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-2">
                <span className={`px-3 py-1 rounded text-sm font-medium border ${getCategoryColor(event.category)}`}>
                  {event.category}
                </span>
                {event.is_featured && (
                  <FiStar className="h-5 w-5 text-gray-500" />
                )}
                {event.is_competition && (
                  <FiAward className="h-5 w-5 text-gray-500" />
                )}
              </div>
              <button
                onClick={handleShare}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                <FiShare2 className="h-5 w-5" />
              </button>
            </div>

            {/* Title */}
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{event.title}</h1>

            {/* Short Description */}
            {event.short_description && (
              <p className="text-lg text-gray-600 mb-6">{event.short_description}</p>
            )}

            {/* Event Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="flex items-center text-gray-700">
                <FiCalendar className="h-5 w-5 mr-3 text-gray-500" />
                <div>
                  <p className="font-medium">Date</p>
                  <p className="text-sm text-gray-600">{formatDate(event.start_datetime)}</p>
                </div>
              </div>

              <div className="flex items-center text-gray-700">
                <FiClock className="h-5 w-5 mr-3 text-gray-500" />
                <div>
                  <p className="font-medium">Time</p>
                  <p className="text-sm text-gray-600">
                    {formatTime(event.start_datetime)} - {formatTime(event.end_datetime)}
                  </p>
                </div>
              </div>

              {event.location && (
                <div className="flex items-center text-gray-700">
                  <FiMapPin className="h-5 w-5 mr-3 text-gray-500" />
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="text-sm text-gray-600">{event.location}</p>
                  </div>
                </div>
              )}

              {event.max_attendees && (
                <div className="flex items-center text-gray-700">
                  <FiUsers className="h-5 w-5 mr-3 text-gray-500" />
                  <div>
                    <p className="font-medium">Capacity</p>
                    <p className="text-sm text-gray-600">Max {event.max_attendees} attendees</p>
                  </div>
                </div>
              )}
            </div>

            {/* Register Button */}
            <button
              onClick={handleRegister}
              disabled={registrationLoading}
              className={`w-full md:w-auto px-8 py-3 rounded-lg font-medium transition-colors duration-200 ${
                registrationLoading
                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                  : 'bg-gray-900 text-white hover:bg-gray-800'
              }`}
            >
              {registrationLoading ? 'Registering...' : 'Register for Event'}
            </button>
          </div>
        </div>

        {/* Event Description */}
        {event.description && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">About This Event</h2>
            <div className="prose max-w-none text-gray-700">
              {event.description.split('\n').map((paragraph, index) => (
                <p key={index} className="mb-4">{paragraph}</p>
              ))}
            </div>
          </div>
        )}

        {/* Requirements */}
        {event.requirements && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Requirements</h2>
            <p className="text-gray-700">{event.requirements}</p>
          </div>
        )}

        {/* Competition Rules */}
        {event.is_competition && event.competition_rules && (
          <div className="bg-gray-50 rounded-xl border border-gray-200 p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Competition Rules</h2>
            <div className="prose max-w-none text-gray-700">
              {event.competition_rules.split('\n').map((rule, index) => (
                <p key={index} className="mb-2">{rule}</p>
              ))}
            </div>
          </div>
        )}

        {/* External Links */}
        {event.external_links && Object.keys(event.external_links).length > 0 && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Additional Resources</h2>
            <div className="space-y-2">
              {Object.entries(event.external_links).map(([key, url]) => (
                <a
                  key={key}
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-gray-600 hover:text-gray-800"
                >
                  <FiExternalLink className="h-4 w-4 mr-2" />
                  {key.charAt(0).toUpperCase() + key.slice(1)}
                </a>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PublicEventDetailsPage;
