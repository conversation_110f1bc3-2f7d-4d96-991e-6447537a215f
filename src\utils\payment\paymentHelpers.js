/**
 * Payment Helper Utilities
 * 
 * Utility functions for PayFast payment integration,
 * form handling, and payment status management.
 */

import logger from '../helpers/logger';

/**
 * Generate PayFast return URLs
 * @param {string} baseUrl - Base application URL
 * @param {string} paymentId - Payment UUID
 * @param {Object} options - Additional options
 * @returns {Object} Return URLs object
 */
export const generatePayFastReturnUrls = (baseUrl, paymentId, options = {}) => {
  const {
    successPath = '/payment/success',
    cancelPath = '/payment/cancel',
    notifyPath = '/api/payments/payfast/notify',
    additionalParams = {}
  } = options;

  const baseParams = {
    payment_id: paymentId,
    ...additionalParams
  };

  const createUrl = (path, params) => {
    const url = new URL(path, baseUrl);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        url.searchParams.set(key, value);
      }
    });
    return url.toString();
  };

  return {
    return_url: createUrl(successPath, baseParams),
    cancel_url: createUrl(cancelPath, baseParams),
    notify_url: createUrl(notifyPath, { payment_id: paymentId })
  };
};

/**
 * Submit PayFast payment form
 * @param {Object} formData - PayFast form data
 * @param {string} actionUrl - PayFast action URL
 * @param {Object} options - Additional options
 */
export const submitPayFastForm = (formData, actionUrl, options = {}) => {
  const {
    target = '_self',
    method = 'POST',
    onBeforeSubmit = null,
    onError = null
  } = options;

  try {
    logger.info('Submitting PayFast form', { actionUrl, formDataKeys: Object.keys(formData) });

    // Call before submit callback
    if (onBeforeSubmit && typeof onBeforeSubmit === 'function') {
      const shouldContinue = onBeforeSubmit(formData, actionUrl);
      if (shouldContinue === false) {
        logger.info('PayFast form submission cancelled by onBeforeSubmit callback');
        return;
      }
    }

    // Create form element
    const form = document.createElement('form');
    form.method = method;
    form.action = actionUrl;
    form.target = target;
    form.style.display = 'none';

    // Add form fields
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = String(value);
        form.appendChild(input);
      }
    });

    // Add form to document and submit
    document.body.appendChild(form);
    form.submit();
    
    // Clean up
    setTimeout(() => {
      if (document.body.contains(form)) {
        document.body.removeChild(form);
      }
    }, 1000);

    logger.info('PayFast form submitted successfully');
  } catch (error) {
    logger.error('Failed to submit PayFast form', { error: error.message, formData, actionUrl });
    
    if (onError && typeof onError === 'function') {
      onError(error);
    } else {
      throw error;
    }
  }
};

/**
 * Validate PayFast form data
 * @param {Object} formData - PayFast form data
 * @returns {Object} Validation result
 */
export const validatePayFastFormData = (formData) => {
  const errors = [];
  const warnings = [];

  // Required fields
  const requiredFields = [
    'merchant_id',
    'merchant_key',
    'amount',
    'item_name',
    'return_url',
    'cancel_url',
    'notify_url'
  ];

  requiredFields.forEach(field => {
    if (!formData[field] || String(formData[field]).trim() === '') {
      errors.push(`Missing required field: ${field}`);
    }
  });

  // Amount validation
  if (formData.amount) {
    const amount = parseFloat(formData.amount);
    if (isNaN(amount) || amount <= 0) {
      errors.push('Amount must be a positive number');
    }
    if (amount < 5) {
      warnings.push('PayFast minimum amount is R5.00');
    }
  }

  // Email validation
  if (formData.email_address) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email_address)) {
      errors.push('Invalid email address format');
    }
  }

  // URL validation
  const urlFields = ['return_url', 'cancel_url', 'notify_url'];
  urlFields.forEach(field => {
    if (formData[field]) {
      try {
        new URL(formData[field]);
      } catch {
        errors.push(`Invalid URL format for ${field}`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Format currency for display
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @param {Object} options - Formatting options
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = 'ZAR', options = {}) => {
  const {
    locale = 'en-ZA',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2
  } = options;

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits,
      maximumFractionDigits
    }).format(amount);
  } catch (error) {
    logger.warn('Currency formatting failed, using fallback', { amount, currency, error: error.message });
    return `${currency} ${parseFloat(amount).toFixed(2)}`;
  }
};

/**
 * Parse currency string to number
 * @param {string} currencyString - Currency string to parse
 * @returns {number} Parsed amount
 */
export const parseCurrency = (currencyString) => {
  if (typeof currencyString === 'number') {
    return currencyString;
  }

  // Remove currency symbols and spaces
  const cleanString = String(currencyString)
    .replace(/[^\d.,\-]/g, '')
    .replace(/,/g, '');

  const amount = parseFloat(cleanString);
  return isNaN(amount) ? 0 : amount;
};

/**
 * Generate payment reference number
 * @param {string} prefix - Reference prefix
 * @param {Object} options - Generation options
 * @returns {string} Payment reference
 */
export const generatePaymentReference = (prefix = 'PAY', options = {}) => {
  const {
    includeTimestamp = true,
    includeRandom = true,
    separator = '-'
  } = options;

  const parts = [prefix];

  if (includeTimestamp) {
    const timestamp = Date.now().toString(36).toUpperCase();
    parts.push(timestamp);
  }

  if (includeRandom) {
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    parts.push(random);
  }

  return parts.join(separator);
};

/**
 * Calculate PayFast fees (approximate)
 * @param {number} amount - Transaction amount
 * @param {string} paymentMethod - Payment method
 * @returns {Object} Fee calculation
 */
export const calculatePayFastFees = (amount, paymentMethod = 'card') => {
  const feeStructure = {
    card: { percentage: 2.9, fixed: 0 },
    eft: { percentage: 0, fixed: 10 },
    instant_eft: { percentage: 1.45, fixed: 0 }
  };

  const fees = feeStructure[paymentMethod] || feeStructure.card;
  const percentageFee = (amount * fees.percentage) / 100;
  const totalFee = percentageFee + fees.fixed;
  const netAmount = amount - totalFee;

  return {
    grossAmount: amount,
    percentageFee,
    fixedFee: fees.fixed,
    totalFee,
    netAmount,
    feePercentage: fees.percentage
  };
};

/**
 * Validate payment amount
 * @param {number} amount - Amount to validate
 * @param {Object} options - Validation options
 * @returns {Object} Validation result
 */
export const validatePaymentAmount = (amount, options = {}) => {
  const {
    minAmount = 5,
    maxAmount = 1000000,
    currency = 'ZAR'
  } = options;

  const errors = [];
  const warnings = [];

  if (typeof amount !== 'number' || isNaN(amount)) {
    errors.push('Amount must be a valid number');
    return { isValid: false, errors, warnings };
  }

  if (amount <= 0) {
    errors.push('Amount must be greater than zero');
  }

  if (amount < minAmount) {
    errors.push(`Minimum amount is ${formatCurrency(minAmount, currency)}`);
  }

  if (amount > maxAmount) {
    errors.push(`Maximum amount is ${formatCurrency(maxAmount, currency)}`);
  }

  // Check for reasonable decimal places
  const decimalPlaces = (amount.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) {
    warnings.push('Amount will be rounded to 2 decimal places');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    roundedAmount: Math.round(amount * 100) / 100
  };
};

/**
 * Create payment timeout handler
 * @param {number} timeoutMs - Timeout in milliseconds
 * @param {Function} onTimeout - Timeout callback
 * @returns {Object} Timeout handler
 */
export const createPaymentTimeout = (timeoutMs = 300000, onTimeout = null) => {
  let timeoutId = null;
  let isActive = true;

  const start = () => {
    if (!isActive) return;

    timeoutId = setTimeout(() => {
      if (isActive && onTimeout && typeof onTimeout === 'function') {
        logger.warn('Payment timeout reached', { timeoutMs });
        onTimeout();
      }
    }, timeoutMs);
  };

  const clear = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    isActive = false;
  };

  const reset = () => {
    clear();
    isActive = true;
    start();
  };

  return {
    start,
    clear,
    reset,
    isActive: () => isActive
  };
};

/**
 * Get payment status display info
 * @param {string} status - Payment status
 * @returns {Object} Display information
 */
export const getPaymentStatusInfo = (status) => {
  const statusMap = {
    pending: {
      color: 'yellow',
      icon: 'clock',
      label: 'Pending',
      description: 'Payment is being processed'
    },
    completed: {
      color: 'green',
      icon: 'check',
      label: 'Completed',
      description: 'Payment was successful'
    },
    failed: {
      color: 'red',
      icon: 'x',
      label: 'Failed',
      description: 'Payment was unsuccessful'
    },
    cancelled: {
      color: 'gray',
      icon: 'x',
      label: 'Cancelled',
      description: 'Payment was cancelled'
    },
    refunded: {
      color: 'blue',
      icon: 'refresh',
      label: 'Refunded',
      description: 'Payment was refunded'
    }
  };

  return statusMap[status] || statusMap.pending;
};
