import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiUsers, FiMail, FiUserCheck } from 'react-icons/fi';
import {
  getCollaborations,
  fetchSentInvitations,
  fetchReceivedInvitations,
  sendInvitationToMentor,
  acceptInvitation,
  rejectInvitation,
  updateCollaboration,
  deleteCollaboration,
  clearSendInvitationState,
  clearRespondInvitationState,
  clearCollaborationActions,
  selectCollaborations,
  selectSentInvitations,
  selectReceivedInvitations,
  selectSendInvitationState,
  selectRespondInvitationState,
  selectCollaborationActions
} from '../../store/slices/CollaborationSlice';
import CollaborationList from '../../components/collaboration/CollaborationList';
import InvitationList from '../../components/collaboration/InvitationList';
import SendInvitationModal from '../../components/collaboration/SendInvitationModal';
import { useToast } from '../../components/ui/Toast';

const InstituteCollaborations = () => {
  const dispatch = useDispatch();
  const toast = useToast();
  const [activeTab, setActiveTab] = useState('collaborations');
  const [showSendModal, setShowSendModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Redux state
  const collaborations = useSelector(selectCollaborations);
  const sentInvitations = useSelector(selectSentInvitations);
  const receivedInvitations = useSelector(selectReceivedInvitations);
  const sendInvitationState = useSelector(selectSendInvitationState);
  const respondInvitationState = useSelector(selectRespondInvitationState);
  const collaborationActions = useSelector(selectCollaborationActions);

  // Load data on component mount
  useEffect(() => {
    loadCollaborations();
    loadInvitations();
  }, []);

  // Handle success states
  useEffect(() => {
    if (sendInvitationState.success) {
      toast.success('Success', 'Invitation sent successfully!');
      dispatch(clearSendInvitationState());
      loadInvitations(); // Refresh invitations
    }
  }, [sendInvitationState.success]);

  useEffect(() => {
    if (respondInvitationState.success) {
      toast.success('Success', 'Invitation response sent successfully!');
      dispatch(clearRespondInvitationState());
      loadInvitations(); // Refresh invitations
      loadCollaborations(); // Refresh collaborations in case invitation was accepted
    }
  }, [respondInvitationState.success]);

  useEffect(() => {
    if (collaborationActions.success) {
      toast.success('Success', 'Collaboration updated successfully!');
      dispatch(clearCollaborationActions());
      loadCollaborations(); // Refresh collaborations
    }
  }, [collaborationActions.success]);

  // Handle error states
  useEffect(() => {
    if (sendInvitationState.error) {
      toast.error('Error', typeof sendInvitationState.error === 'string'
        ? sendInvitationState.error
        : 'Failed to send invitation');
    }
  }, [sendInvitationState.error]);

  useEffect(() => {
    if (respondInvitationState.error) {
      toast.error('Error', typeof respondInvitationState.error === 'string'
        ? respondInvitationState.error
        : 'Failed to respond to invitation');
    }
  }, [respondInvitationState.error]);

  useEffect(() => {
    if (collaborationActions.error) {
      toast.error('Error', typeof collaborationActions.error === 'string'
        ? collaborationActions.error
        : 'Failed to update collaboration');
    }
  }, [collaborationActions.error]);

  const loadCollaborations = () => {
    dispatch(getCollaborations({ page: currentPage, size: 20 }));
  };

  const loadInvitations = () => {
    dispatch(fetchSentInvitations({ page: 1, size: 50 }));
    dispatch(fetchReceivedInvitations({ page: 1, size: 50 }));
  };

  const handleSendInvitation = async (invitationData) => {
    await dispatch(sendInvitationToMentor(invitationData)).unwrap();
  };

  const handleAcceptInvitation = async (invitation) => {
    await dispatch(acceptInvitation(invitation.id)).unwrap();
  };

  const handleRejectInvitation = async (invitation) => {
    await dispatch(rejectInvitation(invitation.id)).unwrap();
  };

  const handleEditCollaboration = (collaboration) => {
    // TODO: Implement edit modal
    console.log('Edit collaboration:', collaboration);
  };

  const handleDeleteCollaboration = async (collaboration) => {
    if (window.confirm('Are you sure you want to delete this collaboration?')) {
      await dispatch(deleteCollaboration(collaboration.id)).unwrap();
    }
  };

  const handleViewCollaborationDetails = (collaboration) => {
    // TODO: Implement details modal or navigation
    console.log('View collaboration details:', collaboration);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    dispatch(getCollaborations({ page, size: 20 }));
  };

  const handleFilterChange = (filters) => {
    dispatch(getCollaborations({
      page: 1,
      size: 20,
      statusFilter: filters.status
    }));
  };

  const tabs = [
    {
      id: 'collaborations',
      name: 'Active Collaborations',
      icon: FiUserCheck,
      count: collaborations.total || 0
    },
    {
      id: 'invitations',
      name: 'Invitations',
      icon: FiMail,
      count: (sentInvitations.total || 0) + (receivedInvitations.total || 0)
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-3 bg-violet-100 dark:bg-violet-900/30 rounded-xl">
            <FiUsers className="w-6 h-6 text-violet-600 dark:text-violet-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Collaboration Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your collaborations with mentors and handle invitations
            </p>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-violet-500 text-violet-600 dark:text-violet-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                    <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-0.5 rounded-full text-xs">
                      {tab.count}
                    </span>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        {activeTab === 'collaborations' && (
          <CollaborationList
            collaborations={collaborations.data}
            loading={collaborations.loading}
            error={collaborations.error}
            pagination={collaborations}
            onEdit={handleEditCollaboration}
            onDelete={handleDeleteCollaboration}
            onViewDetails={handleViewCollaborationDetails}
            onPageChange={handlePageChange}
            onFilterChange={handleFilterChange}
            onCreateNew={() => setShowSendModal(true)}
            userRole="institute"
            showCreateButton={true}
          />
        )}

        {activeTab === 'invitations' && (
          <InvitationList
            sentInvitations={sentInvitations.data}
            receivedInvitations={receivedInvitations.data}
            loading={sentInvitations.loading || receivedInvitations.loading}
            error={sentInvitations.error || receivedInvitations.error}
            onAccept={handleAcceptInvitation}
            onReject={handleRejectInvitation}
            onSendNew={() => setShowSendModal(true)}
            userRole="institute"
            showSendButton={true}
          />
        )}
      </div>

      {/* Send Invitation Modal */}
      <SendInvitationModal
        isOpen={showSendModal}
        onClose={() => setShowSendModal(false)}
        onSend={handleSendInvitation}
        loading={sendInvitationState.loading}
        userRole="institute"
        recipientType="mentor"
      />
    </div>
  );
};

export default InstituteCollaborations;
