import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import API_BASE_URL from "../../utils/api/API_URL";
import Navbar from "../common/navbar/Navbar";

function SignupMenu() {
  const [selectedRole, setSelectedRole] = useState(null);
  const navigate = useNavigate();

  // Check if user is already authenticated on component mount
  useEffect(() => {
    const token = localStorage.getItem('token');
    const role = localStorage.getItem('role');
    
    if (token && role) {
      // Validate token before redirecting
      const validateToken = async () => {
        try {
          await axios.get(`${API_BASE_URL}/api/users/me`);
          
          // Token is valid, redirect to dashboard
          const rolePath = role.toLowerCase();
          navigate(`/${rolePath}/dashboard`);
        } catch (error) {
          // Token is invalid or network error, clear auth data
          localStorage.removeItem('token');
          localStorage.removeItem('role');
          localStorage.removeItem('userdata');
        }
      };

      validateToken();
    }
  }, [navigate]);

  const roles = [
    {
      key: "student",
      title: "Sign up as Student",
      description: "Access learning material and apply for services.",
    },
    {
      key: "teacher",
      title: "Sign up as Teacher",
      description: "Offer your expertise and share knowledge.",
    },
    {
      key: "mentor",
      title: "Sign up as Mentor",
      description: "Offer your experience and knowledge.",
    },
    {
      key: "sponsor",
      title: "Sign up as Sponsor",
      description: "Support educational growth through sponsorships.",
    },
    {
      key: "institute",
      title: "Sign up as Institution",
      description: "Manage courses, teachers, and student programs.",
    },
  ];

  const handleContinue = () => {
    if (selectedRole) {
      navigate(`/signup/${selectedRole}`); // 👈 Route param added here
    }
  };

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 flex flex-col justify-center items-center px-4 py-8 pt-20">
        <div className="text-center max-w-6xl w-full">
        <div className="mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Choose your role
          </h1>
          <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
            Select the option that best describes you to get started with EduFair.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 w-full mb-8">
          {roles.map((role) => (
            <div
              key={role.key}
              onClick={() => setSelectedRole(role.key)}
              className={`
                border-2 rounded-2xl p-6 cursor-pointer text-center transition-all duration-300
                transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-violet-500/50
                min-h-[140px] flex flex-col justify-center
                ${selectedRole === role.key
                  ? "border-violet-500 bg-violet-50 dark:bg-violet-900/30 shadow-xl ring-4 ring-violet-500/20"
                  : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-violet-300 dark:hover:border-violet-600 hover:shadow-lg"
                }
              `}
              tabIndex={0}
              role="button"
              aria-pressed={selectedRole === role.key}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setSelectedRole(role.key);
                }
              }}
            >
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                {role.title}
              </h2>
              <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                {role.description}
              </p>
            </div>
          ))}
        </div>

        <button
          onClick={handleContinue}
          disabled={!selectedRole}
          className={`
            w-full max-w-sm py-3 px-6 rounded-xl font-semibold text-white transition-all duration-200
            focus:outline-none focus:ring-4 focus:ring-violet-500/50 min-h-[48px]
            ${selectedRole
              ? "bg-violet-600 hover:bg-violet-700 active:bg-violet-800 shadow-lg hover:shadow-xl transform hover:scale-105"
              : "bg-gray-300 dark:bg-gray-600 cursor-not-allowed opacity-50"
            }
          `}
        >
          Continue
        </button>
        </div>
      </div>
    </>
  );
}

export default SignupMenu;
