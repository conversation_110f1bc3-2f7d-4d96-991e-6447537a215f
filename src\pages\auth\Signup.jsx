
import { useParams } from 'react-router-dom';
import SignupForm from '../../components/auth/SignupForm';
import Navbar from '../common/navbar/Navbar';
import teacherImage from '../../assets/images/auth/teacher-signup-image.png';
import mentorImage from '../../assets/images/auth/mentor-signup-image.png';
import institutionImage from '../../assets/images/auth/institution-signup-image.png';
import sponsorImage from '../../assets/images/auth/sponsor-signup-image.png';

const Signup = () => {
  const { role } = useParams();
  const userRole = role || 'student';

  const showIllustration = ['institution', 'sponsor', 'mentor', 'teacher'].includes(userRole);

  const getIllustration = () => {
    switch (userRole) {
      case 'teacher':
        return teacherImage;
      case 'mentor':
        return mentorImage;
      case 'institution':
        return institutionImage;
      case 'sponsor':
        return sponsorImage;
      default:
        return teacherImage;
    }
  };

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 pt-20 pb-8">
        <div className="container mx-auto px-4 h-full">
          <div className={`grid gap-8 lg:gap-12 items-center h-full min-h-[calc(100vh-5rem)] ${showIllustration ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-1 max-w-2xl mx-auto'}`}>
            {showIllustration && (
              <div className="hidden lg:flex justify-center items-center order-2 lg:order-1">
                <div className="relative w-full max-w-2xl">
                  <img
                    src={getIllustration()}
                    alt={`${userRole} signup illustration`}
                    className="w-full h-auto object-contain drop-shadow-2xl"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-violet-100/20 to-transparent rounded-3xl"></div>
                </div>
              </div>
            )}

            <div className={`w-full ${showIllustration ? 'order-1 lg:order-2' : ''}`}>
              <div className="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl border border-gray-100 dark:border-gray-700 p-6 sm:p-8 lg:p-10">
                <SignupForm role={userRole} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Signup;
