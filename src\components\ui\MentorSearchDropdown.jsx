import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiSearch, FiChevronDown, FiUser, FiStar, FiMapPin, FiCheckCircle, FiFilter, FiX } from 'react-icons/fi';
import { fetchMentorsList } from '../../store/slices/MentorsSlice';
import { fetchSubjects } from '../../store/slices/SubjectSlice';

const MentorSearchDropdown = ({
  value,
  onChange,
  placeholder = "Search and select a mentor...",
  className = "",
  error = null,
  disabled = false,
  showFilters = false
}) => {
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMentor, setSelectedMentor] = useState(null);
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [filters, setFilters] = useState({
    expertise_areas: [],
    country: '',
    is_verified: '' // '' for all, 'true' for verified only, 'false' for unverified only
  });
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // Get mentors data from Redux store
  const {
    publicMentors,
    publicMentorsLoading,
    publicMentorsError
  } = useSelector(state => state.mentors);

  // Get subjects data from Redux store
  const {
    subjects,
    loading: subjectsLoading
  } = useSelector(state => state.subjects);

  // Fetch subjects when component mounts
  useEffect(() => {
    dispatch(fetchSubjects({ limit: 100 }));
  }, [dispatch]);

  // Fetch mentors when component mounts or search term/filters change
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      const searchParams = {
        search: searchTerm,
        page: 1,
        size: 20,
        ...filters
      };

      // Clean up empty filter values
      Object.keys(searchParams).forEach(key => {
        if (searchParams[key] === '' || (Array.isArray(searchParams[key]) && searchParams[key].length === 0)) {
          delete searchParams[key];
        }
      });

      // Convert parameters for API compatibility
      if (searchParams.is_verified !== '') {
        searchParams.verified_only = searchParams.is_verified === 'true';
        delete searchParams.is_verified;
      }

      // Convert expertise_areas to subject for API compatibility
      if (searchParams.expertise_areas && Array.isArray(searchParams.expertise_areas) && searchParams.expertise_areas.length > 0) {
        searchParams.subject = searchParams.expertise_areas[0]; // API expects single subject
        delete searchParams.expertise_areas;
      }

      dispatch(fetchMentorsList(searchParams)).catch(error => {
        console.error('Error fetching mentors:', error);
      });
    }, 300); // Debounce search

    return () => clearTimeout(delayedSearch);
  }, [dispatch, searchTerm, filters]);

  // Handle clicking outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setShowFilterPanel(false);
        setSearchTerm('');
      }
    };

    // Use both mousedown and click for better reliability
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('click', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // Find selected mentor from the list
  useEffect(() => {
    if (value && publicMentors?.mentors) {
      const mentor = publicMentors.mentors.find(m => m.id === value);
      setSelectedMentor(mentor);
    } else {
      setSelectedMentor(null);
    }
  }, [value, publicMentors]);

  const handleMentorSelect = (mentor, event) => {
    // Prevent event bubbling to avoid conflicts
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    setSelectedMentor(mentor);
    onChange(mentor.id);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleDropdownToggle = (event) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (!disabled) {
      setIsOpen(prev => {
        const newIsOpen = !prev;
        if (newIsOpen) {
          // Use a shorter timeout for better responsiveness
          setTimeout(() => searchInputRef.current?.focus(), 50);
        }
        return newIsOpen;
      });
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleExpertiseAreaToggle = (area) => {
    setFilters(prev => ({
      ...prev,
      expertise_areas: prev.expertise_areas.includes(area)
        ? prev.expertise_areas.filter(a => a !== area)
        : [...prev.expertise_areas, area]
    }));
  };

  const clearFilters = () => {
    setFilters({
      expertise_areas: [],
      country: '',
      is_verified: ''
    });
  };

  const hasActiveFilters = () => {
    return filters.expertise_areas.length > 0 ||
           filters.country ||
           filters.is_verified !== '';
  };

  // Handle different possible data structures from the API
  const filteredMentors = publicMentors?.mentors || publicMentors?.data || publicMentors || [];

  // Get subjects for expertise areas from backend
  const availableSubjects = subjects || [];

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Selected Mentor Display / Trigger */}
      <div
        onClick={handleDropdownToggle}
        onMouseDown={(event) => {
          // Prevent focus issues
          event.preventDefault();
        }}
        className={`
          w-full px-3 py-2 border rounded-md cursor-pointer transition-colors duration-200 select-none
          ${error
            ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
            : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
          }
          ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white hover:border-gray-400'}
          ${isOpen ? 'ring-2 ring-blue-500 ring-opacity-20' : ''}
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1 min-w-0">
            {selectedMentor ? (
              <>
                <div className="flex-shrink-0 w-8 h-8 mr-3">
                  {selectedMentor.profile_image_url ? (
                    <img
                      src={selectedMentor.profile_image_url}
                      alt={selectedMentor.full_name}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <FiUser className="w-4 h-4 text-gray-500" />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 truncate">
                      {selectedMentor.full_name}
                    </span>
                    {selectedMentor.is_verified && (
                      <FiCheckCircle className="w-4 h-4 text-green-500 ml-1 flex-shrink-0" />
                    )}
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <FiStar className="w-3 h-3 mr-1" />
                    <span>{selectedMentor.rating || 'N/A'}</span>
                    <span className="mx-1">•</span>
                    <span>{selectedMentor.experience_years || 0} years</span>
                  </div>
                </div>
              </>
            ) : (
              <span className="text-gray-500">{placeholder}</span>
            )}
          </div>
          <FiChevronDown 
            className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
              isOpen ? 'transform rotate-180' : ''
            }`} 
          />
        </div>
      </div>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search mentors..."
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>

            {/* Filter Toggle */}
            {showFilters && (
              <div className="flex items-center justify-between mt-2">
                <button
                  onClick={() => setShowFilterPanel(!showFilterPanel)}
                  className={`flex items-center px-3 py-1 text-xs rounded-md transition-colors ${
                    hasActiveFilters()
                      ? 'bg-blue-100 text-blue-700 border border-blue-300'
                      : 'bg-gray-100 text-gray-600 border border-gray-300'
                  }`}
                >
                  <FiFilter className="w-3 h-3 mr-1" />
                  Filters
                  {hasActiveFilters() && (
                    <span className="ml-1 bg-blue-500 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs">
                      {[
                        filters.expertise_areas.length > 0,
                        filters.country,
                        filters.is_verified !== ''
                      ].filter(Boolean).length}
                    </span>
                  )}
                </button>

                {hasActiveFilters() && (
                  <button
                    onClick={clearFilters}
                    className="flex items-center px-2 py-1 text-xs text-red-600 hover:text-red-800"
                  >
                    <FiX className="w-3 h-3 mr-1" />
                    Clear
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Filter Panel */}
          {showFilters && showFilterPanel && (
            <div className="p-3 border-b border-gray-200 bg-gray-50 max-h-80 overflow-y-auto">
              <div className="space-y-3">
                {/* Subjects/Expertise Areas */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Subjects</label>
                  {subjectsLoading ? (
                    <div className="text-xs text-gray-500">Loading subjects...</div>
                  ) : (
                    <div className="flex flex-wrap gap-1">
                      {availableSubjects.map(subject => (
                        <button
                          key={subject.id}
                          onClick={() => handleExpertiseAreaToggle(subject.name)}
                          className={`px-2 py-1 text-xs rounded transition-colors ${
                            filters.expertise_areas.includes(subject.name)
                              ? 'bg-blue-500 text-white'
                              : 'bg-white text-gray-600 border border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {subject.name}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Verification Status and Country */}
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Verification Status</label>
                    <select
                      value={filters.is_verified}
                      onChange={(e) => handleFilterChange('is_verified', e.target.value)}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                    >
                      <option value="">All Mentors</option>
                      <option value="true">Verified Only</option>
                      <option value="false">Unverified Only</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Country</label>
                    <input
                      type="text"
                      value={filters.country}
                      onChange={(e) => handleFilterChange('country', e.target.value)}
                      placeholder="e.g. USA"
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Mentors List */}
          <div className="max-h-60 overflow-y-auto">
            {publicMentorsLoading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
                <span className="mt-2 block text-sm">Loading mentors...</span>
              </div>
            ) : publicMentorsError ? (
              <div className="p-4 text-center text-red-500 text-sm">
                Error loading mentors: {
                  typeof publicMentorsError === 'string'
                    ? publicMentorsError
                    : publicMentorsError?.message || publicMentorsError?.error || 'Failed to load mentors'
                }
              </div>
            ) : !Array.isArray(filteredMentors) || filteredMentors.length === 0 ? (
              <div className="p-4 text-center text-gray-500 text-sm">
                {searchTerm ? 'No mentors found matching your search.' : 'No mentors available.'}
              </div>
            ) : (
              filteredMentors.filter(mentor => mentor && typeof mentor === 'object' && mentor.id).map((mentor) => (
                <div
                  key={mentor.id}
                  onClick={(event) => handleMentorSelect(mentor, event)}
                  onMouseDown={(event) => {
                    // Prevent the dropdown from closing before the click is processed
                    event.preventDefault();
                  }}
                  className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150 select-none"
                >
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-10 h-10 mr-3">
                      {mentor.profile_image_url ? (
                        <img
                          src={mentor.profile_image_url}
                          alt={mentor.full_name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <FiUser className="w-5 h-5 text-gray-500" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-gray-900 truncate">
                          {mentor.full_name}
                        </span>
                        {mentor.is_verified && (
                          <FiCheckCircle className="w-4 h-4 text-green-500 ml-1 flex-shrink-0" />
                        )}
                      </div>
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <FiStar className="w-3 h-3 mr-1" />
                        <span>{mentor.rating || 'N/A'}</span>
                        <span className="mx-1">•</span>
                        <span>{mentor.experience_years || 0} years</span>
                        {mentor.country && (
                          <>
                            <span className="mx-1">•</span>
                            <FiMapPin className="w-3 h-3 mr-1" />
                            <span>{mentor.country}</span>
                          </>
                        )}
                      </div>
                      {mentor.expertise_areas && Array.isArray(mentor.expertise_areas) && mentor.expertise_areas.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {mentor.expertise_areas.slice(0, 3).map((area, index) => (
                            <span
                              key={index}
                              className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                            >
                              {area}
                            </span>
                          ))}
                          {mentor.expertise_areas.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{mentor.expertise_areas.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

export default MentorSearchDropdown;
