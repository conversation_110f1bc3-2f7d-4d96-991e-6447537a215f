import React, { useState } from 'react';
import { usePaymentAccess } from '../../hooks/usePaymentAccess';
import { EventTicketPurchase } from '../../components/events';
import { 
  canPurchaseEventTickets, 
  getPaymentAccessMessage,
  getAvailablePaymentMethods 
} from '../../utils/payment/paymentAccessControl';
import { LoadingSpinner } from '../../components/ui';

const PaymentSystemTest = () => {
  const [showModal, setShowModal] = useState(false);
  
  // Mock event data for testing
  const mockEvent = {
    id: 'test-event-123',
    title: 'Test Event',
    organizer_id: 'different-organizer-id',
    tickets: [
      {
        id: 'ticket-1',
        name: 'General Admission',
        price: 100,
        currency: 'ZAR',
        max_quantity_per_order: 5
      }
    ]
  };

  // Test payment access hook
  const { 
    canPurchase, 
    paymentAccess, 
    currentUser,
    availablePaymentMethods,
    isEventOrganizer,
    isAdmin
  } = usePaymentAccess(mockEvent);

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Payment System Test</h1>
      
      {/* User Info */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Current User</h2>
        {currentUser ? (
          <div className="space-y-2">
            <p><strong>Role:</strong> {currentUser.user_type || currentUser.role}</p>
            <p><strong>Email:</strong> {currentUser.email}</p>
            <p><strong>ID:</strong> {currentUser.id || currentUser.user_id}</p>
          </div>
        ) : (
          <p className="text-gray-600">No user logged in</p>
        )}
      </div>

      {/* Payment Access Status */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Access Status</h2>
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <span className="font-medium">Can Purchase:</span>
            <span className={`px-2 py-1 rounded text-sm ${
              canPurchase ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {canPurchase ? 'Yes' : 'No'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="font-medium">Is Admin:</span>
            <span className={`px-2 py-1 rounded text-sm ${
              isAdmin() ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
            }`}>
              {isAdmin() ? 'Yes' : 'No'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="font-medium">Is Event Organizer:</span>
            <span className={`px-2 py-1 rounded text-sm ${
              isEventOrganizer(mockEvent) ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
            }`}>
              {isEventOrganizer(mockEvent) ? 'Yes' : 'No'}
            </span>
          </div>

          {paymentAccess && (
            <div className="mt-4 p-3 bg-gray-50 rounded">
              <p><strong>Access Reason:</strong> {paymentAccess.reason}</p>
              <p><strong>Message:</strong> {paymentAccess.message}</p>
            </div>
          )}
        </div>
      </div>

      {/* Available Payment Methods */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Available Payment Methods</h2>
        {availablePaymentMethods.length > 0 ? (
          <div className="space-y-2">
            {availablePaymentMethods.map((method) => (
              <div key={method.id} className="p-3 border border-gray-200 rounded">
                <div className="font-medium">{method.name}</div>
                <div className="text-sm text-gray-600">{method.description}</div>
                <div className="text-xs text-gray-500">{method.fees}</div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-600">No payment methods available</p>
        )}
      </div>

      {/* Test Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Actions</h2>
        <div className="space-y-3">
          <button
            onClick={() => setShowModal(true)}
            disabled={!canPurchase}
            className={`px-4 py-2 rounded font-medium ${
              canPurchase
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Test Payment Modal
          </button>
          
          <div className="text-sm text-gray-600">
            {canPurchase 
              ? 'Click to test the payment modal' 
              : 'Payment not available for your role/situation'
            }
          </div>
        </div>
      </div>

      {/* Mock Event Details */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Mock Event Details</h2>
        <div className="space-y-2">
          <p><strong>Event ID:</strong> {mockEvent.id}</p>
          <p><strong>Title:</strong> {mockEvent.title}</p>
          <p><strong>Organizer ID:</strong> {mockEvent.organizer_id}</p>
          <p><strong>Tickets:</strong> {mockEvent.tickets.length} available</p>
        </div>
      </div>

      {/* Payment Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <EventTicketPurchase
            event={mockEvent}
            onClose={() => setShowModal(false)}
            onSuccess={(payment) => {
              console.log('Payment successful:', payment);
              setShowModal(false);
            }}
            onError={(error) => {
              console.error('Payment error:', error);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default PaymentSystemTest;
