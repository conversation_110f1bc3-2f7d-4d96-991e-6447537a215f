import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiUsers, 
  FiSend, 
  FiInbox, 
  FiCheck, 
  FiX, 
  FiClock, 
  FiDollarSign,
  FiCalendar,
  FiPlus
} from 'react-icons/fi';
import { 
  fetchSentInvitations,
  fetchReceivedInvitations,
  respondToInvitation,
  selectSentInvitations,
  selectReceivedInvitations,
  selectRespondInvitationState,
  clearRespondInvitationState
} from '../../store/slices/collaborationSlice';
import { LoadingSpinner, ActionModal } from '../ui';
import InviteModal from './InviteModal';

const CollaborationDashboard = ({ userType = 'institute' }) => {
  const dispatch = useDispatch();
  const sentInvitations = useSelector(selectSentInvitations);
  const receivedInvitations = useSelector(selectReceivedInvitations);
  const respondState = useSelector(selectRespondInvitationState);

  const [activeTab, setActiveTab] = useState('received');
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [selectedInvitation, setSelectedInvitation] = useState(null);
  const [responseType, setResponseType] = useState('accept');

  useEffect(() => {
    dispatch(fetchSentInvitations({ page: 1, size: 20 }));
    dispatch(fetchReceivedInvitations({ page: 1, size: 20 }));
  }, [dispatch]);

  useEffect(() => {
    if (respondState.success) {
      setShowResponseModal(false);
      setSelectedInvitation(null);
      dispatch(clearRespondInvitationState());
      // Refresh data
      dispatch(fetchReceivedInvitations({ page: 1, size: 20 }));
    }
  }, [respondState.success, dispatch]);

  const handleRespond = (invitation, type) => {
    setSelectedInvitation(invitation);
    setResponseType(type);
    setShowResponseModal(true);
  };

  const confirmResponse = async () => {
    if (selectedInvitation) {
      await dispatch(respondToInvitation({
        invitationId: selectedInvitation.id,
        response: { status: responseType }
      }));
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const InvitationCard = ({ invitation, type }) => (
    <div className="bg-white rounded-lg border p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <FiUsers className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {type === 'sent' ? 'Invitation Sent' : 'Invitation Received'}
            </h3>
            <p className="text-sm text-gray-600">
              {invitation.received_by === 'mentor' ? 'To Mentor' : 'To Institute'}
            </p>
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
              <div className="flex items-center">
                <FiDollarSign className="h-4 w-4 mr-1" />
                ${invitation.hourly_rate}/hr
              </div>
              <div className="flex items-center">
                <FiClock className="h-4 w-4 mr-1" />
                {invitation.hours_per_week}h/week
              </div>
              <div className="flex items-center">
                <FiCalendar className="h-4 w-4 mr-1" />
                {formatDate(invitation.created_at)}
              </div>
            </div>
          </div>
        </div>
        
        {type === 'received' && (
          <div className="flex space-x-2">
            <button
              onClick={() => handleRespond(invitation, 'accept')}
              className="flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <FiCheck className="h-4 w-4 mr-1" />
              Accept
            </button>
            <button
              onClick={() => handleRespond(invitation, 'reject')}
              className="flex items-center px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              <FiX className="h-4 w-4 mr-1" />
              Reject
            </button>
          </div>
        )}
        
        {type === 'sent' && (
          <div className="flex items-center px-3 py-2 bg-yellow-100 text-yellow-800 rounded-lg">
            <FiClock className="h-4 w-4 mr-1" />
            Pending
          </div>
        )}
      </div>
    </div>
  );

  const EmptyState = ({ type }) => (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
        {type === 'sent' ? (
          <FiSend className="h-8 w-8 text-gray-400" />
        ) : (
          <FiInbox className="h-8 w-8 text-gray-400" />
        )}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        No {type} invitations
      </h3>
      <p className="text-gray-500 mb-4">
        {type === 'sent' 
          ? "You haven't sent any invitations yet." 
          : "You don't have any pending invitations."
        }
      </p>
      {type === 'sent' && (
        <button
          onClick={() => setShowInviteModal(true)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <FiPlus className="h-4 w-4 mr-2" />
          Send Invitation
        </button>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Collaborations</h1>
          <p className="text-gray-600">
            Manage your {userType === 'institute' ? 'mentor' : 'institute'} collaborations
          </p>
        </div>
        <button
          onClick={() => setShowInviteModal(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <FiPlus className="h-4 w-4 mr-2" />
          Send Invitation
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('received')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'received'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FiInbox className="inline h-4 w-4 mr-2" />
            Received ({receivedInvitations.total})
          </button>
          <button
            onClick={() => setActiveTab('sent')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'sent'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FiSend className="inline h-4 w-4 mr-2" />
            Sent ({sentInvitations.total})
          </button>
        </nav>
      </div>

      {/* Content */}
      <div className="space-y-4">
        {activeTab === 'received' && (
          <>
            {receivedInvitations.loading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner size="lg" />
              </div>
            ) : receivedInvitations.data.length > 0 ? (
              receivedInvitations.data.map((invitation) => (
                <InvitationCard
                  key={invitation.id}
                  invitation={invitation}
                  type="received"
                />
              ))
            ) : (
              <EmptyState type="received" />
            )}
          </>
        )}

        {activeTab === 'sent' && (
          <>
            {sentInvitations.loading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner size="lg" />
              </div>
            ) : sentInvitations.data.length > 0 ? (
              sentInvitations.data.map((invitation) => (
                <InvitationCard
                  key={invitation.id}
                  invitation={invitation}
                  type="sent"
                />
              ))
            ) : (
              <EmptyState type="sent" />
            )}
          </>
        )}
      </div>

      {/* Invite Modal */}
      <InviteModal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        userType={userType}
      />

      {/* Response Modal */}
      <ActionModal
        isOpen={showResponseModal}
        onClose={() => {
          setShowResponseModal(false);
          setSelectedInvitation(null);
        }}
        onConfirm={confirmResponse}
        title={`${responseType === 'accept' ? 'Accept' : 'Reject'} Invitation`}
        message={`Are you sure you want to ${responseType} this collaboration invitation?`}
        confirmText={responseType === 'accept' ? 'Accept' : 'Reject'}
        confirmColor={responseType === 'accept' ? 'green' : 'red'}
        icon={responseType === 'accept' ? FiCheck : FiX}
        loading={respondState.loading}
      />
    </div>
  );
};

export default CollaborationDashboard;
