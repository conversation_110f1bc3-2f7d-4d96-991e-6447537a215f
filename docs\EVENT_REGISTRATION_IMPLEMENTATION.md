# Event Registration Implementation

## Overview
I've implemented a comprehensive event registration system with proper ticket selection functionality. Since the backend API endpoints for events are not yet implemented, the system uses mock data and APIs that provide a realistic preview of the complete functionality.

## What's Been Implemented

### 1. **New Event Registration Modal** (`EventRegistrationModalNew.jsx`)
- **Multi-step registration process**:
  - Step 1: Ticket Selection with quantity controls
  - Step 2: Registration Details (personal info, dietary requirements, emergency contact)
  - Step 3: Payment (for paid events) - shows payment integration placeholder
  - Step 4: Confirmation with registration details

- **Features**:
  - Professional UI with progress indicators
  - Ticket selection with pricing and quantity controls
  - Form validation for required fields
  - Responsive design using Tailwind CSS
  - Loading states and error handling
  - Support for free and paid events

### 2. **Mock Events API Service** (`mockEventsApi.js`)
- **Realistic event data** with multiple ticket types:
  - React & Next.js Workshop (paid event with Early Bird, Regular, Student tickets)
  - AI & Machine Learning Conference (free + VIP tickets)
  - Startup Pitch Competition (participant + audience tickets)

- **API Functions**:
  - `getEvents()` - Get events with filtering and pagination
  - `getEvent(id)` - Get single event details
  - `registerForEvent()` - Handle event registration

### 3. **Updated Components**
- **PublicEventCard**: Now uses the new registration modal
- **EventsSlice**: Enhanced to fallback to mock data when API is unavailable
- **EventsPage**: Re-enabled registration functionality

### 4. **Event Data Structure**
Each event includes:
```javascript
{
  id: 'event-1',
  title: 'Event Title',
  description: 'Full description',
  start_datetime: '2024-02-15T09:00:00Z',
  end_datetime: '2024-02-15T17:00:00Z',
  location: 'Venue Name',
  category: 'workshop|conference|competition',
  max_attendees: 50,
  current_attendees: 23,
  agenda: [...],
  requirements: 'Prerequisites',
  tags: ['react', 'nextjs'],
  tickets: [
    {
      id: 'ticket-1',
      name: 'General Admission',
      description: 'Standard access',
      price: 0,
      currency: 'ZAR',
      max_quantity_per_order: 5,
      available_quantity: 100,
      status: 'ACTIVE'
    }
  ]
}
```

## How It Works

### 1. **Event Display**
- Events are fetched from the mock API when the real API is unavailable
- Each event card shows basic info with a "Register" button
- Featured events are highlighted separately

### 2. **Registration Flow**
1. **User clicks "Register"** → Opens registration modal
2. **Ticket Selection** → User selects ticket type and quantity
3. **Details Form** → User fills in personal information
4. **Payment** (if required) → Shows payment integration placeholder
5. **Confirmation** → Shows registration success with details

### 3. **Ticket Selection**
- Visual ticket cards with pricing and descriptions
- Quantity controls with min/max validation
- Real-time total calculation
- Support for free and paid tickets

### 4. **Form Validation**
- Required field validation (name, email)
- Email format validation
- Real-time error display
- Prevents submission with invalid data

## Backend Integration Ready

The system is designed to easily integrate with real backend APIs:

### Expected API Endpoints:
```
GET /api/events                     # Get all events
GET /api/events/{event_id}          # Get event details  
POST /api/events/{event_id}/register # Register for event
GET /api/events/my-registrations    # Get user registrations
```

### Registration Request Format:
```javascript
{
  event_id: "event-123",
  ticket_id: "ticket-456", 
  quantity: 2,
  user_details: {
    user_name: "John Doe",
    user_email: "<EMAIL>",
    additional_info: "...",
    dietary_requirements: "...",
    emergency_contact: {
      name: "Jane Doe",
      phone: "+27123456789",
      relationship: "Spouse"
    }
  }
}
```

### Registration Response Format:
```javascript
{
  registration_id: "reg-789",
  event_id: "event-123",
  status: "confirmed",
  registration_number: "REG-20240215001",
  registered_at: "2024-02-15T10:30:00Z",
  ticket_info: {...},
  total_amount: 299.99,
  currency: "ZAR"
}
```

## Testing the Implementation

1. **Start the development server**: `npm run dev`
2. **Navigate to Events page** in the application
3. **Click "Register"** on any event card
4. **Go through the registration flow**:
   - Select a ticket type and quantity
   - Fill in registration details
   - See payment step (for paid events)
   - View confirmation screen

## Next Steps

When the backend API is ready:

1. **Remove mock data fallbacks** from EventsSlice.js
2. **Update API endpoints** to match backend implementation
3. **Integrate real payment processing** in the payment step
4. **Add email confirmation** functionality
5. **Implement registration management** (view/cancel registrations)

## Files Modified/Created

### New Files:
- `src/components/events/EventRegistrationModalNew.jsx`
- `src/services/mockEventsApi.js`
- `docs/EVENT_REGISTRATION_IMPLEMENTATION.md`

### Modified Files:
- `src/components/events/PublicEventCard.jsx`
- `src/store/slices/EventsSlice.js`
- `src/pages/events/EventsPage.jsx`

The implementation provides a complete, professional event registration experience that's ready for backend integration while offering full functionality through mock data during development.
