import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  fetchAdminEventRegistrations,
  selectAdminEventRegistrations,
  selectAdminEventRegistrationsLoading,
  selectAdminEventRegistrationsError
} from '../../store/slices/EventsSlice';
import { FiArrowLeft, FiUser, FiMail, FiCalendar, FiDollarSign, FiFilter, FiDownload } from 'react-icons/fi';
import { LoadingSpinner } from '../ui';

const AdminEventRegistrations = ({ eventId }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const registrations = useSelector(selectAdminEventRegistrations);
  const loading = useSelector(selectAdminEventRegistrationsLoading);
  const error = useSelector(selectAdminEventRegistrationsError);

  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState('');
  const perPage = 20;

  useEffect(() => {
    if (eventId) {
      loadRegistrations();
    }
  }, [eventId, currentPage, statusFilter]);

  const loadRegistrations = () => {
    const skip = (currentPage - 1) * perPage;
    dispatch(fetchAdminEventRegistrations({
      eventId,
      skip,
      limit: perPage,
      status_filter: statusFilter || undefined
    }));
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handleStatusFilter = (status) => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleBack = () => {
    navigate(`/admin/events/${eventId}/view`);
  };

  const handleExport = () => {
    alert('Export functionality coming soon!');
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div className="flex items-center">
          <button
            onClick={handleBack}
            className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
          >
            <FiArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Event Registrations</h1>
            <p className="text-gray-600 mt-2">Manage and view all event registrations</p>
          </div>
        </div>
        <button
          onClick={handleExport}
          className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700"
        >
          <FiDownload className="h-4 w-4 mr-2" />
          Export CSV
        </button>
      </div>

      {/* Filters */}
      <div className="mb-6 flex items-center space-x-4">
        <div className="flex items-center">
          <FiFilter className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm font-medium text-gray-700">Filter by status:</span>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => handleStatusFilter('')}
            className={`px-3 py-1 text-sm rounded-full ${
              statusFilter === ''
                ? 'bg-blue-100 text-blue-800'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          <button
            onClick={() => handleStatusFilter('confirmed')}
            className={`px-3 py-1 text-sm rounded-full ${
              statusFilter === 'confirmed'
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Confirmed
          </button>
          <button
            onClick={() => handleStatusFilter('pending')}
            className={`px-3 py-1 text-sm rounded-full ${
              statusFilter === 'pending'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Pending
          </button>
          <button
            onClick={() => handleStatusFilter('cancelled')}
            className={`px-3 py-1 text-sm rounded-full ${
              statusFilter === 'cancelled'
                ? 'bg-red-100 text-red-800'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Cancelled
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <LoadingSpinner size="lg" />
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-600">Error loading registrations: {error?.detail || error?.message || String(error)}</p>
              <button
                onClick={loadRegistrations}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          ) : !registrations?.registrations || registrations.registrations.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No registrations found.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {registrations?.registrations?.map((registration) => (
                <div key={registration.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <div className="flex items-center mb-2">
                        <FiUser className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="font-medium text-gray-900">{registration.user_name}</span>
                      </div>
                      <div className="flex items-center mb-2">
                        <FiMail className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-600">{registration.user_email}</span>
                      </div>
                      <div className="text-xs text-gray-500">
                        Registration: {registration.registration_number}
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center mb-2">
                        <FiCalendar className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-600">
                          {formatDate(registration.registered_at)}
                        </span>
                      </div>
                      <div className="flex items-center mb-2">
                        <span className="text-sm text-gray-600">
                          Ticket: {registration.ticket_name}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        Quantity: {registration.quantity}
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center mb-2">
                        <FiDollarSign className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm font-medium text-gray-900">
                          {registration.currency} {registration.total_amount}
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(registration.status)}`}>
                          {registration.status}
                        </span>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getPaymentStatusColor(registration.payment_status)}`}>
                          {registration.payment_status}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        {registrations && registrations.total_count > perPage && (
          <div className="mt-6 px-6 pb-6 flex items-center justify-between border-t border-gray-200 pt-6">
            <div className="text-sm text-gray-700">
              Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, registrations.total_count)} of {registrations.total_count} registrations
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={!registrations.has_prev}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              <span className="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md">
                Page {currentPage}
              </span>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!registrations.has_next}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminEventRegistrations;