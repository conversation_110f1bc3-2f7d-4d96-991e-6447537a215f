/**
 * PaymentStatusPage Component
 * 
 * Standalone page for checking payment status.
 * Useful for tracking payments and providing status updates.
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiArrowLeft,
  FiRefreshCw,
  FiHome,
  FiCalendar,
  FiUser,
  FiSearch,
  FiAlertCircle
} from 'react-icons/fi';
import { PaymentStatus } from '../../components/payment';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import {
  getPaymentStatus,
  pollPaymentStatus,
  selectPaymentStatus,
  selectStatusLoading,
  selectStatusError,
  selectPollingLoading,
  clearPaymentErrors
} from '../../store/slices/PaymentSlice';

const PaymentStatusPage = () => {
  const { paymentId: urlPaymentId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  
  // Get payment ID from URL params or query string
  const paymentId = urlPaymentId || searchParams.get('payment_id');
  
  // Local state
  const [manualPaymentId, setManualPaymentId] = useState('');
  const [showManualSearch, setShowManualSearch] = useState(!paymentId);
  const [lastStatusCheck, setLastStatusCheck] = useState(null);

  // Redux selectors
  const paymentStatus = useSelector(selectPaymentStatus);
  const statusLoading = useSelector(selectStatusLoading);
  const statusError = useSelector(selectStatusError);
  const pollingLoading = useSelector(selectPollingLoading);

  // Load payment status on mount
  useEffect(() => {
    if (paymentId) {
      dispatch(clearPaymentErrors());
      dispatch(getPaymentStatus(paymentId));
      setLastStatusCheck(Date.now());
    }
  }, [dispatch, paymentId]);

  // Handle manual payment ID search
  const handleManualSearch = (e) => {
    e.preventDefault();
    if (manualPaymentId.trim()) {
      navigate(`/payment/status/${manualPaymentId.trim()}`);
    }
  };

  // Handle refresh status
  const handleRefreshStatus = () => {
    if (paymentId) {
      dispatch(getPaymentStatus(paymentId));
      setLastStatusCheck(Date.now());
    }
  };

  // Handle start polling
  const handleStartPolling = () => {
    if (paymentId) {
      dispatch(pollPaymentStatus({ paymentId, maxAttempts: 20, interval: 3000 }));
    }
  };

  // Handle status change
  const handleStatusChange = (status) => {
    setLastStatusCheck(Date.now());
    
    // Navigate to appropriate page based on status
    if (status.status === 'completed') {
      const searchParams = new URLSearchParams();
      searchParams.set('payment_id', paymentId);
      navigate(`/payment/success?${searchParams.toString()}`);
    } else if (status.status === 'failed') {
      const searchParams = new URLSearchParams();
      searchParams.set('payment_id', paymentId);
      navigate(`/payment/cancel?${searchParams.toString()}`);
    }
  };

  // Handle navigation
  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoToEvents = () => {
    navigate('/events');
  };

  const handleGoToProfile = () => {
    const userRole = localStorage.getItem('role')?.toLowerCase();
    if (userRole) {
      navigate(`/${userRole}/dashboard`);
    } else {
      navigate('/profile');
    }
  };

  // Format time since last check
  const getTimeSinceLastCheck = () => {
    if (!lastStatusCheck) return '';
    
    const seconds = Math.floor((Date.now() - lastStatusCheck) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleGoBack}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FiArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Payment Status
                </h1>
                <p className="text-sm text-gray-500">
                  {paymentId ? `Tracking payment ${paymentId.slice(-8)}` : 'Check payment status'}
                </p>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={handleGoToEvents}
                className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FiCalendar className="w-4 h-4" />
                <span>Events</span>
              </button>
              
              <button
                onClick={handleGoToProfile}
                className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FiUser className="w-4 h-4" />
                <span>Profile</span>
              </button>
              
              <button
                onClick={handleGoHome}
                className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiHome className="w-4 h-4" />
                <span>Home</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Manual Payment ID Search */}
        {showManualSearch && (
          <div className="mb-8">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <FiSearch className="w-5 h-5" />
                <span>Check Payment Status</span>
              </h2>
              
              <form onSubmit={handleManualSearch} className="flex space-x-3">
                <div className="flex-1">
                  <input
                    type="text"
                    value={manualPaymentId}
                    onChange={(e) => setManualPaymentId(e.target.value)}
                    placeholder="Enter payment ID (e.g., pay_1234567890abcdef)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <button
                  type="submit"
                  disabled={!manualPaymentId.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <FiSearch className="w-4 h-4" />
                  <span>Check Status</span>
                </button>
              </form>
              
              <p className="text-sm text-gray-500 mt-2">
                You can find your payment ID in your confirmation email or receipt.
              </p>
            </div>
          </div>
        )}

        {/* Payment Status Display */}
        {paymentId && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {statusError && !paymentStatus ? (
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="text-center">
                    <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Payment Not Found
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {statusError}
                    </p>
                    <div className="flex space-x-3 justify-center">
                      <button
                        onClick={handleRefreshStatus}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Try Again
                      </button>
                      <button
                        onClick={() => setShowManualSearch(true)}
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        Search Different Payment
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <PaymentStatus
                  paymentId={paymentId}
                  onStatusChange={handleStatusChange}
                  autoRefresh={true}
                  refreshInterval={10000}
                />
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Status Controls */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Status Controls
                </h3>
                
                <div className="space-y-3">
                  <button
                    onClick={handleRefreshStatus}
                    disabled={statusLoading}
                    className="w-full flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                  >
                    <FiRefreshCw className={`w-4 h-4 ${statusLoading ? 'animate-spin' : ''}`} />
                    <span>Refresh Status</span>
                  </button>
                  
                  {paymentStatus?.status === 'pending' && (
                    <button
                      onClick={handleStartPolling}
                      disabled={pollingLoading}
                      className="w-full flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                    >
                      {pollingLoading ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        <FiRefreshCw className="w-4 h-4" />
                      )}
                      <span>
                        {pollingLoading ? 'Checking...' : 'Auto-Check Status'}
                      </span>
                    </button>
                  )}
                  
                  <button
                    onClick={() => setShowManualSearch(true)}
                    className="w-full flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FiSearch className="w-4 h-4" />
                    <span>Check Different Payment</span>
                  </button>
                </div>
                
                {lastStatusCheck && (
                  <p className="text-xs text-gray-500 mt-3 text-center">
                    Last checked: {getTimeSinceLastCheck()}
                  </p>
                )}
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Quick Actions
                </h3>
                
                <div className="space-y-3">
                  <button
                    onClick={handleGoToEvents}
                    className="w-full flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FiCalendar className="w-4 h-4" />
                    <span>Browse Events</span>
                  </button>
                  
                  <button
                    onClick={handleGoToProfile}
                    className="w-full flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FiUser className="w-4 h-4" />
                    <span>My Account</span>
                  </button>
                  
                  <button
                    onClick={handleGoHome}
                    className="w-full flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FiHome className="w-4 h-4" />
                    <span>Homepage</span>
                  </button>
                </div>
              </div>

              {/* Help */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-semibold text-blue-800 mb-2">
                  Payment Help
                </h4>
                <div className="text-sm text-blue-700 space-y-2">
                  <p>• Payments typically process within 5-10 minutes</p>
                  <p>• Check your email for payment confirmations</p>
                  <p>• Contact support if status doesn't update</p>
                </div>
                
                <div className="mt-3 pt-3 border-t border-blue-200 space-y-1 text-sm text-blue-600">
                  <p>Email: <EMAIL></p>
                  <p>Phone: +27 12 345 6789</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentStatusPage;
