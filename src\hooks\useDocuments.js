/**
 * useDocuments Hook
 * React hook for document operations using the new document fetch API
 */

import { useState, useCallback } from 'react';
import {
  fetchDocument,
  downloadDocument,
  displayDocument,
  getDocumentPreview,
  canPreviewDocument
} from '../services/documentService';

export const useDocuments = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [documentData, setDocumentData] = useState(null);

  /**
   * Fetch document data
   */
  const fetchDocumentData = useCallback(async (documentPath) => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await fetchDocument(documentPath);
      setDocumentData(data);
      return data;
    } catch (err) {
      setError(err.message || 'Failed to fetch document');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Download document
   */
  const downloadDocumentFile = useCallback(async (documentPath, filename = null) => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await downloadDocument(documentPath, filename);
      return data;
    } catch (err) {
      setError(err.message || 'Failed to download document');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Display document in browser or download
   */
  const displayDocumentFile = useCallback(async (documentPath, container = null) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await displayDocument(documentPath, container);
      return result;
    } catch (err) {
      setError(err.message || 'Failed to display document');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get document preview data
   */
  const getPreview = useCallback(async (documentPath) => {
    setLoading(true);
    setError(null);
    
    try {
      const preview = await getDocumentPreview(documentPath);
      return preview;
    } catch (err) {
      setError(err.message || 'Failed to get document preview');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Check if document can be previewed
   */
  const checkPreviewable = useCallback((mimeType) => {
    return canPreviewDocument(mimeType);
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Clear document data
   */
  const clearDocumentData = useCallback(() => {
    setDocumentData(null);
  }, []);

  return {
    // State
    loading,
    error,
    documentData,
    
    // Actions
    fetchDocumentData,
    downloadDocumentFile,
    displayDocumentFile,
    getPreview,
    checkPreviewable,
    clearError,
    clearDocumentData
  };
};

export default useDocuments;
