import React from 'react';

const AdminEventInfo = ({ event }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Information</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <p className="text-sm font-medium text-gray-900">Event ID</p>
          <p className="text-sm text-gray-600 font-mono">{event.id}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-900">Organizer ID</p>
          <p className="text-sm text-gray-600 font-mono">{event.organizer_id}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-900">Created</p>
          <p className="text-sm text-gray-600">
            {event.created_at ? new Date(event.created_at).toLocaleString() : 'N/A'}
          </p>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-900">Last Updated</p>
          <p className="text-sm text-gray-600">
            {event.updated_at ? new Date(event.updated_at).toLocaleString() : 'N/A'}
          </p>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-900">Total Registrations</p>
          <p className="text-sm text-gray-600">{event.total_registrations || 0}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-900">Available Tickets</p>
          <p className="text-sm text-gray-600">{event.available_tickets || 0}</p>
        </div>
      </div>
    </div>
  );
};

export default AdminEventInfo;
