import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import axios from 'axios';
import API_BASE_URL from '../../utils/api/API_URL';
import { mockEventsApi } from '../../services/mockEventsApi';

// Base URL for events endpoints
const API_BASE = `${API_BASE_URL}/api/events`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Async Thunks for API calls

// 1. Get All Events (Updated to match API docs)
export const fetchPublicEvents = createAsyncThunk(
  'events/fetchPublicEvents',
  async (queryParams = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();

      // Add query parameters based on API documentation
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const res = await axios.get(`${API_BASE}?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });

      // Handle different response structures
      if (res.data && Array.isArray(res.data)) {
        return res.data;
      } else if (res.data && res.data.events && Array.isArray(res.data.events)) {
        return res.data;
      } else {
        console.warn('Unexpected API response structure for public events:', res.data);
        return { events: [], total: 0, page: 1, size: 20, total_pages: 1 };
      }
    } catch (err) {
      // If API fails, use mock data
      console.log('Events API not available, using mock data');
      try {
        const mockData = await mockEventsApi.getEvents(queryParams);
        return {
          events: mockData.events,
          total: mockData.total,
          page: mockData.page,
          size: mockData.limit,
          total_pages: Math.ceil(mockData.total / mockData.limit)
        };
      } catch (mockErr) {
        return rejectWithValue(mockErr.message);
      }
    }
  }
);

// 1a. Get Public Events with User Registration Status - Enhanced for user experience
export const fetchPublicEventsWithUserStatus = createAsyncThunk(
  'events/fetchPublicEventsWithUserStatus',
  async (queryParams = {}, { rejectWithValue, dispatch }) => {
    try {
      // Fetch public events and user's registered events in parallel
      const [publicEventsResult, userEventsResult] = await Promise.allSettled([
        dispatch(fetchPublicEvents(queryParams)).unwrap(),
        dispatch(fetchUserEventList({ page: 1, per_page: 100 })).unwrap()
      ]);

      let publicEvents = [];
      let userEvents = [];

      // Handle public events result
      if (publicEventsResult.status === 'fulfilled') {
        publicEvents = Array.isArray(publicEventsResult.value)
          ? publicEventsResult.value
          : publicEventsResult.value.events || [];
      }

      // Handle user events result
      if (userEventsResult.status === 'fulfilled') {
        userEvents = userEventsResult.value.events || [];
      }

      // Create a map of user's registered event IDs for quick lookup
      const userEventIds = new Set(userEvents.map(event => event.event_id));

      // Enhance public events with registration status
      const enhancedEvents = publicEvents.map(event => ({
        ...event,
        isUserRegistered: userEventIds.has(event.id),
        userRegistrationStatus: userEvents.find(userEvent => userEvent.event_id === event.id)?.registration_status || null,
        userPaymentStatus: userEvents.find(userEvent => userEvent.event_id === event.id)?.payment_status || null
      }));

      return {
        events: enhancedEvents,
        userRegisteredEvents: userEvents,
        total: publicEventsResult.status === 'fulfilled' ?
          (publicEventsResult.value.total || enhancedEvents.length) : 0
      };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get Event Details (Updated to match API docs)
export const fetchEventDetails = createAsyncThunk(
  'events/fetchEventDetails',
  async (eventId, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/${eventId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Get Featured Events (Removed - not in API docs)
export const fetchFeaturedEvents = createAsyncThunk(
  'events/fetchFeaturedEvents',
  async ({ limit = 5 } = {}, { rejectWithValue }) => {
    try {
      // Filter events by featured status using the main events endpoint
      const res = await axios.get(`${API_BASE}?limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      // Filter featured events on frontend side
      const featuredEvents = res.data.events?.filter(event => event.is_featured) || [];
      return { events: featuredEvents, total: featuredEvents.length };
    } catch (err) {
      // If API fails, use mock data
      console.log('Featured events API not available, using mock data');
      try {
        const mockData = await mockEventsApi.getEvents({ is_featured: true, limit });
        return { events: mockData.events, total: mockData.total };
      } catch (mockErr) {
        return rejectWithValue(mockErr.message);
      }
    }
  }
);

// 4. Search Events (Removed - not in API docs, use filtering instead)
export const searchEvents = createAsyncThunk(
  'events/searchEvents',
  async ({ q, limit = 20 }, { rejectWithValue }) => {
    try {
      // Use the main events endpoint and filter on frontend
      const res = await axios.get(`${API_BASE}?limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      // Filter events by search query on frontend side
      const filteredEvents = res.data.events?.filter(event =>
        event.title?.toLowerCase().includes(q.toLowerCase()) ||
        event.description?.toLowerCase().includes(q.toLowerCase()) ||
        event.category?.toLowerCase().includes(q.toLowerCase())
      ) || [];
      return { events: filteredEvents, total: filteredEvents.length };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Create Event (Teachers Only) - Updated to match API docs
export const createEvent = createAsyncThunk(
  'events/createEvent',
  async (eventData, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}`, eventData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 6. Get My Events (Institutes Only) - Updated to use correct my-events endpoint
export const fetchMyEvents = createAsyncThunk(
  'events/fetchMyEvents',
  async ({ skip = 0, limit = 10 } = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({ skip: skip.toString(), limit: limit.toString() });
      // Use the correct my-events endpoint for institutes
      const res = await axios.get(`${API_BASE}/my-events?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 7. Update Event (Organizer Only)
export const updateEvent = createAsyncThunk(
  'events/updateEvent',
  async ({ eventId, eventData }, { rejectWithValue }) => {
    try {
      const res = await axios.put(`${API_BASE}/${eventId}`, eventData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 8. Register for Event
// NOTE: This endpoint is not yet implemented in the backend API
// Using mock API until backend implementation is complete
export const registerForEvent = createAsyncThunk(
  'events/registerForEvent',
  async ({ eventId, registrationData }, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/${eventId}/register`, registrationData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      // If API fails, use mock registration
      console.log('Registration API not available, using mock registration');
      try {
        const mockResult = await mockEventsApi.registerForEvent(eventId, registrationData);
        return mockResult;
      } catch (mockErr) {
        return rejectWithValue(mockErr.message);
      }
    }
  }
);

// 9. Get My Registrations - Updated to match API docs
export const fetchMyRegistrations = createAsyncThunk(
  'events/fetchMyRegistrations',
  async ({ page = 1, limit = 10, status } = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
      if (status) params.append('status', status);

      const res = await axios.get(`${API_BASE}/my-registrations?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 10. Admin Get Event Details
export const fetchAdminEventDetails = createAsyncThunk(
  'events/fetchAdminEventDetails',
  async (eventId, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE_URL}/api/admin/events/${eventId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 11. Admin Delete Event
export const deleteAdminEvent = createAsyncThunk(
  'events/deleteAdminEvent',
  async ({ eventId, force = false }, { rejectWithValue }) => {
    try {
      const res = await axios.delete(`${API_BASE_URL}/api/admin/events/${eventId}?force=${force}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return { eventId, message: res.data.message };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 12. Admin Get Event Registrations
export const fetchAdminEventRegistrations = createAsyncThunk(
  'events/fetchAdminEventRegistrations',
  async ({ eventId, skip = 0, limit = 20, status_filter = null }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString()
      });
      if (status_filter) {
        params.append('status_filter', status_filter);
      }

      const res = await axios.get(`${API_BASE_URL}/api/admin/events/${eventId}/registrations?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 13. Delete Event (Admin/Organizer Only) - Keep for backward compatibility
export const deleteEvent = createAsyncThunk(
  'events/deleteEvent',
  async (eventId, { rejectWithValue }) => {
    try {
      const res = await axios.delete(`${API_BASE}/${eventId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return { eventId, message: res.data.message };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 11. Cancel Registration
export const cancelRegistration = createAsyncThunk(
  'events/cancelRegistration',
  async (eventId, { rejectWithValue }) => {
    try {
      const res = await axios.delete(`${API_BASE}/${eventId}/register`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return { eventId, message: res.data.message };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 11. Register for Event with Payment
export const registerForEventWithPayment = createAsyncThunk(
  'events/registerForEventWithPayment',
  async ({ eventId, registrationData, paymentData }, { rejectWithValue, dispatch }) => {
    try {
      // First, register for the event
      const registrationRes = await axios.post(`${API_BASE}/${eventId}/register`, registrationData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });

      const registration = registrationRes.data;

      // If event has a registration fee, create payment
      if (registration.payment_amount && registration.payment_amount > 0) {
        const paymentPayload = {
          registration_id: registration.registration_id,
          amount: registration.payment_amount,
          currency: registration.currency || 'ZAR',
          user_email: paymentData.user_email,
          user_name: paymentData.user_name,
          return_url: paymentData.return_url,
          cancel_url: paymentData.cancel_url
        };

        // Import payment action dynamically to avoid circular dependency
        const { createEventPayment } = await import('./PaymentSlice');
        const paymentResult = await dispatch(createEventPayment(paymentPayload)).unwrap();

        return {
          registration,
          payment: paymentResult,
          requiresPayment: true
        };
      }

      // No payment required
      return {
        registration,
        payment: null,
        requiresPayment: false
      };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 12. Check Registration Payment Status
export const checkRegistrationPaymentStatus = createAsyncThunk(
  'events/checkRegistrationPaymentStatus',
  async ({ registrationId, paymentId }, { rejectWithValue }) => {
    try {
      // Check both registration and payment status
      const [registrationRes, paymentRes] = await Promise.all([
        axios.get(`${API_BASE}/registrations/${registrationId}`, {
          headers: { Authorization: `Bearer ${getAuthToken()}` }
        }),
        paymentId ? axios.get(`${API_BASE_URL}/api/payments/payfast/payment/${paymentId}/status`, {
          headers: { Authorization: `Bearer ${getAuthToken()}` }
        }) : Promise.resolve(null)
      ]);

      return {
        registration: registrationRes.data,
        payment: paymentRes?.data || null
      };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// ===== UNIVERSAL EVENT ACCESS SECTION =====
// These APIs provide universal access to event data across all user types

// 13. Get User Event Dashboard - Universal access to user's event overview
export const fetchUserEventDashboard = createAsyncThunk(
  'events/fetchUserEventDashboard',
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE_URL}/api/universal-event-access/dashboard`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 14. Get User Event List - Universal access to user's events with filtering
export const fetchUserEventList = createAsyncThunk(
  'events/fetchUserEventList',
  async ({ page = 1, per_page = 10, status = null, category = null, search = null } = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: per_page.toString()
      });

      if (status) params.append('status', status);
      if (category) params.append('category', category);
      if (search) params.append('search', search);

      const res = await axios.get(`${API_BASE_URL}/api/universal-event-access/events?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 15. Get User Event Details - Universal access to detailed event information
export const fetchUserEventDetails = createAsyncThunk(
  'events/fetchUserEventDetails',
  async (eventId, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE_URL}/api/universal-event-access/events/${eventId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 16. Get User Event Statistics - Universal access to user's event participation stats
export const fetchUserEventStats = createAsyncThunk(
  'events/fetchUserEventStats',
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE_URL}/api/universal-event-access/stats`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 17. Update User Event Registration - Universal access to modify registration
export const updateUserEventRegistration = createAsyncThunk(
  'events/updateUserEventRegistration',
  async ({ eventId, registrationData }, { rejectWithValue }) => {
    try {
      const res = await axios.put(`${API_BASE_URL}/api/universal-event-access/events/${eventId}/registration`, registrationData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 18. Cancel User Event Registration - Universal access to cancel registration
export const cancelUserEventRegistration = createAsyncThunk(
  'events/cancelUserEventRegistration',
  async (eventId, { rejectWithValue }) => {
    try {
      const res = await axios.delete(`${API_BASE_URL}/api/universal-event-access/events/${eventId}/registration`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return { eventId, message: res.data.message };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 19. Get User's Registered Events for Public View - Quick access to user's events
export const fetchUserRegisteredEventsForPublic = createAsyncThunk(
  'events/fetchUserRegisteredEventsForPublic',
  async ({ status = null, upcoming_only = true } = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({
        page: '1',
        per_page: '50' // Get more events for public view
      });

      if (status) params.append('status', status);
      if (upcoming_only) {
        // Filter for upcoming events only
        const now = new Date().toISOString();
        params.append('start_date_after', now);
      }

      const res = await axios.get(`${API_BASE_URL}/api/universal-event-access/events?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 20. Check User Registration Status for Multiple Events - Batch check for public events
export const checkUserRegistrationStatus = createAsyncThunk(
  'events/checkUserRegistrationStatus',
  async (eventIds, { rejectWithValue }) => {
    try {
      // Get user's registered events and create a lookup map
      const res = await axios.get(`${API_BASE_URL}/api/universal-event-access/events?per_page=100`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });

      const userEvents = res.data.events || [];
      const registrationMap = {};

      userEvents.forEach(event => {
        registrationMap[event.event_id] = {
          isRegistered: true,
          registrationStatus: event.registration_status,
          paymentStatus: event.payment_status,
          registrationId: event.registration_id,
          registeredAt: event.registered_at
        };
      });

      // Check each requested event ID
      const results = {};
      eventIds.forEach(eventId => {
        results[eventId] = registrationMap[eventId] || {
          isRegistered: false,
          registrationStatus: null,
          paymentStatus: null,
          registrationId: null,
          registeredAt: null
        };
      });

      return results;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  // Public events
  publicEvents: [],
  publicEventsLoading: false,
  publicEventsError: null,
  publicEventsPagination: {
    total: 0,
    page: 1,
    size: 20,
    total_pages: 1
  },

  // Public events with user registration status
  publicEventsWithUserStatus: [],
  publicEventsWithUserStatusLoading: false,
  publicEventsWithUserStatusError: null,
  userRegisteredEventsForPublic: [],

  // User registration status checking
  userRegistrationStatusMap: {},
  userRegistrationStatusLoading: false,
  userRegistrationStatusError: null,

  // Featured events
  featuredEvents: [],
  featuredEventsLoading: false,
  featuredEventsError: null,

  // Event details
  currentEvent: null,
  eventDetailsLoading: false,
  eventDetailsError: null,

  // Search results
  searchResults: [],
  searchLoading: false,
  searchError: null,
  searchQuery: '',

  // My events (for teachers)
  myEvents: [],
  myEventsLoading: false,
  myEventsError: null,
  myEventsPagination: {
    total: 0,
    page: 1,
    size: 10,
    total_pages: 1
  },

  // My registrations
  myRegistrations: [],
  myRegistrationsLoading: false,
  myRegistrationsError: null,
  myRegistrationsPagination: {
    total: 0,
    page: 1,
    size: 10,
    total_pages: 1
  },

  // Event creation/update
  createLoading: false,
  createError: null,
  createSuccess: false,
  updateLoading: false,
  updateError: null,
  updateSuccess: false,
  deleteLoading: false,
  deleteError: null,
  deleteSuccess: false,

  // Admin-specific states
  adminEventDetailsLoading: false,
  adminEventDetailsError: null,
  adminDeleteLoading: false,
  adminDeleteError: null,
  adminDeleteSuccess: false,
  adminEventRegistrations: [],
  adminEventRegistrationsLoading: false,
  adminEventRegistrationsError: null,
  adminEventRegistrationsPagination: {
    total: 0,
    skip: 0,
    limit: 20
  },

  // Registration
  registrationLoading: false,
  registrationError: null,
  registrationSuccess: false,

  // Payment-enabled registration
  paymentRegistrationLoading: false,
  paymentRegistrationError: null,
  paymentRegistrationSuccess: false,
  currentRegistration: null,

  // Registration payment status
  registrationPaymentStatusLoading: false,
  registrationPaymentStatusError: null,

  // Universal Event Access states
  userEventDashboard: null,
  userEventDashboardLoading: false,
  userEventDashboardError: null,

  userEventList: [],
  userEventListLoading: false,
  userEventListError: null,
  userEventListPagination: {
    page: 1,
    per_page: 10,
    total_count: 0,
    has_next: false,
    has_prev: false
  },

  userEventDetails: null,
  userEventDetailsLoading: false,
  userEventDetailsError: null,

  userEventStats: null,
  userEventStatsLoading: false,
  userEventStatsError: null,

  updateUserRegistrationLoading: false,
  updateUserRegistrationError: null,
  updateUserRegistrationSuccess: false,

  cancelUserRegistrationLoading: false,
  cancelUserRegistrationError: null,
  cancelUserRegistrationSuccess: false,

  // UI state
  selectedEvent: null,
  showEventDetails: false,
  showRegistrationModal: false,
  showPaymentModal: false,
  filters: {
    category_id: null,
    location_id: null,
    is_featured: null,
    is_competition: null,
    search: ''
  }
};

// Events Slice
const eventsSlice = createSlice({
  name: 'events',
  initialState,
  reducers: {
    // Update filters
    updateFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },

    // Reset filters
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },

    // Select event for details view
    selectEvent: (state, action) => {
      state.selectedEvent = action.payload;
      state.showEventDetails = true;
    },

    // Close event details
    closeEventDetails: (state) => {
      state.selectedEvent = null;
      state.showEventDetails = false;
    },

    // Set registration modal visibility
    setRegistrationModalVisible: (state, action) => {
      state.showRegistrationModal = action.payload;
    },

    // Set payment modal visibility
    setPaymentModalVisible: (state, action) => {
      state.showPaymentModal = action.payload;
    },

    // Clear errors
    clearErrors: (state) => {
      state.publicEventsError = null;
      state.featuredEventsError = null;
      state.eventDetailsError = null;
      state.searchError = null;
      state.myEventsError = null;
      state.myRegistrationsError = null;
      state.createError = null;
      state.updateError = null;
      state.deleteError = null;
      state.adminEventDetailsError = null;
      state.adminDeleteError = null;
      state.adminEventRegistrationsError = null;
      state.registrationError = null;
      state.paymentRegistrationError = null;
      state.registrationPaymentStatusError = null;
      // Public events with user status errors
      state.publicEventsWithUserStatusError = null;
      state.userRegistrationStatusError = null;
      // Universal Event Access errors
      state.userEventDashboardError = null;
      state.userEventListError = null;
      state.userEventDetailsError = null;
      state.userEventStatsError = null;
      state.updateUserRegistrationError = null;
      state.cancelUserRegistrationError = null;
    },

    // Clear success states
    clearSuccessStates: (state) => {
      state.createSuccess = false;
      state.updateSuccess = false;
      state.registrationSuccess = false;
      state.paymentRegistrationSuccess = false;
      // Universal Event Access success states
      state.updateUserRegistrationSuccess = false;
      state.cancelUserRegistrationSuccess = false;
    },

    // Set current registration
    setCurrentRegistration: (state, action) => {
      state.currentRegistration = action.payload;
    },

    // Clear current registration
    clearCurrentRegistration: (state) => {
      state.currentRegistration = null;
    },

    // Reset events state
    resetEventsState: () => {
      return initialState;
    },

    // Set search query
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Public Events
      .addCase(fetchPublicEvents.pending, (state) => {
        state.publicEventsLoading = true;
        state.publicEventsError = null;
      })
      .addCase(fetchPublicEvents.fulfilled, (state, action) => {
        state.publicEventsLoading = false;
        // Handle both direct array and nested object responses
        if (Array.isArray(action.payload)) {
          state.publicEvents = action.payload;
        } else if (action.payload && Array.isArray(action.payload.events)) {
          state.publicEvents = action.payload.events;
          state.publicEventsPagination = {
            total: action.payload.total || 0,
            page: action.payload.page || 1,
            size: action.payload.size || 20,
            total_pages: action.payload.total_pages || 1
          };
        } else {
          console.warn('Unexpected public events API response:', action.payload);
          state.publicEvents = [];
        }
      })
      .addCase(fetchPublicEvents.rejected, (state, action) => {
        state.publicEventsLoading = false;
        state.publicEventsError = action.payload;
      })

      // Fetch Public Events with User Status
      .addCase(fetchPublicEventsWithUserStatus.pending, (state) => {
        state.publicEventsWithUserStatusLoading = true;
        state.publicEventsWithUserStatusError = null;
      })
      .addCase(fetchPublicEventsWithUserStatus.fulfilled, (state, action) => {
        state.publicEventsWithUserStatusLoading = false;
        state.publicEventsWithUserStatus = action.payload.events || [];
        state.userRegisteredEventsForPublic = action.payload.userRegisteredEvents || [];
        // Also update regular public events for backward compatibility
        state.publicEvents = action.payload.events || [];
        state.publicEventsPagination = {
          total: action.payload.total || 0,
          page: 1,
          size: 20,
          total_pages: Math.ceil((action.payload.total || 0) / 20)
        };
      })
      .addCase(fetchPublicEventsWithUserStatus.rejected, (state, action) => {
        state.publicEventsWithUserStatusLoading = false;
        state.publicEventsWithUserStatusError = action.payload;
      })

      // Fetch User Registered Events for Public
      .addCase(fetchUserRegisteredEventsForPublic.fulfilled, (state, action) => {
        state.userRegisteredEventsForPublic = action.payload.events || [];
      })

      // Check User Registration Status
      .addCase(checkUserRegistrationStatus.pending, (state) => {
        state.userRegistrationStatusLoading = true;
        state.userRegistrationStatusError = null;
      })
      .addCase(checkUserRegistrationStatus.fulfilled, (state, action) => {
        state.userRegistrationStatusLoading = false;
        state.userRegistrationStatusMap = action.payload;
      })
      .addCase(checkUserRegistrationStatus.rejected, (state, action) => {
        state.userRegistrationStatusLoading = false;
        state.userRegistrationStatusError = action.payload;
      })

      // Fetch Event Details
      .addCase(fetchEventDetails.pending, (state) => {
        state.eventDetailsLoading = true;
        state.eventDetailsError = null;
      })
      .addCase(fetchEventDetails.fulfilled, (state, action) => {
        state.eventDetailsLoading = false;
        state.currentEvent = action.payload;
      })
      .addCase(fetchEventDetails.rejected, (state, action) => {
        state.eventDetailsLoading = false;
        state.eventDetailsError = action.payload;
      })

      // Fetch Featured Events
      .addCase(fetchFeaturedEvents.pending, (state) => {
        state.featuredEventsLoading = true;
        state.featuredEventsError = null;
      })
      .addCase(fetchFeaturedEvents.fulfilled, (state, action) => {
        state.featuredEventsLoading = false;
        // Handle both direct array and nested object responses
        if (Array.isArray(action.payload)) {
          state.featuredEvents = action.payload;
        } else if (action.payload && Array.isArray(action.payload.events)) {
          state.featuredEvents = action.payload.events;
        } else {
          console.warn('Unexpected featured events API response:', action.payload);
          state.featuredEvents = [];
        }
      })
      .addCase(fetchFeaturedEvents.rejected, (state, action) => {
        state.featuredEventsLoading = false;
        state.featuredEventsError = action.payload;
      })

      // Search Events
      .addCase(searchEvents.pending, (state) => {
        state.searchLoading = true;
        state.searchError = null;
      })
      .addCase(searchEvents.fulfilled, (state, action) => {
        state.searchLoading = false;
        state.searchResults = action.payload;
      })
      .addCase(searchEvents.rejected, (state, action) => {
        state.searchLoading = false;
        state.searchError = action.payload;
      })

      // Create Event
      .addCase(createEvent.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
        state.createSuccess = false;
      })
      .addCase(createEvent.fulfilled, (state, action) => {
        state.createLoading = false;
        state.createSuccess = true;
        // Add the new event to myEvents if it exists
        if (state.myEvents) {
          state.myEvents.unshift(action.payload);
        }
      })
      .addCase(createEvent.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Fetch My Events
      .addCase(fetchMyEvents.pending, (state) => {
        state.myEventsLoading = true;
        state.myEventsError = null;
      })
      .addCase(fetchMyEvents.fulfilled, (state, action) => {
        state.myEventsLoading = false;

        // Handle both array response and object response
        if (Array.isArray(action.payload)) {
          // Direct array response from API
          state.myEvents = action.payload;
          state.myEventsPagination = {
            total: action.payload.length,
            page: 1,
            size: action.payload.length,
            total_pages: 1
          };
        } else {
          // Object response with pagination info
          state.myEvents = action.payload.events || action.payload.data || [];
          state.myEventsPagination = {
            total: action.payload.total || 0,
            page: action.payload.page || 1,
            size: action.payload.size || action.payload.limit || 20,
            total_pages: action.payload.total_pages || 1
          };
        }
      })
      .addCase(fetchMyEvents.rejected, (state, action) => {
        state.myEventsLoading = false;
        state.myEventsError = action.payload;
      })

      // Update Event
      .addCase(updateEvent.pending, (state) => {
        state.updateLoading = true;
        state.updateError = null;
        state.updateSuccess = false;
      })
      .addCase(updateEvent.fulfilled, (state, action) => {
        state.updateLoading = false;
        state.updateSuccess = true;
        // Update the event in myEvents if it exists
        const index = state.myEvents.findIndex(event => event.id === action.payload.id);
        if (index !== -1) {
          state.myEvents[index] = action.payload;
        }
        // Update currentEvent if it's the same event
        if (state.currentEvent?.id === action.payload.id) {
          state.currentEvent = action.payload;
        }
      })
      .addCase(updateEvent.rejected, (state, action) => {
        state.updateLoading = false;
        state.updateError = action.payload;
      })

      // Delete Event
      .addCase(deleteEvent.pending, (state) => {
        state.deleteLoading = true;
        state.deleteError = null;
        state.deleteSuccess = false;
      })
      .addCase(deleteEvent.fulfilled, (state, action) => {
        state.deleteLoading = false;
        state.deleteSuccess = true;
        // Remove from publicEvents
        state.publicEvents = state.publicEvents.filter(event => event.id !== action.payload.eventId);
        // Remove from myEvents if it exists
        state.myEvents = state.myEvents.filter(event => event.id !== action.payload.eventId);
        // Clear currentEvent if it's the deleted event
        if (state.currentEvent?.id === action.payload.eventId) {
          state.currentEvent = null;
        }
      })
      .addCase(deleteEvent.rejected, (state, action) => {
        state.deleteLoading = false;
        state.deleteError = action.payload;
      })

      // Register for Event
      .addCase(registerForEvent.pending, (state) => {
        state.registrationLoading = true;
        state.registrationError = null;
        state.registrationSuccess = false;
      })
      .addCase(registerForEvent.fulfilled, (state, action) => {
        state.registrationLoading = false;
        state.registrationSuccess = true;
        // Add to myRegistrations if it exists
        if (state.myRegistrations) {
          state.myRegistrations.unshift(action.payload);
        }
      })
      .addCase(registerForEvent.rejected, (state, action) => {
        state.registrationLoading = false;
        state.registrationError = action.payload;
      })

      // Fetch My Registrations
      .addCase(fetchMyRegistrations.pending, (state) => {
        state.myRegistrationsLoading = true;
        state.myRegistrationsError = null;
      })
      .addCase(fetchMyRegistrations.fulfilled, (state, action) => {
        state.myRegistrationsLoading = false;
        state.myRegistrations = action.payload.registrations;
        state.myRegistrationsPagination = {
          total: action.payload.total,
          page: action.payload.page,
          size: action.payload.size,
          total_pages: action.payload.total_pages
        };
      })
      .addCase(fetchMyRegistrations.rejected, (state, action) => {
        state.myRegistrationsLoading = false;
        state.myRegistrationsError = action.payload;
      })

      // Cancel Registration
      .addCase(cancelRegistration.fulfilled, (state, action) => {
        // Remove from myRegistrations
        state.myRegistrations = state.myRegistrations.filter(
          registration => registration.event_id !== action.payload.eventId
        );
      })

      // Register for Event with Payment
      .addCase(registerForEventWithPayment.pending, (state) => {
        state.paymentRegistrationLoading = true;
        state.paymentRegistrationError = null;
        state.paymentRegistrationSuccess = false;
      })
      .addCase(registerForEventWithPayment.fulfilled, (state, action) => {
        state.paymentRegistrationLoading = false;
        state.paymentRegistrationSuccess = true;
        state.currentRegistration = action.payload.registration;

        // Update my registrations if it exists
        const existingIndex = state.myRegistrations.findIndex(
          reg => reg.event_id === action.payload.registration.event_id
        );
        if (existingIndex >= 0) {
          state.myRegistrations[existingIndex] = action.payload.registration;
        } else {
          state.myRegistrations.unshift(action.payload.registration);
        }
      })
      .addCase(registerForEventWithPayment.rejected, (state, action) => {
        state.paymentRegistrationLoading = false;
        state.paymentRegistrationError = action.payload;
      })

      // Check Registration Payment Status
      .addCase(checkRegistrationPaymentStatus.pending, (state) => {
        state.registrationPaymentStatusLoading = true;
        state.registrationPaymentStatusError = null;
      })
      .addCase(checkRegistrationPaymentStatus.fulfilled, (state, action) => {
        state.registrationPaymentStatusLoading = false;

        // Update current registration if it matches
        if (state.currentRegistration &&
            state.currentRegistration.registration_id === action.payload.registration.registration_id) {
          state.currentRegistration = action.payload.registration;
        }

        // Update in my registrations
        const existingIndex = state.myRegistrations.findIndex(
          reg => reg.registration_id === action.payload.registration.registration_id
        );
        if (existingIndex >= 0) {
          state.myRegistrations[existingIndex] = action.payload.registration;
        }
      })
      .addCase(checkRegistrationPaymentStatus.rejected, (state, action) => {
        state.registrationPaymentStatusLoading = false;
        state.registrationPaymentStatusError = action.payload;
      })

      // Admin Event Details
      .addCase(fetchAdminEventDetails.pending, (state) => {
        state.adminEventDetailsLoading = true;
        state.adminEventDetailsError = null;
      })
      .addCase(fetchAdminEventDetails.fulfilled, (state, action) => {
        state.adminEventDetailsLoading = false;
        state.currentEvent = action.payload;
      })
      .addCase(fetchAdminEventDetails.rejected, (state, action) => {
        state.adminEventDetailsLoading = false;
        state.adminEventDetailsError = action.payload;
      })

      // Admin Delete Event
      .addCase(deleteAdminEvent.pending, (state) => {
        state.adminDeleteLoading = true;
        state.adminDeleteError = null;
        state.adminDeleteSuccess = false;
      })
      .addCase(deleteAdminEvent.fulfilled, (state, action) => {
        state.adminDeleteLoading = false;
        state.adminDeleteSuccess = true;
        // Remove from publicEvents
        state.publicEvents = state.publicEvents.filter(event => event.id !== action.payload.eventId);
        // Remove from myEvents if it exists
        state.myEvents = state.myEvents.filter(event => event.id !== action.payload.eventId);
        // Clear currentEvent if it's the deleted event
        if (state.currentEvent?.id === action.payload.eventId) {
          state.currentEvent = null;
        }
      })
      .addCase(deleteAdminEvent.rejected, (state, action) => {
        state.adminDeleteLoading = false;
        state.adminDeleteError = action.payload;
      })

      // Admin Event Registrations
      .addCase(fetchAdminEventRegistrations.pending, (state) => {
        state.adminEventRegistrationsLoading = true;
        state.adminEventRegistrationsError = null;
      })
      .addCase(fetchAdminEventRegistrations.fulfilled, (state, action) => {
        state.adminEventRegistrationsLoading = false;
        state.adminEventRegistrations = action.payload;
      })
      .addCase(fetchAdminEventRegistrations.rejected, (state, action) => {
        state.adminEventRegistrationsLoading = false;
        state.adminEventRegistrationsError = action.payload;
      })

      // Universal Event Access - User Event Dashboard
      .addCase(fetchUserEventDashboard.pending, (state) => {
        state.userEventDashboardLoading = true;
        state.userEventDashboardError = null;
      })
      .addCase(fetchUserEventDashboard.fulfilled, (state, action) => {
        state.userEventDashboardLoading = false;
        state.userEventDashboard = action.payload;
      })
      .addCase(fetchUserEventDashboard.rejected, (state, action) => {
        state.userEventDashboardLoading = false;
        state.userEventDashboardError = action.payload;
      })

      // Universal Event Access - User Event List
      .addCase(fetchUserEventList.pending, (state) => {
        state.userEventListLoading = true;
        state.userEventListError = null;
      })
      .addCase(fetchUserEventList.fulfilled, (state, action) => {
        state.userEventListLoading = false;
        state.userEventList = action.payload.events || [];
        state.userEventListPagination = {
          page: action.payload.page || 1,
          per_page: action.payload.per_page || 10,
          total_count: action.payload.total_count || 0,
          has_next: action.payload.has_next || false,
          has_prev: action.payload.has_prev || false
        };
      })
      .addCase(fetchUserEventList.rejected, (state, action) => {
        state.userEventListLoading = false;
        state.userEventListError = action.payload;
      })

      // Universal Event Access - User Event Details
      .addCase(fetchUserEventDetails.pending, (state) => {
        state.userEventDetailsLoading = true;
        state.userEventDetailsError = null;
      })
      .addCase(fetchUserEventDetails.fulfilled, (state, action) => {
        state.userEventDetailsLoading = false;
        state.userEventDetails = action.payload;
      })
      .addCase(fetchUserEventDetails.rejected, (state, action) => {
        state.userEventDetailsLoading = false;
        state.userEventDetailsError = action.payload;
      })

      // Universal Event Access - User Event Stats
      .addCase(fetchUserEventStats.pending, (state) => {
        state.userEventStatsLoading = true;
        state.userEventStatsError = null;
      })
      .addCase(fetchUserEventStats.fulfilled, (state, action) => {
        state.userEventStatsLoading = false;
        state.userEventStats = action.payload;
      })
      .addCase(fetchUserEventStats.rejected, (state, action) => {
        state.userEventStatsLoading = false;
        state.userEventStatsError = action.payload;
      })

      // Universal Event Access - Update User Registration
      .addCase(updateUserEventRegistration.pending, (state) => {
        state.updateUserRegistrationLoading = true;
        state.updateUserRegistrationError = null;
        state.updateUserRegistrationSuccess = false;
      })
      .addCase(updateUserEventRegistration.fulfilled, (state, action) => {
        state.updateUserRegistrationLoading = false;
        state.updateUserRegistrationSuccess = true;

        // Update the registration in userEventList if it exists
        const eventIndex = state.userEventList.findIndex(
          event => event.event_id === action.payload.event_id
        );
        if (eventIndex !== -1) {
          state.userEventList[eventIndex] = { ...state.userEventList[eventIndex], ...action.payload };
        }
      })
      .addCase(updateUserEventRegistration.rejected, (state, action) => {
        state.updateUserRegistrationLoading = false;
        state.updateUserRegistrationError = action.payload;
      })

      // Universal Event Access - Cancel User Registration
      .addCase(cancelUserEventRegistration.pending, (state) => {
        state.cancelUserRegistrationLoading = true;
        state.cancelUserRegistrationError = null;
        state.cancelUserRegistrationSuccess = false;
      })
      .addCase(cancelUserEventRegistration.fulfilled, (state, action) => {
        state.cancelUserRegistrationLoading = false;
        state.cancelUserRegistrationSuccess = true;

        // Remove or update the registration in userEventList
        state.userEventList = state.userEventList.filter(
          event => event.event_id !== action.payload.eventId
        );
      })
      .addCase(cancelUserEventRegistration.rejected, (state, action) => {
        state.cancelUserRegistrationLoading = false;
        state.cancelUserRegistrationError = action.payload;
      });
  }
});

// Actions
export const {
  updateFilters,
  resetFilters,
  selectEvent,
  closeEventDetails,
  setRegistrationModalVisible,
  setPaymentModalVisible,
  clearErrors,
  clearSuccessStates,
  resetEventsState,
  setSearchQuery,
  setCurrentRegistration,
  clearCurrentRegistration
} = eventsSlice.actions;

// Selectors
export const selectPublicEvents = (state) => state.events.publicEvents;
export const selectPublicEventsLoading = (state) => state.events.publicEventsLoading;
export const selectPublicEventsError = (state) => state.events.publicEventsError;
export const selectPublicEventsPagination = (state) => state.events.publicEventsPagination;

// Public events with user registration status selectors
export const selectPublicEventsWithUserStatus = (state) => state.events.publicEventsWithUserStatus;
export const selectPublicEventsWithUserStatusLoading = (state) => state.events.publicEventsWithUserStatusLoading;
export const selectPublicEventsWithUserStatusError = (state) => state.events.publicEventsWithUserStatusError;
export const selectUserRegisteredEventsForPublic = (state) => state.events.userRegisteredEventsForPublic;

// User registration status selectors
export const selectUserRegistrationStatusMap = (state) => state.events.userRegistrationStatusMap;
export const selectUserRegistrationStatusLoading = (state) => state.events.userRegistrationStatusLoading;
export const selectUserRegistrationStatusError = (state) => state.events.userRegistrationStatusError;

export const selectFeaturedEvents = (state) => state.events.featuredEvents;
export const selectFeaturedEventsLoading = (state) => state.events.featuredEventsLoading;
export const selectFeaturedEventsError = (state) => state.events.featuredEventsError;

export const selectCurrentEvent = (state) => state.events.currentEvent;
export const selectEventDetailsLoading = (state) => state.events.eventDetailsLoading;
export const selectEventDetailsError = (state) => state.events.eventDetailsError;

export const selectSearchResults = (state) => state.events.searchResults;
export const selectSearchLoading = (state) => state.events.searchLoading;
export const selectSearchError = (state) => state.events.searchError;
export const selectSearchQuery = (state) => state.events.searchQuery;

export const selectMyEvents = (state) => state.events.myEvents;
export const selectMyEventsLoading = (state) => state.events.myEventsLoading;
export const selectMyEventsError = (state) => state.events.myEventsError;
export const selectMyEventsPagination = (state) => state.events.myEventsPagination;

export const selectMyRegistrations = (state) => state.events.myRegistrations;
export const selectMyRegistrationsLoading = (state) => state.events.myRegistrationsLoading;
export const selectMyRegistrationsError = (state) => state.events.myRegistrationsError;
export const selectMyRegistrationsPagination = (state) => state.events.myRegistrationsPagination;

export const selectCreateLoading = (state) => state.events.createLoading;
export const selectCreateError = (state) => state.events.createError;
export const selectCreateSuccess = (state) => state.events.createSuccess;

export const selectUpdateLoading = (state) => state.events.updateLoading;
export const selectUpdateError = (state) => state.events.updateError;
export const selectUpdateSuccess = (state) => state.events.updateSuccess;

export const selectDeleteLoading = (state) => state.events.deleteLoading;
export const selectDeleteError = (state) => state.events.deleteError;
export const selectDeleteSuccess = (state) => state.events.deleteSuccess;

// Admin selectors
export const selectAdminEventDetailsLoading = (state) => state.events.adminEventDetailsLoading;
export const selectAdminEventDetailsError = (state) => state.events.adminEventDetailsError;

export const selectAdminDeleteLoading = (state) => state.events.adminDeleteLoading;
export const selectAdminDeleteError = (state) => state.events.adminDeleteError;
export const selectAdminDeleteSuccess = (state) => state.events.adminDeleteSuccess;

export const selectAdminEventRegistrations = (state) => state.events.adminEventRegistrations;
export const selectAdminEventRegistrationsLoading = (state) => state.events.adminEventRegistrationsLoading;
export const selectAdminEventRegistrationsError = (state) => state.events.adminEventRegistrationsError;
export const selectAdminEventRegistrationsPagination = (state) => state.events.adminEventRegistrationsPagination;

export const selectRegistrationLoading = (state) => state.events.registrationLoading;
export const selectRegistrationError = (state) => state.events.registrationError;
export const selectRegistrationSuccess = (state) => state.events.registrationSuccess;

// Payment registration selectors
export const selectPaymentRegistrationLoading = (state) => state.events.paymentRegistrationLoading;
export const selectPaymentRegistrationError = (state) => state.events.paymentRegistrationError;
export const selectPaymentRegistrationSuccess = (state) => state.events.paymentRegistrationSuccess;
export const selectCurrentRegistration = (state) => state.events.currentRegistration;

// Registration payment status selectors
export const selectRegistrationPaymentStatusLoading = (state) => state.events.registrationPaymentStatusLoading;
export const selectRegistrationPaymentStatusError = (state) => state.events.registrationPaymentStatusError;

// Universal Event Access selectors
export const selectUserEventDashboard = (state) => state.events.userEventDashboard;
export const selectUserEventDashboardLoading = (state) => state.events.userEventDashboardLoading;
export const selectUserEventDashboardError = (state) => state.events.userEventDashboardError;

export const selectUserEventList = (state) => state.events.userEventList;
export const selectUserEventListLoading = (state) => state.events.userEventListLoading;
export const selectUserEventListError = (state) => state.events.userEventListError;
export const selectUserEventListPagination = (state) => state.events.userEventListPagination;

export const selectUserEventDetails = (state) => state.events.userEventDetails;
export const selectUserEventDetailsLoading = (state) => state.events.userEventDetailsLoading;
export const selectUserEventDetailsError = (state) => state.events.userEventDetailsError;

export const selectUserEventStats = (state) => state.events.userEventStats;
export const selectUserEventStatsLoading = (state) => state.events.userEventStatsLoading;
export const selectUserEventStatsError = (state) => state.events.userEventStatsError;

export const selectUpdateUserRegistrationLoading = (state) => state.events.updateUserRegistrationLoading;
export const selectUpdateUserRegistrationError = (state) => state.events.updateUserRegistrationError;
export const selectUpdateUserRegistrationSuccess = (state) => state.events.updateUserRegistrationSuccess;

export const selectCancelUserRegistrationLoading = (state) => state.events.cancelUserRegistrationLoading;
export const selectCancelUserRegistrationError = (state) => state.events.cancelUserRegistrationError;
export const selectCancelUserRegistrationSuccess = (state) => state.events.cancelUserRegistrationSuccess;

// UI selectors
export const selectSelectedEvent = (state) => state.events.selectedEvent;
export const selectShowEventDetails = (state) => state.events.showEventDetails;
export const selectShowRegistrationModal = (state) => state.events.showRegistrationModal;
export const selectShowPaymentModal = (state) => state.events.showPaymentModal;
export const selectFilters = (state) => state.events.filters;

// Memoized helper selectors
export const selectUpcomingEvents = createSelector(
  [selectPublicEvents],
  (events) => {
    const now = new Date();
    return events.filter(event => new Date(event.start_datetime) > now);
  }
);

export const selectEventsByCategory = (categoryId) => createSelector(
  [selectPublicEvents],
  (events) => {
    return events.filter(event => event.category?.id === categoryId);
  }
);

export const selectCompetitionEvents = createSelector(
  [selectPublicEvents],
  (events) => {
    return events.filter(event => event.is_competition);
  }
);

export const selectIsRegisteredForEvent = (eventId) => (state) => {
  const registrations = state.events.myRegistrations;
  return registrations.some(registration => registration.event_id === eventId);
};

// Universal Event Access helper selectors
export const selectUpcomingUserEvents = createSelector(
  [selectUserEventList],
  (events) => {
    const now = new Date();
    return events.filter(event =>
      new Date(event.event_start_datetime) > now &&
      event.registration_status === 'confirmed'
    );
  }
);

export const selectPastUserEvents = createSelector(
  [selectUserEventList],
  (events) => {
    const now = new Date();
    return events.filter(event =>
      new Date(event.event_end_datetime) < now
    );
  }
);

export const selectUserEventsByStatus = (status) => createSelector(
  [selectUserEventList],
  (events) => {
    return events.filter(event => event.registration_status === status);
  }
);

export const selectUserEventsByCategory = (category) => createSelector(
  [selectUserEventList],
  (events) => {
    return events.filter(event => event.event_category === category);
  }
);

export const selectUserEventsByPaymentStatus = (paymentStatus) => createSelector(
  [selectUserEventList],
  (events) => {
    return events.filter(event => event.payment_status === paymentStatus);
  }
);

// Public Events with User Status helper selectors
export const selectRegisteredPublicEvents = createSelector(
  [selectPublicEventsWithUserStatus],
  (events) => {
    return events.filter(event => event.isUserRegistered);
  }
);

export const selectUnregisteredPublicEvents = createSelector(
  [selectPublicEventsWithUserStatus],
  (events) => {
    return events.filter(event => !event.isUserRegistered);
  }
);

export const selectPublicEventsByRegistrationStatus = (status) => createSelector(
  [selectPublicEventsWithUserStatus],
  (events) => {
    return events.filter(event => event.userRegistrationStatus === status);
  }
);

export const selectUpcomingRegisteredEvents = createSelector(
  [selectPublicEventsWithUserStatus],
  (events) => {
    const now = new Date();
    return events.filter(event =>
      event.isUserRegistered &&
      new Date(event.start_datetime || event.start_date) > now
    );
  }
);

export const selectPendingPaymentEvents = createSelector(
  [selectPublicEventsWithUserStatus],
  (events) => {
    return events.filter(event =>
      event.isUserRegistered &&
      event.userPaymentStatus === 'pending'
    );
  }
);

// Helper function to check if user is registered for a specific event
export const selectIsUserRegisteredForEvent = (eventId) => createSelector(
  [selectUserRegistrationStatusMap, selectPublicEventsWithUserStatus],
  (statusMap, events) => {
    // Check in status map first
    if (statusMap[eventId]) {
      return statusMap[eventId].isRegistered;
    }

    // Fallback to checking in enhanced events
    const event = events.find(e => e.id === eventId);
    return event ? event.isUserRegistered : false;
  }
);

// Get user's registration details for a specific event
export const selectUserRegistrationForEvent = (eventId) => createSelector(
  [selectUserRegistrationStatusMap, selectPublicEventsWithUserStatus],
  (statusMap, events) => {
    // Check in status map first
    if (statusMap[eventId]) {
      return statusMap[eventId];
    }

    // Fallback to checking in enhanced events
    const event = events.find(e => e.id === eventId);
    if (event && event.isUserRegistered) {
      return {
        isRegistered: true,
        registrationStatus: event.userRegistrationStatus,
        paymentStatus: event.userPaymentStatus,
        registrationId: null, // Not available in this view
        registeredAt: null // Not available in this view
      };
    }

    return {
      isRegistered: false,
      registrationStatus: null,
      paymentStatus: null,
      registrationId: null,
      registeredAt: null
    };
  }
);

export default eventsSlice.reducer;
