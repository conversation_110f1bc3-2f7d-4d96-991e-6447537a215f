/**
 * DocumentList Component
 * Simple list of documents with download functionality
 */

import React from 'react';
import DocumentCard from './DocumentCard';
import { useDocuments } from '../../hooks/useDocuments';

const DocumentList = ({ 
  documents = [], 
  onRemove,
  showRemove = false,
  disabled = false,
  layout = 'grid', // 'grid' or 'list'
  className = '',
  emptyMessage = 'No documents available'
}) => {
  const { downloadDocumentFile } = useDocuments();

  const handleDownload = async (document) => {
    if (disabled) return;
    
    try {
      const documentPath = document.document_url || document.path;
      const documentName = document.document_name || document.name;
      
      if (documentPath) {
        await downloadDocumentFile(documentPath, documentName);
      }
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleRemove = (index) => {
    if (onRemove && !disabled) {
      onRemove(index);
    }
  };

  if (!documents || documents.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {emptyMessage}
      </div>
    );
  }

  const gridClasses = layout === 'grid' 
    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
    : 'space-y-3';

  return (
    <div className={`${gridClasses} ${className}`}>
      {documents.map((document, index) => (
        <DocumentCard
          key={document.id || index}
          document={document}
          onDownload={() => handleDownload(document)}
          onRemove={() => handleRemove(index)}
          showRemove={showRemove}
          disabled={disabled}
        />
      ))}
    </div>
  );
};

export default DocumentList;
