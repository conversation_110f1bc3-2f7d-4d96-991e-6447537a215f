{"name": "mosaic-react", "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@radix-ui/react-popover": "^1.1.2", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/forms": "^0.5.7", "axios": "^1.10.0", "chart.js": "^4.4.1", "chartjs-adapter-moment": "^1.0.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "moment": "^2.29.4", "react": "^19.1.0", "react-day-picker": "^9.4.1", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-router-dom": "^7.0.2", "react-transition-group": "^4.4.5", "tailwind-merge": "^2.5.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.0", "@vitejs/plugin-react": "^4.2.1", "postcss": "^8.4.32", "tailwindcss": "^4.0.0", "vite": "^6.0.3"}}