import React, { useRef, useEffect } from 'react';
import { cleanVerificationCode } from '../../services/emailVerificationService';

/**
 * VerificationCodeInput Component
 * 
 * A reusable component for entering 6-digit verification codes
 * with automatic focus management and paste support.
 * 
 * @param {Object} props
 * @param {string} props.value - Current verification code value
 * @param {function} props.onChange - Callback when code changes
 * @param {boolean} props.disabled - Whether inputs are disabled
 * @param {boolean} props.error - Whether to show error state
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.autoFocus - Whether to auto-focus first input
 */
const VerificationCodeInput = ({
  value = '',
  onChange,
  disabled = false,
  error = false,
  className = '',
  autoFocus = false,
}) => {
  const inputRefs = useRef([]);
  const codeArray = value.padEnd(6, '').split('').slice(0, 6);

  // Auto-focus first input on mount if autoFocus is true
  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  // Handle individual input change
  const handleInputChange = (index, inputValue) => {
    const cleanValue = inputValue.replace(/\D/g, ''); // Only allow digits
    
    if (cleanValue.length <= 1) {
      const newCodeArray = [...codeArray];
      newCodeArray[index] = cleanValue;
      const newCode = newCodeArray.join('').replace(/\s/g, '');
      
      onChange(newCode);
      
      // Auto-focus next input if value was entered
      if (cleanValue && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  // Handle key down events
  const handleKeyDown = (index, e) => {
    // Handle backspace
    if (e.key === 'Backspace') {
      if (!codeArray[index] && index > 0) {
        // If current input is empty, focus previous input
        inputRefs.current[index - 1]?.focus();
      } else if (codeArray[index]) {
        // If current input has value, clear it
        const newCodeArray = [...codeArray];
        newCodeArray[index] = '';
        const newCode = newCodeArray.join('').replace(/\s/g, '');
        onChange(newCode);
      }
    }
    
    // Handle arrow keys
    if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
    if (e.key === 'ArrowRight' && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle paste
  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const cleanData = cleanVerificationCode(pastedData);
    
    if (cleanData.length <= 6) {
      onChange(cleanData);
      
      // Focus the next empty input or the last input
      const nextIndex = Math.min(cleanData.length, 5);
      inputRefs.current[nextIndex]?.focus();
    }
  };

  // Handle focus
  const handleFocus = (index) => {
    // Select all text when focusing
    inputRefs.current[index]?.select();
  };

  const baseInputClasses = `
    w-12 h-12 text-center text-lg font-semibold rounded-lg
    border-2 transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
  `;

  const getInputClasses = (hasValue) => {
    let classes = baseInputClasses;
    
    if (error) {
      classes += ` border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/20 
                   text-red-900 dark:text-red-100 focus:ring-red-500 focus:border-red-500`;
    } else if (hasValue) {
      classes += ` border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20 
                   text-green-900 dark:text-green-100 focus:ring-green-500 focus:border-green-500`;
    } else {
      classes += ` border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 
                   text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500
                   hover:border-gray-400 dark:hover:border-gray-500`;
    }
    
    return classes;
  };

  return (
    <div className={`flex justify-center space-x-2 ${className}`}>
      {[0, 1, 2, 3, 4, 5].map((index) => (
        <input
          key={index}
          ref={(el) => (inputRefs.current[index] = el)}
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          maxLength="1"
          value={codeArray[index] || ''}
          onChange={(e) => handleInputChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          onPaste={handlePaste}
          onFocus={() => handleFocus(index)}
          disabled={disabled}
          className={getInputClasses(!!codeArray[index])}
          aria-label={`Verification code digit ${index + 1}`}
        />
      ))}
    </div>
  );
};

export default VerificationCodeInput;
