import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchAdminEventDetails,
  deleteAdminEvent,
  selectCurrentEvent,
  selectAdminEventDetailsLoading,
  selectAdminEventDetailsError,
  selectAdminDeleteLoading
} from '../../store/slices/EventsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

// Import smaller components
import AdminEventHeader from '../../components/admin/AdminEventHeader';
import AdminEventDetails from '../../components/admin/AdminEventDetails';
import AdminEventInfo from '../../components/admin/AdminEventInfo';
import AdminEventSections from '../../components/admin/AdminEventSections';
import AdminEventSidebar from '../../components/admin/AdminEventSidebar';
import DeleteEventModal from '../../components/admin/DeleteEventModal';

const AdminEventView = ({ eventId }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const event = useSelector(selectCurrentEvent);
  const loading = useSelector(selectAdminEventDetailsLoading);
  const error = useSelector(selectAdminEventDetailsError);
  const deleteLoading = useSelector(selectAdminDeleteLoading);

  const [showDeleteModal, setShowDeleteModal] = useState(false);

  useEffect(() => {
    if (eventId) {
      dispatch(fetchAdminEventDetails(eventId));
    }
  }, [dispatch, eventId]);

  const handleEdit = () => {
    navigate(`/admin/events/${eventId}/edit`);
  };

  const handleDelete = async () => {
    try {
      await dispatch(deleteAdminEvent({ eventId, force: true })).unwrap();
      navigate('/admin/events');
    } catch (error) {
      console.error('Delete failed:', error);
    }
  };

  const handleBack = () => {
    navigate('/admin/events');
  };

  const handlePublicView = () => {
    window.open(`/events/${eventId}`, '_blank');
  };



  const handleViewAnalytics = () => {
    navigate(`/admin/events/${eventId}/analytics`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  if (!event) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Event not found</h3>
        <p className="text-gray-500 mt-2">The event you're looking for doesn't exist.</p>
        <button
          onClick={handleBack}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Back to Events
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <AdminEventHeader
        event={event}
        onBack={handleBack}
        onEdit={handleEdit}
        onDelete={() => setShowDeleteModal(true)}
        onPublicView={handlePublicView}
      />

      {/* Event Banner */}
      {event.banner_image_url && (
        <div className="mb-8 rounded-2xl overflow-hidden">
          <img
            src={event.banner_image_url}
            alt={event.title}
            className="w-full h-64 object-cover"
          />
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <AdminEventDetails event={event} />
          <AdminEventInfo event={event} />
          <AdminEventSections event={event} />
        </div>

        {/* Sidebar */}
        <AdminEventSidebar
          event={event}
          onEdit={handleEdit}
          onViewAnalytics={handleViewAnalytics}
        />
      </div>

      <DeleteEventModal
        event={event}
        isOpen={showDeleteModal}
        onConfirm={handleDelete}
        onCancel={() => setShowDeleteModal(false)}
        loading={deleteLoading}
      />
    </div>
  );
};

export default AdminEventView;
