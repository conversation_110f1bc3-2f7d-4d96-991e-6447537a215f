import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiCalendar, 
  FiMapPin, 
  FiUsers, 
  FiCheck, 
  FiClock, 
  FiDollarSign,
  FiEye,
  FiX,
  FiAlertCircle
} from 'react-icons/fi';
import {
  fetchPublicEventsWithUserStatus,
  fetchUserRegisteredEventsForPublic,
  checkUserRegistrationStatus,
  selectPublicEventsWithUserStatus,
  selectPublicEventsWithUserStatusLoading,
  selectPublicEventsWithUserStatusError,
  selectRegisteredPublicEvents,
  selectUnregisteredPublicEvents,
  selectUpcomingRegisteredEvents,
  selectPendingPaymentEvents,
  selectIsUserRegisteredForEvent,
  selectUserRegistrationForEvent
} from '../../store/slices/EventsSlice';
import { LoadingSpinner, ErrorMessage } from '../ui';

const PublicEventsWithUserStatus = () => {
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'registered', 'available'
  const [searchQuery, setSearchQuery] = useState('');

  // Selectors
  const events = useSelector(selectPublicEventsWithUserStatus);
  const loading = useSelector(selectPublicEventsWithUserStatusLoading);
  const error = useSelector(selectPublicEventsWithUserStatusError);
  const registeredEvents = useSelector(selectRegisteredPublicEvents);
  const unregisteredEvents = useSelector(selectUnregisteredPublicEvents);
  const upcomingRegistered = useSelector(selectUpcomingRegisteredEvents);
  const pendingPaymentEvents = useSelector(selectPendingPaymentEvents);

  // Load events on component mount
  useEffect(() => {
    dispatch(fetchPublicEventsWithUserStatus());
  }, [dispatch]);

  // Filter events based on active tab and search
  const getFilteredEvents = () => {
    let filteredEvents = [];
    
    switch (activeTab) {
      case 'registered':
        filteredEvents = registeredEvents;
        break;
      case 'available':
        filteredEvents = unregisteredEvents;
        break;
      default:
        filteredEvents = events;
        break;
    }

    // Apply search filter
    if (searchQuery.trim()) {
      filteredEvents = filteredEvents.filter(event =>
        event.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.category?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filteredEvents;
  };

  // Get registration status badge
  const getRegistrationBadge = (event) => {
    if (!event.isUserRegistered) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          <FiUsers className="w-3 h-3 mr-1" />
          Available
        </span>
      );
    }

    const statusColors = {
      confirmed: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      cancelled: 'bg-red-100 text-red-800',
      waitlisted: 'bg-blue-100 text-blue-800'
    };

    const statusIcons = {
      confirmed: FiCheck,
      pending: FiClock,
      cancelled: FiX,
      waitlisted: FiAlertCircle
    };

    const status = event.userRegistrationStatus || 'confirmed';
    const Icon = statusIcons[status] || FiCheck;
    const colorClass = statusColors[status] || statusColors.confirmed;

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  // Get payment status badge
  const getPaymentBadge = (event) => {
    if (!event.isUserRegistered || !event.userPaymentStatus) {
      return null;
    }

    const paymentColors = {
      completed: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      failed: 'bg-red-100 text-red-800',
      refunded: 'bg-gray-100 text-gray-800'
    };

    const status = event.userPaymentStatus;
    const colorClass = paymentColors[status] || paymentColors.pending;

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass} ml-2`}>
        <FiDollarSign className="w-3 h-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-8">
        <ErrorMessage 
          message="Failed to load events" 
          onRetry={() => dispatch(fetchPublicEventsWithUserStatus())}
        />
      </div>
    );
  }

  const filteredEvents = getFilteredEvents();

  return (
    <div className="space-y-6">
      {/* Header with stats */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Events</h2>
        
        {/* Quick stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">{events.length}</div>
            <div className="text-sm text-blue-600">Total Events</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">{registeredEvents.length}</div>
            <div className="text-sm text-green-600">Registered</div>
          </div>
          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-yellow-600">{upcomingRegistered.length}</div>
            <div className="text-sm text-yellow-600">Upcoming</div>
          </div>
          <div className="bg-red-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-red-600">{pendingPaymentEvents.length}</div>
            <div className="text-sm text-red-600">Pending Payment</div>
          </div>
        </div>

        {/* Search and filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search events..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          {/* Tab filters */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {[
              { key: 'all', label: 'All Events', count: events.length },
              { key: 'registered', label: 'My Events', count: registeredEvents.length },
              { key: 'available', label: 'Available', count: unregisteredEvents.length }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.key
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Events list */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredEvents.map(event => (
          <div key={event.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            {/* Event image */}
            {event.banner_image_url && (
              <div className="h-48 bg-gray-200">
                <img 
                  src={event.banner_image_url} 
                  alt={event.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            
            <div className="p-6">
              {/* Event title and badges */}
              <div className="flex items-start justify-between mb-3">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                  {event.title}
                </h3>
              </div>
              
              {/* Registration and payment status */}
              <div className="flex flex-wrap items-center mb-3">
                {getRegistrationBadge(event)}
                {getPaymentBadge(event)}
              </div>

              {/* Event details */}
              <div className="space-y-2 text-sm text-gray-600 mb-4">
                {event.start_datetime && (
                  <div className="flex items-center">
                    <FiCalendar className="w-4 h-4 mr-2" />
                    {new Date(event.start_datetime).toLocaleDateString()}
                  </div>
                )}
                {event.location && (
                  <div className="flex items-center">
                    <FiMapPin className="w-4 h-4 mr-2" />
                    {event.location}
                  </div>
                )}
                {event.category && (
                  <div className="text-blue-600 font-medium">
                    {event.category}
                  </div>
                )}
              </div>

              {/* Description */}
              {event.description && (
                <p className="text-gray-600 text-sm line-clamp-3 mb-4">
                  {event.description}
                </p>
              )}

              {/* Action button */}
              <button className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <FiEye className="w-4 h-4 mr-2" />
                View Details
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Empty state */}
      {filteredEvents.length === 0 && (
        <div className="text-center py-12">
          <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
          <p className="text-gray-600">
            {searchQuery ? 'Try adjusting your search terms.' : 'No events match the selected filters.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default PublicEventsWithUserStatus;
