import React from 'react';
import { FiSearch, FiFilter, FiRefreshCw } from 'react-icons/fi';

const EventFilters = ({
  searchQuery,
  onSearchChange,
  filters,
  onFilterChange,
  showFilters,
  onToggleFilters,
  onClearFilters,
  onReload,
  isLoading = false
}) => {
  const handleSearch = (e) => {
    e.preventDefault();
    // Search is handled by onChange, but we can add additional logic here if needed
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search */}
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search events..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded focus:ring-1 focus:ring-gray-500 focus:border-gray-500 text-gray-900 placeholder-gray-500 bg-white"
            />
          </div>
        </form>

        {/* Action Buttons */}
        <div className="flex gap-2">
          {/* Reload Button */}
          <button
            onClick={onReload}
            disabled={isLoading}
            className={`inline-flex items-center px-4 py-2 rounded text-sm font-medium transition-colors duration-200 ${
              isLoading
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <FiRefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Reload
          </button>

          {/* Filter Toggle */}
          <button
            onClick={onToggleFilters}
            className={`inline-flex items-center px-4 py-2 rounded text-sm font-medium transition-colors duration-200 ${
              showFilters
                ? 'bg-gray-900 text-white'
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <FiFilter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Category
              </label>
              <select
                value={filters.category || ''}
                onChange={(e) => onFilterChange('category', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-1 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900"
              >
                <option value="">All Categories</option>
                <option value="WORKSHOP">Workshop</option>
                <option value="CONFERENCE">Conference</option>
                <option value="WEBINAR">Webinar</option>
                <option value="COMPETITION">Competition</option>
                <option value="SEMINAR">Seminar</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                value={filters.status || ''}
                onChange={(e) => onFilterChange('status', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-1 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900"
              >
                <option value="">All Status</option>
                <option value="PUBLISHED">Published</option>
                <option value="DRAFT">Draft</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Type
              </label>
              <select
                value={filters.type || ''}
                onChange={(e) => onFilterChange('type', e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-1 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900"
              >
                <option value="">All Types</option>
                <option value="featured">Featured</option>
                <option value="competition">Competitions</option>
                <option value="regular">Regular</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={onClearFilters}
                className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 focus:outline-none focus:ring-1 focus:ring-gray-500 transition-colors duration-200"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EventFilters;
