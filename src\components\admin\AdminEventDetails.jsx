import React from 'react';
import { FiCalendar, FiMapPin, FiUsers, FiClock, FiStar, FiAward } from 'react-icons/fi';

const AdminEventDetails = ({ event }) => {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-start justify-between mb-4">
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
              {event.category}
            </span>
            {event.is_featured && (
              <div className="flex items-center text-yellow-600">
                <FiStar className="h-4 w-4 mr-1" />
                <span className="text-sm font-medium">Featured</span>
              </div>
            )}
            {event.is_competition && (
              <div className="flex items-center text-orange-600">
                <FiAward className="h-4 w-4 mr-1" />
                <span className="text-sm font-medium">Competition</span>
              </div>
            )}
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{event.title}</h2>
          <p className="text-gray-600">{event.short_description}</p>
        </div>
        <span className={`px-3 py-1 text-sm font-medium rounded-full ${
          event.status === 'PUBLISHED' 
            ? 'bg-green-100 text-green-800'
            : event.status === 'DRAFT'
            ? 'bg-yellow-100 text-yellow-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {event.status}
        </span>
      </div>

      {/* Event Details Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="flex items-center">
          <FiCalendar className="h-5 w-5 text-gray-400 mr-3" />
          <div>
            <p className="text-sm font-medium text-gray-900">Start Date</p>
            <p className="text-sm text-gray-600">{formatDate(event.start_datetime)}</p>
          </div>
        </div>
        <div className="flex items-center">
          <FiClock className="h-5 w-5 text-gray-400 mr-3" />
          <div>
            <p className="text-sm font-medium text-gray-900">Time</p>
            <p className="text-sm text-gray-600">
              {formatTime(event.start_datetime)} - {formatTime(event.end_datetime)}
            </p>
          </div>
        </div>
        <div className="flex items-center">
          <FiMapPin className="h-5 w-5 text-gray-400 mr-3" />
          <div>
            <p className="text-sm font-medium text-gray-900">Location</p>
            <p className="text-sm text-gray-600">{event.location}</p>
          </div>
        </div>
        <div className="flex items-center">
          <FiUsers className="h-5 w-5 text-gray-400 mr-3" />
          <div>
            <p className="text-sm font-medium text-gray-900">Registrations</p>
            <p className="text-sm text-gray-600">
              {event.total_registrations || 0} / {event.max_attendees} attendees
            </p>
            <p className="text-xs text-gray-500">
              Available tickets: {event.available_tickets || 0}
            </p>
          </div>
        </div>
      </div>

      {/* Description */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
        <div className="prose max-w-none text-gray-600">
          {event.description}
        </div>
      </div>
    </div>
  );
};

export default AdminEventDetails;
