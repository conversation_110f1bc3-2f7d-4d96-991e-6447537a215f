import React from 'react';
import {
  <PERSON>H<PERSON>,
  FiEdit3,
  <PERSON>Eye,
  FiCheck,
  FiX,
  FiClock,
  FiAlertCircle
} from 'react-icons/fi';

const InstituteProfileHeader = ({
  profile,
  profileNotFound,
  approvalStatus,
  isEditing,
  onToggleEdit,
  onCancelEdit
}) => {
  // Get status configuration
  const getStatusConfig = (status) => {
    const configs = {
      approved: {
        icon: FiCheck,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        label: 'Approved',
        description: 'Your institute profile has been approved by admin.'
      },
      pending: {
        icon: FiClock,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200',
        label: 'Pending Approval',
        description: 'Your profile is under admin review.'
      },
      rejected: {
        icon: FiX,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        label: 'Rejected',
        description: 'Your profile needs updates before approval.'
      },
      draft: {
        icon: FiAlertCircle,
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        label: 'Draft',
        description: 'Complete your profile to submit for approval.'
      }
    };
    return configs[status] || configs.draft;
  };

  const statusConfig = getStatusConfig(approvalStatus);
  const StatusIcon = statusConfig.icon;

  return (
    <div className="space-y-6">
      {/* Main Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            {profile?.logo_url ? (
              <img
                src={profile.logo_url}
                alt="Institute Logo"
                className="w-16 h-16 rounded-lg object-cover border border-gray-200"
              />
            ) : (
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center border border-gray-200">
                <FiHome className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {profileNotFound ? 'Create Institute Profile' : 
               profile?.institute_name || 'Institute Profile'}
            </h1>
            <p className="text-gray-600">
              {profileNotFound 
                ? 'Set up your institute profile to get started'
                : 'Manage your institute information and settings'
              }
            </p>
          </div>
        </div>

        {/* Edit Toggle Button */}
        {!profileNotFound && (
          <div className="flex space-x-3">
            {isEditing ? (
              <button
                onClick={onCancelEdit}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiX className="h-4 w-4 mr-2" />
                Cancel
              </button>
            ) : (
              <button
                onClick={onToggleEdit}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiEdit3 className="h-4 w-4 mr-2" />
                Edit Profile
              </button>
            )}
            {!isEditing && (
              <button
                onClick={() => {/* View mode toggle if needed */}}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                <FiEye className="h-4 w-4 mr-2" />
                View Mode
              </button>
            )}
          </div>
        )}
      </div>

      {/* Status Banner */}
      {!profileNotFound ? (
        <div className={`rounded-lg border p-4 ${statusConfig.bgColor} ${statusConfig.borderColor}`}>
          <div className="flex items-start">
            <StatusIcon className={`h-5 w-5 ${statusConfig.color} mt-0.5 mr-3 flex-shrink-0`} />
            <div className="flex-1">
              <h3 className={`text-sm font-medium ${statusConfig.color}`}>
                {statusConfig.label}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {statusConfig.description}
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="rounded-lg border p-4 bg-blue-50 border-blue-200">
          <div className="flex items-start">
            <FiAlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-blue-600">
                Create Your Institute Profile
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Fill out the form below to create your institute profile. All required fields must be completed before you can submit for approval.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstituteProfileHeader;
