import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiSend, FiUser, FiDollarSign, FiClock } from 'react-icons/fi';
import { 
  sendInvitation,
  selectSendInvitationState,
  clearSendInvitationState
} from '../../store/slices/collaborationSlice';
import { ActionModal } from '../ui';

const InviteModal = ({ isOpen, onClose, userType = 'institute' }) => {
  const dispatch = useDispatch();
  const sendState = useSelector(selectSendInvitationState);

  const [formData, setFormData] = useState({
    receiverId: '',
    hourlyRate: '',
    hoursPerWeek: '',
    receivedBy: userType === 'institute' ? 'mentor' : 'institute'
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (sendState.success) {
      onClose();
      setFormData({
        receiverId: '',
        hourlyRate: '',
        hoursPerWeek: '',
        receivedBy: userType === 'institute' ? 'mentor' : 'institute'
      });
      setErrors({});
      dispatch(clearSendInvitationState());
    }
  }, [sendState.success, onClose, userType, dispatch]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.receiverId.trim()) {
      newErrors.receiverId = 'Receiver ID is required';
    }

    if (!formData.hourlyRate || formData.hourlyRate <= 0) {
      newErrors.hourlyRate = 'Valid hourly rate is required';
    }

    if (!formData.hoursPerWeek || formData.hoursPerWeek <= 0) {
      newErrors.hoursPerWeek = 'Valid hours per week is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    const invitationData = {
      receiver_id: formData.receiverId,
      hourly_rate: parseFloat(formData.hourlyRate),
      hours_per_week: parseInt(formData.hoursPerWeek),
      received_by: formData.receivedBy
    };

    dispatch(sendInvitation(invitationData));
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleClose = () => {
    setFormData({
      receiverId: '',
      hourlyRate: '',
      hoursPerWeek: '',
      receivedBy: userType === 'institute' ? 'mentor' : 'institute'
    });
    setErrors({});
    dispatch(clearSendInvitationState());
    onClose();
  };

  return (
    <ActionModal
      isOpen={isOpen}
      onClose={handleClose}
      onConfirm={handleSubmit}
      title="Send Collaboration Invitation"
      message={`Invite a ${userType === 'institute' ? 'mentor' : 'institute'} to collaborate with you.`}
      confirmText="Send Invitation"
      confirmColor="blue"
      icon={FiSend}
      loading={sendState.loading}
    >
      <div className="space-y-4">
        {/* Receiver ID */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <FiUser className="inline h-4 w-4 mr-1" />
            {userType === 'institute' ? 'Mentor' : 'Institute'} ID *
          </label>
          <input
            type="text"
            value={formData.receiverId}
            onChange={(e) => handleChange('receiverId', e.target.value)}
            placeholder={`Enter ${userType === 'institute' ? 'mentor' : 'institute'} ID`}
            className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.receiverId ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.receiverId && (
            <p className="text-red-500 text-sm mt-1">{errors.receiverId}</p>
          )}
        </div>

        {/* Hourly Rate */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <FiDollarSign className="inline h-4 w-4 mr-1" />
            Hourly Rate (USD) *
          </label>
          <input
            type="number"
            min="0"
            step="0.01"
            value={formData.hourlyRate}
            onChange={(e) => handleChange('hourlyRate', e.target.value)}
            placeholder="Enter hourly rate"
            className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.hourlyRate ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.hourlyRate && (
            <p className="text-red-500 text-sm mt-1">{errors.hourlyRate}</p>
          )}
        </div>

        {/* Hours per Week */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <FiClock className="inline h-4 w-4 mr-1" />
            Hours per Week *
          </label>
          <input
            type="number"
            min="1"
            max="168"
            value={formData.hoursPerWeek}
            onChange={(e) => handleChange('hoursPerWeek', e.target.value)}
            placeholder="Enter hours per week"
            className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.hoursPerWeek ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.hoursPerWeek && (
            <p className="text-red-500 text-sm mt-1">{errors.hoursPerWeek}</p>
          )}
        </div>

        {/* Summary */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Invitation Summary</h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p>• Inviting: {userType === 'institute' ? 'Mentor' : 'Institute'}</p>
            <p>• Rate: ${formData.hourlyRate || '0'} per hour</p>
            <p>• Commitment: {formData.hoursPerWeek || '0'} hours per week</p>
            <p>• Total weekly: ${((formData.hourlyRate || 0) * (formData.hoursPerWeek || 0)).toFixed(2)}</p>
          </div>
        </div>

        {/* Error Message */}
        {sendState.error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            Error: {sendState.error.message || sendState.error}
          </div>
        )}
      </div>
    </ActionModal>
  );
};

export default InviteModal;
