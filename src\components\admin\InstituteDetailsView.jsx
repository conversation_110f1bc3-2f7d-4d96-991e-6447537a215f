import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiArrowLeft, FiHome, FiMail, FiPhone, FiMapPin, FiGlobe, FiFileText, FiCheck, FiX, FiDownload, FiAlertTriangle } from 'react-icons/fi';
import { LoadingSpinner, ActionModal } from '../ui';
import {
  fetchInstituteDetailsAdmin,
  fetchInstituteProfileById,
  approveInstituteAdmin,
  rejectInstituteAdmin,
  selectSelectedInstituteProfile,
  selectSelectedInstituteLoading,
  selectSelectedInstituteError,
  selectAdminApprovalLoading,
  selectAdminRejectionLoading
} from '../../store/slices/InstituteProfileSlice';
import { downloadDocument } from '../../services/documentService';

const InstituteDetailsView = ({ institute, loading: propLoading, onBack }) => {
  const dispatch = useDispatch();

  // Redux state
  const detailedInstitute = useSelector(selectSelectedInstituteProfile);
  const apiLoading = useSelector(selectSelectedInstituteLoading);
  const apiError = useSelector(selectSelectedInstituteError);
  const approvalLoading = useSelector(selectAdminApprovalLoading);
  const rejectionLoading = useSelector(selectAdminRejectionLoading);

  // Use API loading if we're fetching, otherwise use prop loading
  const loading = apiLoading || propLoading;

  // Get institute ID from either the institute object or directly if it's just an ID
  const instituteId = typeof institute === 'object' ? institute.id : institute;

  // Fetch detailed institute data when component mounts or institute changes
  useEffect(() => {
    if (instituteId && !detailedInstitute) {
      dispatch(fetchInstituteProfileById(instituteId));
    }
  }, [dispatch, instituteId, detailedInstitute]);

  // Use detailed institute data if available, otherwise fall back to prop data
  const currentInstitute = detailedInstitute || institute;

  // State for approval/rejection modals
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [approvalNotes, setApprovalNotes] = useState('');
  const [rejectionNotes, setRejectionNotes] = useState('');

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
        <span className="ml-3 text-gray-600">Loading institute details...</span>
      </div>
    );
  }

  if (apiError) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-red-900 mb-2">Error Loading Institute</h3>
        <p className="text-red-600 mb-4">{apiError.message || 'Failed to load institute details'}</p>
        <button
          onClick={() => dispatch(fetchInstituteProfileById(instituteId))}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!currentInstitute) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Institute Selected</h3>
        <p className="text-gray-500">Please select an institute to view details.</p>
      </div>
    );
  }

  // Parse the API response structure correctly
  const user = currentInstitute.user || {};
  const profile = currentInstitute.profile || currentInstitute.institute_profile || currentInstitute;
  const documents = profile.documents || [];
  const stats = {
    totalCompetitions: currentInstitute.total_competitions || 0,
    totalMentors: currentInstitute.total_mentors || 0,
    activeCompetitions: currentInstitute.active_competitions || 0
  };

  const getStatusColor = (status) => ({
    approved: 'bg-green-500 text-white',
    pending: 'bg-yellow-500 text-white',
    rejected: 'bg-red-500 text-white'
  }[status] || 'bg-gray-500 text-white');

  const handleApprove = async () => {
    try {
      await dispatch(approveInstituteAdmin({
        institute_id: instituteId,
        verification_notes: approvalNotes
      })).unwrap();

      // Close modal and reset state
      setShowApprovalModal(false);
      setApprovalNotes('');

      // Refresh the institute data after approval
      dispatch(fetchInstituteProfileById(instituteId));

      alert('Institute approved successfully!');
    } catch (error) {
      console.error('Failed to approve institute:', error);
      alert(`Failed to approve institute: ${error.message || error}`);
    }
  };

  const handleReject = async () => {
    if (!rejectionNotes.trim()) {
      alert('Please provide a reason for rejection.');
      return;
    }

    try {
      await dispatch(rejectInstituteAdmin({
        institute_id: instituteId,
        verification_notes: rejectionNotes
      })).unwrap();

      // Close modal and reset state
      setShowRejectionModal(false);
      setRejectionNotes('');

      // Refresh the institute data after rejection
      dispatch(fetchInstituteProfileById(instituteId));

      alert('Institute rejected successfully!');
    } catch (error) {
      console.error('Failed to reject institute:', error);
      alert(`Failed to reject institute: ${error.message || error}`);
    }
  };

  const handleDocumentDownload = async (documentPath, documentName) => {
    try {
      await downloadDocument(documentPath, documentName);
    } catch (error) {
      console.error('Failed to download document:', error);
      alert('Failed to download document. Please try again.');
    }
  };

  const ContactField = ({ icon: Icon, label, value, href }) => (
    <div className="flex items-center space-x-3 p-3">
      <Icon className="h-5 w-5 text-blue-600" />
      <div>
        <p className="text-sm font-medium text-gray-500">{label}</p>
        {href ? (
          <a href={href} className="text-gray-900 hover:text-blue-600" target="_blank" rel="noopener noreferrer">
            {value || 'Not provided'}
          </a>
        ) : (
          <p className="text-gray-900">{value || 'Not provided'}</p>
        )}
      </div>
    </div>
  );

  const StatCard = ({ label, value, color = "blue" }) => (
    <div className="bg-gray-50 rounded-lg p-4 text-center">
      <p className={`text-2xl font-bold text-${color}-600`}>{value}</p>
      <p className="text-sm text-gray-600">{label}</p>
    </div>
  );

  const DetailField = ({ label, value }) => (
    <div className="flex justify-between py-2 border-b border-gray-100">
      <span className="text-sm font-medium text-gray-500">{label}</span>
      <span className="text-gray-900">{value || 'Not provided'}</span>
    </div>
  );

  return (
    <main className="p-6 space-y-6 w-full">
      {/* Back Button */}
      <button
        onClick={onBack}
        className="flex items-center text-gray-600 hover:text-gray-900 mb-6"
      >
        <FiArrowLeft className="h-4 w-4 mr-2" />
        Back to Institutes
      </button>

      {/* Header */}
      <header className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6 rounded-xl">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 rounded-lg bg-white bg-opacity-20 flex items-center justify-center overflow-hidden">
            {profile.logo ? (
              <img
                src={profile.logo.data ? `data:${profile.logo.content_type};base64,${profile.logo.data}` : `https://edufair.duckdns.org${profile.logo.url}`}
                alt="Institute Logo"
                className="w-full h-full object-cover rounded-lg"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
            ) : null}
            <FiHome className="h-8 w-8" style={{ display: profile.logo ? 'none' : 'block' }} />
          </div>
          <div>
            <h1 className="text-2xl font-bold">
              {profile.institute_name || 'Unnamed Institute'}
            </h1>
            <p className="text-blue-100 text-lg">
              {profile.institute_type || 'Unknown Type'}
            </p>
            <div className="flex items-center mt-2 space-x-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(profile.verification_status)}`}>
                {profile.verification_status?.charAt(0).toUpperCase() + profile.verification_status?.slice(1) || 'Unknown'}
              </span>
              <span className="text-blue-100 text-sm">ID: {instituteId}</span>
            </div>
          </div>
        </div>

        {/* Banner Image */}
        {profile.banner && (
          <div className="mt-4 rounded-lg overflow-hidden">
            <img
              src={profile.banner.data ? `data:${profile.banner.content_type};base64,${profile.banner.data}` : `https://edufair.duckdns.org${profile.banner.url}`}
              alt="Institute Banner"
              className="w-full h-32 object-cover"
              onError={(e) => {
                e.target.style.display = 'none';
              }}
            />
          </div>
        )}
      </header>

      {/* Institute Statistics */}
      <section className="bg-white rounded-xl p-6 shadow-sm">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <FiHome className="h-5 w-5 mr-2 text-blue-600" />
          Institute Statistics
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatCard label="Total Competitions" value={stats.totalCompetitions} color="blue" />
          <StatCard label="Total Mentors" value={stats.totalMentors} color="green" />
          <StatCard label="Active Competitions" value={stats.activeCompetitions} color="purple" />
        </div>
      </section>

      {/* Contact Information */}
      <section className="bg-white rounded-xl p-6 shadow-sm">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <FiMail className="h-5 w-5 mr-2 text-blue-600" />
          Contact Information
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ContactField icon={FiMail} label="Email" value={user.email} href={user.email ? `mailto:${user.email}` : null} />
          <ContactField icon={FiPhone} label="Mobile" value={user.mobile} href={user.mobile ? `tel:${user.mobile}` : null} />
          <ContactField icon={FiMapPin} label="Address" value={profile.address} />
          <ContactField icon={FiGlobe} label="Website" value={profile.website} href={profile.website} />
        </div>
      </section>

      {/* Institute Details */}
      <section className="bg-white rounded-xl p-6 shadow-sm">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <FiHome className="h-5 w-5 mr-2 text-blue-600" />
          Institute Details
        </h2>
        <div className="space-y-2">
          <DetailField label="Institute Type" value={profile.institute_type} />
          <DetailField label="Established Year" value={profile.established_year} />
          <DetailField label="Accreditation" value={profile.accreditation} />
          <DetailField label="Description" value={profile.description} />
          <DetailField label="City" value={profile.city} />
          <DetailField label="State" value={profile.state} />
          <DetailField label="Postal Code" value={profile.postal_code} />
          <DetailField label="Country" value={user.country} />
          <DetailField label="Created At" value={new Date(profile.created_at).toLocaleDateString()} />
          <DetailField label="Last Updated" value={new Date(profile.updated_at).toLocaleDateString()} />
        </div>
      </section>

      {/* Social Media Links */}
      {(profile.linkedin_url || profile.facebook_url || profile.twitter_url) && (
        <section className="bg-white rounded-xl p-6 shadow-sm">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <FiGlobe className="h-5 w-5 mr-2 text-blue-600" />
            Social Media
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {profile.linkedin_url && (
              <ContactField icon={FiGlobe} label="LinkedIn" value="View Profile" href={profile.linkedin_url} />
            )}
            {profile.facebook_url && (
              <ContactField icon={FiGlobe} label="Facebook" value="View Page" href={profile.facebook_url} />
            )}
            {profile.twitter_url && (
              <ContactField icon={FiGlobe} label="Twitter" value="View Profile" href={profile.twitter_url} />
            )}
          </div>
        </section>
      )}

      {/* Documents */}
      {documents.length > 0 && (
        <section className="bg-white rounded-xl p-6 shadow-sm">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <FiFileText className="h-5 w-5 mr-2 text-blue-600" />
            Documents ({documents.length})
          </h2>
          <div className="space-y-3">
            {documents.map((doc, index) => (
              <div key={doc.id || index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                <div className="flex items-center space-x-3">
                  <FiFileText className="h-5 w-5 text-gray-600" />
                  <div>
                    <p className="text-gray-900 font-medium">{doc.document_name || `Document ${index + 1}`}</p>
                    <p className="text-sm text-gray-500 capitalize">{doc.document_type || 'Unknown Type'}</p>
                    {doc.description && (
                      <p className="text-xs text-gray-400">{doc.description}</p>
                    )}
                    <div className="flex items-center space-x-4 mt-1">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        doc.verified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {doc.verified ? 'Verified' : 'Pending Verification'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(doc.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleDocumentDownload(doc.document_url, doc.document_name)}
                    className="flex items-center px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                    title="Download Document"
                  >
                    <FiDownload className="h-4 w-4 mr-1" />
                    Download
                  </button>
                  <a
                    href={`https://edufair.duckdns.org/api/institutes/document/${doc.document_url}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    title="View Document"
                  >
                    <FiFileText className="h-4 w-4 mr-1" />
                    View
                  </a>
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Actions */}
      <section className="bg-white rounded-xl p-6 shadow-sm">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Admin Actions</h2>
        <div className="flex space-x-4">
          <button
            onClick={() => setShowApprovalModal(true)}
            disabled={approvalLoading || profile.verification_status === 'approved'}
            className="flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <FiCheck className="h-5 w-5 mr-2" />
            {approvalLoading ? 'Approving...' : 'Approve'}
          </button>
          <button
            onClick={() => setShowRejectionModal(true)}
            disabled={rejectionLoading || profile.verification_status === 'rejected'}
            className="flex items-center px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <FiX className="h-5 w-5 mr-2" />
            {rejectionLoading ? 'Rejecting...' : 'Reject'}
          </button>
        </div>

        {profile.verification_status === 'approved' && (
          <p className="text-green-600 text-sm mt-2">✓ This institute has been approved</p>
        )}
        {profile.verification_status === 'rejected' && (
          <p className="text-red-600 text-sm mt-2">✗ This institute has been rejected</p>
        )}
      </section>

      {/* Beautiful Approval Modal */}
      <ActionModal
        isOpen={showApprovalModal}
        onClose={() => {
          setShowApprovalModal(false);
          setApprovalNotes('');
        }}
        onConfirm={handleApprove}
        title="Approve Institute"
        message={`Are you sure you want to approve "${profile.institute_name}"?`}
        confirmText="Approve Institute"
        confirmColor="green"
        icon={FiCheck}
        loading={approvalLoading}
      >
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Approval Notes (Optional)
          </label>
          <textarea
            value={approvalNotes}
            onChange={(e) => setApprovalNotes(e.target.value)}
            placeholder="Add any notes about the approval..."
            className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            rows={3}
          />
        </div>
      </ActionModal>

      {/* Beautiful Rejection Modal */}
      <ActionModal
        isOpen={showRejectionModal}
        onClose={() => {
          setShowRejectionModal(false);
          setRejectionNotes('');
        }}
        onConfirm={handleReject}
        title="Reject Institute"
        message={`Are you sure you want to reject "${profile.institute_name}"?`}
        confirmText="Reject Institute"
        confirmColor="red"
        icon={FiAlertTriangle}
        loading={rejectionLoading}
      >
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Reason for Rejection <span className="text-red-500">*</span>
          </label>
          <textarea
            value={rejectionNotes}
            onChange={(e) => setRejectionNotes(e.target.value)}
            placeholder="Please provide a reason for rejection..."
            className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            rows={3}
            required
          />
          {!rejectionNotes.trim() && (
            <p className="text-red-500 text-sm mt-1">Rejection reason is required</p>
          )}
        </div>
      </ActionModal>
    </main>
  );
};

export default InstituteDetailsView;
