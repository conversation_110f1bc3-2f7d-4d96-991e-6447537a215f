import React from 'react';
import { Fi<PERSON>, <PERSON><PERSON>he<PERSON>, FiAlertTriangle } from 'react-icons/fi';
import LoadingSpinner from '../LoadingSpinner';

const ActionModal = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  confirmColor = "blue",
  loading = false,
  children,
  icon: Icon,
  iconColor = "blue"
}) => {
  console.log('ActionModal render:', { isOpen, title, message }); // Debug log

  if (!isOpen) return null;

  const colorClasses = {
    blue: {
      bg: "bg-blue-600 hover:bg-blue-700",
      icon: "text-blue-600",
      iconBg: "bg-blue-100"
    },
    green: {
      bg: "bg-green-600 hover:bg-green-700", 
      icon: "text-green-600",
      iconBg: "bg-green-100"
    },
    red: {
      bg: "bg-red-600 hover:bg-red-700",
      icon: "text-red-600", 
      iconBg: "bg-red-100"
    }
  };

  const colors = colorClasses[confirmColor] || colorClasses.blue;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      {/* Background overlay */}
      <div
        className="absolute inset-0"
        onClick={onClose}
      />

      {/* Modal panel */}
      <div className="relative w-full max-w-md bg-white rounded-2xl shadow-xl p-6" style={{ zIndex: 1000 }}>
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              {Icon && (
                <div className={`flex items-center justify-center w-10 h-10 rounded-full ${colors.iconBg} mr-3`}>
                  <Icon className={`w-5 h-5 ${colors.icon}`} />
                </div>
              )}
              <h3 className="text-lg font-semibold text-gray-900">
                {title || 'Modal Title'}
              </h3>
            </div>
            <button
              onClick={onClose}
              disabled={loading}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <FiX className="w-5 h-5" />
            </button>
          </div>

          {/* Message */}
          {message && (
            <p className="text-gray-600 mb-4">
              {message}
            </p>
          )}

          {/* Debug info */}
          {!message && (
            <p className="text-gray-600 mb-4">
              Debug: Modal is open but no message provided
            </p>
          )}

          {/* Children content */}
          {children && (
            <div className="mb-6">
              {children}
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-3">
            <button
              onClick={onConfirm}
              disabled={loading}
              className={`flex-1 flex items-center justify-center px-4 py-2.5 text-white rounded-lg font-medium transition-colors ${colors.bg} disabled:opacity-50`}
            >
              {loading ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <FiCheck className="w-4 h-4 mr-2" />
                  {confirmText}
                </>
              )}
            </button>
            <button
              onClick={onClose}
              disabled={loading}
              className="flex-1 px-4 py-2.5 text-gray-700 bg-gray-100 rounded-lg font-medium hover:bg-gray-200 transition-colors disabled:opacity-50"
            >
              {cancelText}
            </button>
          </div>
        </div>
    </div>
  );
};

export default ActionModal;
