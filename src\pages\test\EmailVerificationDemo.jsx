import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  sendVerificationEmail,
  verifyEmailCode,
  resendVerificationEmail,
  checkVerificationStatus,
  clearErrors,
  clearSuccessStates,
  setVerificationStatus,
  selectEmailVerification,
} from '../../store/slices/EmailVerificationSlice';
import { useEmailVerification } from '../../components/auth/EmailVerificationGuard';
import VerificationCodeInput from '../../components/ui/VerificationCodeInput';
import EmailVerificationStatus, { EmailVerificationAlert } from '../../components/ui/EmailVerificationStatus';
import Navbar from '../common/navbar/Navbar';

const EmailVerificationDemo = () => {
  const dispatch = useDispatch();
  const emailVerification = useSelector(selectEmailVerification);
  const { isEmailVerified, isExempt, userRole } = useEmailVerification();
  const [demoCode, setDemoCode] = useState('');

  const handleSendEmail = () => {
    dispatch(sendVerificationEmail());
  };

  const handleVerifyCode = () => {
    if (demoCode.length === 6) {
      dispatch(verifyEmailCode(demoCode));
    }
  };

  const handleResendEmail = () => {
    dispatch(resendVerificationEmail());
  };

  const handleCheckStatus = () => {
    dispatch(checkVerificationStatus());
  };

  const handleClearErrors = () => {
    dispatch(clearErrors());
  };

  const handleClearSuccess = () => {
    dispatch(clearSuccessStates());
  };

  const handleSetVerified = () => {
    dispatch(setVerificationStatus({
      isEmailVerified: true,
      email: '<EMAIL>',
      verifiedAt: new Date().toISOString(),
    }));
  };

  const handleSetUnverified = () => {
    dispatch(setVerificationStatus({
      isEmailVerified: false,
      email: '<EMAIL>',
      verifiedAt: null,
    }));
  };

  // Demo functions to simulate different user roles
  const simulateUserRole = (role) => {
    // This would normally be done through proper login, but for demo purposes
    // we'll simulate by updating localStorage
    localStorage.setItem('role', role);

    // Force a page refresh to update the user context
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
            Email Verification Demo
          </h1>

          {/* Current State Display */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Current State
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Verification Status</h3>
                <EmailVerificationStatus
                  isVerified={emailVerification.isEmailVerified}
                  email={emailVerification.email}
                  variant="card"
                />

                {/* Admin Exemption Status */}
                <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>User Role:</span>
                      <span className="font-medium">{userRole || 'Not logged in'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Is Exempt (Admin):</span>
                      <span className={`font-medium ${isExempt ? 'text-green-600' : 'text-gray-600'}`}>
                        {isExempt ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Effective Status:</span>
                      <span className={`font-medium ${(isEmailVerified || isExempt) ? 'text-green-600' : 'text-red-600'}`}>
                        {(isEmailVerified || isExempt) ? 'Verified' : 'Unverified'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Loading States</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Sending Email:</span>
                    <span className={emailVerification.sendingEmail ? 'text-blue-600' : 'text-gray-500'}>
                      {emailVerification.sendingEmail ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Verifying Code:</span>
                    <span className={emailVerification.verifyingCode ? 'text-blue-600' : 'text-gray-500'}>
                      {emailVerification.verifyingCode ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Resending Email:</span>
                    <span className={emailVerification.resendingEmail ? 'text-blue-600' : 'text-gray-500'}>
                      {emailVerification.resendingEmail ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Can Resend:</span>
                    <span className={emailVerification.canResend ? 'text-green-600' : 'text-red-600'}>
                      {emailVerification.canResend ? 'Yes' : `No (${emailVerification.resendCooldown}s)`}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* State JSON */}
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300">
                View Full State (JSON)
              </summary>
              <pre className="mt-2 p-3 bg-gray-100 dark:bg-gray-700 rounded text-xs overflow-auto">
                {JSON.stringify(emailVerification, null, 2)}
              </pre>
            </details>
          </div>

          {/* Demo Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Demo Actions
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <button
                onClick={handleSendEmail}
                disabled={emailVerification.sendingEmail}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                Send Verification Email
              </button>
              
              <button
                onClick={handleResendEmail}
                disabled={emailVerification.resendingEmail || !emailVerification.canResend}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                Resend Email
              </button>
              
              <button
                onClick={handleCheckStatus}
                disabled={emailVerification.checkingStatus}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
              >
                Check Status
              </button>
              
              <button
                onClick={handleSetVerified}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Set Verified (Demo)
              </button>
              
              <button
                onClick={handleSetUnverified}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Set Unverified (Demo)
              </button>
              
              <button
                onClick={handleClearErrors}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Clear Errors
              </button>
              
              <button
                onClick={handleClearSuccess}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Clear Success
              </button>
            </div>

            {/* Role Simulation Section */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                Simulate User Roles (Demo Only)
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Test how different user roles interact with email verification. Admin users are exempt from verification.
              </p>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
                <button
                  onClick={() => simulateUserRole('admin')}
                  className="px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 text-sm"
                >
                  Admin
                </button>
                <button
                  onClick={() => simulateUserRole('student')}
                  className="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
                >
                  Student
                </button>
                <button
                  onClick={() => simulateUserRole('teacher')}
                  className="px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-sm"
                >
                  Teacher
                </button>
                <button
                  onClick={() => simulateUserRole('institute')}
                  className="px-3 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 text-sm"
                >
                  Institute
                </button>
                <button
                  onClick={() => simulateUserRole('mentor')}
                  className="px-3 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 text-sm"
                >
                  Mentor
                </button>
              </div>
            </div>
          </div>

          {/* Verification Code Input Demo */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Verification Code Input Demo
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Enter 6-digit code:
                </label>
                <VerificationCodeInput
                  value={demoCode}
                  onChange={setDemoCode}
                  autoFocus={false}
                />
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  Current value: "{demoCode}" (Length: {demoCode.length})
                </p>
              </div>
              
              <button
                onClick={handleVerifyCode}
                disabled={demoCode.length !== 6 || emailVerification.verifyingCode}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                Verify Code
              </button>
            </div>
          </div>

          {/* Alert Demos */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Alert Components Demo
            </h2>
            
            <div className="space-y-4">
              <EmailVerificationAlert
                type="success"
                title="Success"
                message="Email verified successfully!"
              />
              
              <EmailVerificationAlert
                type="error"
                title="Error"
                message="Invalid verification code. Please try again."
              />
              
              <EmailVerificationAlert
                type="warning"
                title="Warning"
                message="Your verification code will expire in 5 minutes."
              />
              
              <EmailVerificationAlert
                type="info"
                title="Info"
                message="We've sent a verification code to your email address."
              />
            </div>
          </div>

          {/* Status Badge Demos */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Status Badge Demos
            </h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Badge Variants</h3>
                <div className="flex flex-wrap gap-2">
                  <EmailVerificationStatus isVerified={true} variant="badge" size="sm" />
                  <EmailVerificationStatus isVerified={false} variant="badge" size="sm" />
                  <EmailVerificationStatus isVerified={true} variant="badge" size="md" />
                  <EmailVerificationStatus isVerified={false} variant="badge" size="md" />
                  <EmailVerificationStatus isVerified={true} variant="badge" size="lg" />
                  <EmailVerificationStatus isVerified={false} variant="badge" size="lg" />
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Inline Variants</h3>
                <div className="space-y-2">
                  <EmailVerificationStatus 
                    isVerified={true} 
                    email="<EMAIL>" 
                    variant="inline" 
                    size="md" 
                  />
                  <EmailVerificationStatus 
                    isVerified={false} 
                    email="<EMAIL>" 
                    variant="inline" 
                    size="md" 
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationDemo;
