import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiArrowLeft,
  FiSave,
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiStar,
  FiAward
} from 'react-icons/fi';
import {
  fetchInstituteEventById,
  updateInstituteEvent,
  selectCurrentEvent,
  selectCurrentEventLoading,
  selectCurrentEventError,
  selectUpdateLoading,
  selectUpdateSuccess,
  clearErrors
} from '../../store/slices/InstituteEventsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const InstituteEventEditPage = ({ eventId: propEventId }) => {
  const { eventId: paramEventId } = useParams();
  const eventId = propEventId || paramEventId; // Use prop first, fallback to params
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('basic');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Redux state
  const event = useSelector(selectCurrentEvent);
  const loading = useSelector(selectCurrentEventLoading);
  const error = useSelector(selectCurrentEventError);
  const updateLoading = useSelector(selectUpdateLoading);
  const updateSuccess = useSelector(selectUpdateSuccess);

  // Form data state - Updated to match new API structure
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    short_description: '',
    banner_image_url: '',
    gallery_images: [],
    category: 'WORKSHOP',
    start_datetime: '',
    end_datetime: '',
    registration_start: '',
    registration_end: '',
    location: '', // Now just a string
    max_attendees: '',
    min_attendees: 1,
    status: 'DRAFT',
    is_featured: false,
    is_public: true,
    requires_approval: false,
    agenda: [],
    requirements: '',
    tags: [],
    external_links: {},
    is_competition: false,
    competition_exam_id: '',
    competition_rules: '',
    prize_details: {}
  });

  // Helper function to validate UUID
  const isValidUUID = (str) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  };

  // Load event details on mount
  useEffect(() => {
    if (eventId && isValidUUID(eventId)) {
      dispatch(fetchInstituteEventById(eventId));
    }
  }, [dispatch, eventId]);

  // Populate form when event data is loaded
  useEffect(() => {
    if (event) {
      setFormData({
        title: event.title || '',
        description: event.description || '',
        short_description: event.short_description || '',
        banner_image_url: event.banner_image_url || '',
        gallery_images: event.gallery_images || [],
        category: event.category || 'WORKSHOP',
        start_datetime: event.start_datetime ? new Date(event.start_datetime).toISOString().slice(0, 16) : '',
        end_datetime: event.end_datetime ? new Date(event.end_datetime).toISOString().slice(0, 16) : '',
        registration_start: event.registration_start ? new Date(event.registration_start).toISOString().slice(0, 16) : '',
        registration_end: event.registration_end ? new Date(event.registration_end).toISOString().slice(0, 16) : '',
        location: event.location || '',
        max_attendees: event.max_attendees || '',
        min_attendees: event.min_attendees || 1,
        status: event.status || 'DRAFT',
        is_featured: event.is_featured || false,
        is_public: event.is_public !== false,
        requires_approval: event.requires_approval || false,
        agenda: event.agenda || [],
        requirements: event.requirements || '',
        tags: event.tags || [],
        external_links: event.external_links || {},
        is_competition: event.is_competition || false,
        competition_exam_id: event.competition_exam_id || '',
        competition_rules: event.competition_rules || '',
        prize_details: event.prize_details || {}
      });
    }
  }, [event]);

  // Handle successful update
  useEffect(() => {
    if (updateSuccess) {
      navigate(`/institute/events/${eventId}`);
    }
  }, [updateSuccess, navigate, eventId]);

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      dispatch(clearErrors());
    };
  }, [dispatch]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Prepare data for API
      const eventData = {
        title: formData.title,
        description: formData.description,
        short_description: formData.short_description,
        banner_image_url: formData.banner_image_url,
        gallery_images: formData.gallery_images,
        start_datetime: formData.start_datetime,
        end_datetime: formData.end_datetime,
        registration_start: formData.registration_start,
        registration_end: formData.registration_end,
        category: formData.category,
        location: formData.location,
        status: formData.status,
        is_featured: formData.is_featured,
        is_public: formData.is_public,
        requires_approval: formData.requires_approval,
        max_attendees: parseInt(formData.max_attendees) || null,
        min_attendees: parseInt(formData.min_attendees) || 1,
        agenda: formData.agenda,
        requirements: formData.requirements,
        tags: formData.tags,
        external_links: formData.external_links,
        is_competition: formData.is_competition,
        competition_exam_id: formData.competition_exam_id || null,
        competition_rules: formData.competition_rules,
        prize_details: formData.prize_details
      };

      await dispatch(updateInstituteEvent({ eventId, eventData }));
    } catch (error) {
      console.error('Failed to update event:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  // Check for invalid eventId
  if (eventId && !isValidUUID(eventId)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Event ID</h2>
          <p className="text-gray-600 mb-4">The event ID provided is not valid.</p>
          <button
            onClick={() => navigate('/institute/events')}
            className="text-blue-600 hover:text-blue-800"
          >
            Back to Events
          </button>
        </div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Event not found</h2>
          <p className="text-gray-600 mb-4">The event you're trying to edit doesn't exist.</p>
          <button
            onClick={() => navigate('/institute/events')}
            className="text-blue-600 hover:text-blue-800"
          >
            Back to Events
          </button>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: FiStar },
    { id: 'schedule', label: 'Schedule', icon: FiCalendar },
    { id: 'location', label: 'Location', icon: FiMapPin },
    { id: 'settings', label: 'Settings', icon: FiUsers }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(`/institute/events/${eventId}`)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <FiArrowLeft className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Edit Event</h1>
                <p className="text-gray-600">{formData.title}</p>
              </div>
            </div>

            <button
              onClick={handleSubmit}
              disabled={isSubmitting || updateLoading}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {(isSubmitting || updateLoading) ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <FiSave className="h-4 w-4 mr-2" />
              )}
              {(isSubmitting || updateLoading) ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <IconComponent className="h-5 w-5 mr-3" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Form Content */}
          <div className="lg:col-span-3">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Info Tab */}
              {activeTab === 'basic' && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-6">Basic Information</h2>
                  
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Event Title *
                      </label>
                      <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter event title"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Short Description
                      </label>
                      <input
                        type="text"
                        name="short_description"
                        value={formData.short_description}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Brief description for previews"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Description *
                      </label>
                      <textarea
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        required
                        rows={6}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Detailed event description"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Category *
                      </label>
                      <select
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="WORKSHOP">Workshop</option>
                        <option value="CONFERENCE">Conference</option>
                        <option value="WEBINAR">Webinar</option>
                        <option value="COMPETITION">Competition</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Banner Image URL
                      </label>
                      <input
                        type="url"
                        name="banner_image_url"
                        value={formData.banner_image_url}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="https://example.com/banner.jpg"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Schedule Tab */}
              {activeTab === 'schedule' && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-6">Schedule & Registration</h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Start Date & Time *
                      </label>
                      <input
                        type="datetime-local"
                        name="start_datetime"
                        value={formData.start_datetime}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        End Date & Time *
                      </label>
                      <input
                        type="datetime-local"
                        name="end_datetime"
                        value={formData.end_datetime}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Registration Start
                      </label>
                      <input
                        type="datetime-local"
                        name="registration_start"
                        value={formData.registration_start}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Registration End
                      </label>
                      <input
                        type="datetime-local"
                        name="registration_end"
                        value={formData.registration_end}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Location Tab */}
              {activeTab === 'location' && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-6">Location Details</h2>
                  
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Location *
                      </label>
                      <input
                        type="text"
                        name="location"
                        value={formData.location}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., Main Auditorium, 123 University Ave, City"
                      />
                      <p className="text-sm text-gray-500 mt-1">Enter the complete location details</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Settings Tab */}
              {activeTab === 'settings' && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-6">Event Settings</h2>
                  
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Maximum Attendees
                        </label>
                        <input
                          type="number"
                          name="max_attendees"
                          value={formData.max_attendees}
                          onChange={handleInputChange}
                          min="1"
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Leave empty for unlimited"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Status
                        </label>
                        <select
                          name="status"
                          value={formData.status}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="DRAFT">Draft</option>
                          <option value="PUBLISHED">Published</option>
                          <option value="CANCELLED">Cancelled</option>
                        </select>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          name="is_featured"
                          checked={formData.is_featured}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700">
                          Featured Event
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          name="is_public"
                          checked={formData.is_public}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700">
                          Public Event
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          name="requires_approval"
                          checked={formData.requires_approval}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700">
                          Requires Approval
                        </label>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Requirements
                      </label>
                      <textarea
                        name="requirements"
                        value={formData.requirements}
                        onChange={handleInputChange}
                        rows={4}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Any special requirements or prerequisites"
                      />
                    </div>
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstituteEventEditPage;
