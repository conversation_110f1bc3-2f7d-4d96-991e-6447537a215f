import React, { useState } from 'react';
import { FiX, FiSend, FiUser, FiHome } from 'react-icons/fi';
import { FormField, TextInput, TextArea } from '../ui/FormComponents';
import LoadingSpinner from '../ui/LoadingSpinner';

const SendInvitationModal = ({
  isOpen,
  onClose,
  onSend,
  loading = false,
  userRole = 'mentor', // 'mentor' or 'institute'
  recipientType = 'mentor' // 'mentor' or 'institute'
}) => {
  const [formData, setFormData] = useState({
    receiver_id: '',
    hourly_rate: '',
    hours_per_week: '',
    invitation_message: '',
    expertise_areas_needed: '',
    contract_terms: '',
    received_by: recipientType
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.receiver_id.trim()) {
      newErrors.receiver_id = 'Recipient ID is required';
    }
    
    if (!formData.hourly_rate || formData.hourly_rate <= 0) {
      newErrors.hourly_rate = 'Valid hourly rate is required';
    }
    
    if (!formData.hours_per_week || formData.hours_per_week <= 0) {
      newErrors.hours_per_week = 'Valid hours per week is required';
    }
    
    if (!formData.invitation_message.trim()) {
      newErrors.invitation_message = 'Invitation message is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    const submitData = {
      ...formData,
      hourly_rate: parseFloat(formData.hourly_rate),
      hours_per_week: parseInt(formData.hours_per_week),
      expertise_areas_needed: formData.expertise_areas_needed 
        ? formData.expertise_areas_needed.split(',').map(area => area.trim()).filter(Boolean)
        : []
    };
    
    try {
      await onSend(submitData);
      handleClose();
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  const handleClose = () => {
    setFormData({
      receiver_id: '',
      hourly_rate: '',
      hours_per_week: '',
      invitation_message: '',
      expertise_areas_needed: '',
      contract_terms: '',
      received_by: recipientType
    });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-violet-100 dark:bg-violet-900/30 rounded-lg">
              {recipientType === 'mentor' ? (
                <FiUser className="w-5 h-5 text-violet-600 dark:text-violet-400" />
              ) : (
                <FiHome className="w-5 h-5 text-violet-600 dark:text-violet-400" />
              )}
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Send Invitation to {recipientType === 'mentor' ? 'Mentor' : 'Institute'}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Create a collaboration invitation
              </p>
            </div>
          </div>
          
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Recipient ID */}
          <FormField 
            label={`${recipientType === 'mentor' ? 'Mentor' : 'Institute'} ID`} 
            error={errors.receiver_id} 
            required
          >
            <TextInput
              name="receiver_id"
              placeholder={`Enter ${recipientType} ID`}
              value={formData.receiver_id}
              onChange={handleChange}
              error={errors.receiver_id}
            />
          </FormField>

          {/* Rate and Hours */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Hourly Rate ($)" error={errors.hourly_rate} required>
              <TextInput
                type="number"
                name="hourly_rate"
                placeholder="0.00"
                value={formData.hourly_rate}
                onChange={handleChange}
                error={errors.hourly_rate}
                min="0"
                step="0.01"
              />
            </FormField>

            <FormField label="Hours per Week" error={errors.hours_per_week} required>
              <TextInput
                type="number"
                name="hours_per_week"
                placeholder="0"
                value={formData.hours_per_week}
                onChange={handleChange}
                error={errors.hours_per_week}
                min="1"
              />
            </FormField>
          </div>

          {/* Expertise Areas */}
          <FormField 
            label="Expertise Areas Needed" 
            error={errors.expertise_areas_needed}
            hint="Separate multiple areas with commas"
          >
            <TextInput
              name="expertise_areas_needed"
              placeholder="e.g., Mathematics, Physics, Programming"
              value={formData.expertise_areas_needed}
              onChange={handleChange}
              error={errors.expertise_areas_needed}
            />
          </FormField>

          {/* Invitation Message */}
          <FormField label="Invitation Message" error={errors.invitation_message} required>
            <TextArea
              name="invitation_message"
              placeholder="Write a personalized message explaining the collaboration opportunity..."
              value={formData.invitation_message}
              onChange={handleChange}
              error={errors.invitation_message}
              rows={4}
            />
          </FormField>

          {/* Contract Terms */}
          <FormField label="Contract Terms" error={errors.contract_terms}>
            <TextArea
              name="contract_terms"
              placeholder="Specify any contract terms, conditions, or expectations..."
              value={formData.contract_terms}
              onChange={handleChange}
              error={errors.contract_terms}
              rows={3}
            />
          </FormField>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            
            <button
              type="submit"
              disabled={loading}
              className="flex items-center space-x-2 px-6 py-2 bg-violet-600 hover:bg-violet-700 disabled:bg-violet-400 text-white rounded-lg transition-colors"
            >
              {loading ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>Sending...</span>
                </>
              ) : (
                <>
                  <FiSend className="w-4 h-4" />
                  <span>Send Invitation</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SendInvitationModal;
