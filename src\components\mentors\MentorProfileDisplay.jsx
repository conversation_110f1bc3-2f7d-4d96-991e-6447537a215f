import React from 'react';
import {
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiClock,
  FiDollarSign,
  FiStar,
  FiCheckCircle,
  FiGlobe,
  FiAward
} from 'react-icons/fi';
import API_BASE_URL from '../../utils/api/API_URL';

/**
 * Component to display mentor profile information
 * Handles the new API response structure with user and profile objects
 */
const MentorProfileDisplay = ({ mentorData, showFullDetails = true }) => {
  if (!mentorData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No mentor data available</p>
      </div>
    );
  }

  // Helper function to get full image URL
  const getFullImageUrl = (imageUrl) => {
    if (!imageUrl) return null;

    // If it's base64 data, return as is
    if (imageUrl.startsWith('data:')) {
      return imageUrl;
    }

    // If it's already a full URL, return as is
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // If it's a relative path, prepend the API base URL
    if (imageUrl.startsWith('/')) {
      return `${API_BASE_URL}${imageUrl}`;
    }

    // If it's just a filename, assume it's in the static folder
    return `${API_BASE_URL}/static/${imageUrl}`;
  };

  // Handle the new API response structure
  const userData = mentorData.user || mentorData;
  const profileData = mentorData.profile || mentorData.user?.mentor_profile || mentorData;
  
  // Extract data with fallbacks
  const mentorInfo = {
    // User information
    id: userData.id,
    username: userData.username,
    email: userData.email,
    mobile: userData.mobile,
    country: userData.country,
    profile_picture: userData.profile_picture,
    user_type: userData.user_type,
    is_email_verified: userData.is_email_verified,
    is_mobile_verified: userData.is_mobile_verified,
    created_at: userData.created_at,
    
    // Profile information
    bio: profileData.bio,
    experience_years: profileData.experience_years,
    hourly_rate: profileData.hourly_rate,
    full_name: profileData.full_name,
    mobile: userData.mobile, // Use mobile from user data instead of phone from profile
    linkedin_url: profileData.linkedin_url,
    website: profileData.website,
    current_position: profileData.current_position,
    current_organization: profileData.current_organization,
    education: profileData.education,
    certifications: profileData.certifications,
    portfolio_url: profileData.portfolio_url,
    resume_url: profileData.resume_url,
    languages: profileData.languages || [],
    availability_hours: profileData.availability_hours || {},
    profile_image_url: profileData.profile_image_url,
    is_verified: profileData.is_verified,
    verification_status: profileData.verification_status || mentorData.verification_status || 'pending',
    rating: profileData.rating,
    total_reviews: profileData.total_reviews,
    expertise_subjects: profileData.expertise_subjects || [],
    preferred_subjects: profileData.preferred_subjects || [],

    // Additional fields
    total_competitions: mentorData.total_competitions || 0,
    active_institutes: mentorData.active_institutes || 0,
    average_rating: mentorData.average_rating,

    // New fields from updated API
    total_students: mentorData.total_students || profileData.total_students || 0,
    total_sessions: mentorData.total_sessions || profileData.total_sessions || 0,
    response_time: mentorData.response_time || profileData.response_time || 'within 24 hours',
    last_active: mentorData.last_active || profileData.last_active
  };

  const renderStars = (rating) => {
    if (!rating) return null;
    
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <FiStar key={i} className="h-4 w-4 text-yellow-400 fill-current" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <FiStar key="half" className="h-4 w-4 text-yellow-400 fill-current opacity-50" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <FiStar key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
      );
    }

    return stars;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-start space-x-4 mb-6">
        <div className="relative">
          <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-100 border-2 border-gray-200">
            {getFullImageUrl(mentorInfo.profile_image_url) ? (
              <img
                src={getFullImageUrl(mentorInfo.profile_image_url)}
                alt={mentorInfo.username}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // If image fails to load, hide it and show default icon
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
            ) : null}
            {!getFullImageUrl(mentorInfo.profile_image_url) && (
              <div className="w-full h-full flex items-center justify-center">
                <FiUser className="w-6 h-6 text-gray-400" />
              </div>
            )}
            {/* Fallback div for when image fails to load */}
            <div className="w-full h-full flex items-center justify-center" style={{ display: 'none' }}>
              <FiUser className="w-6 h-6 text-gray-400" />
            </div>
          </div>
          {mentorInfo.verification_status === 'verified' && (
            <div className="absolute -top-1 -right-1">
              <FiCheckCircle className="h-6 w-6 text-green-500 bg-white rounded-full" />
            </div>
          )}
        </div>
        
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">{mentorInfo.username}</h3>
          <p className="text-sm text-gray-600">{mentorInfo.user_type}</p>
          
          {/* Rating */}
          {mentorInfo.average_rating && (
            <div className="flex items-center mt-2">
              {renderStars(mentorInfo.average_rating)}
              <span className="ml-2 text-sm text-gray-600">
                {mentorInfo.average_rating.toFixed(1)}
              </span>
            </div>
          )}
          
          {/* Verification Status */}
          <div className="mt-2">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              mentorInfo.verification_status === 'verified' 
                ? 'bg-green-100 text-green-800' 
                : mentorInfo.verification_status === 'pending'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-gray-100 text-gray-800'
            }`}>
              <FiAward className="h-3 w-3 mr-1" />
              {mentorInfo.verification_status}
            </span>
          </div>
        </div>
      </div>

      {/* Basic Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="flex items-center text-sm text-gray-600">
          <FiMail className="h-4 w-4 mr-2" />
          {mentorInfo.email}
          {mentorInfo.is_email_verified && (
            <FiCheckCircle className="h-3 w-3 ml-1 text-green-500" />
          )}
        </div>
        
        {mentorInfo.mobile && (
          <div className="flex items-center text-sm text-gray-600">
            <FiPhone className="h-4 w-4 mr-2" />
            {mentorInfo.mobile}
            {mentorInfo.is_mobile_verified && (
              <FiCheckCircle className="h-3 w-3 ml-1 text-green-500" />
            )}
          </div>
        )}
        
        {mentorInfo.country && (
          <div className="flex items-center text-sm text-gray-600">
            <FiMapPin className="h-4 w-4 mr-2" />
            {mentorInfo.country}
          </div>
        )}
        
        {mentorInfo.experience_years && (
          <div className="flex items-center text-sm text-gray-600">
            <FiClock className="h-4 w-4 mr-2" />
            {mentorInfo.experience_years} years experience
          </div>
        )}
        
        {mentorInfo.hourly_rate && (
          <div className="flex items-center text-sm text-gray-600">
            <FiDollarSign className="h-4 w-4 mr-2" />
            ${mentorInfo.hourly_rate}/hour
          </div>
        )}
        
        {mentorInfo.languages.length > 0 && (
          <div className="flex items-center text-sm text-gray-600">
            <FiGlobe className="h-4 w-4 mr-2" />
            {mentorInfo.languages.join(', ')}
          </div>
        )}
      </div>

      {/* Bio */}
      {mentorInfo.bio && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Bio</h4>
          <p className="text-sm text-gray-600">{mentorInfo.bio}</p>
        </div>
      )}

      {/* Professional Information */}
      {showFullDetails && (mentorInfo.current_position || mentorInfo.current_organization || mentorInfo.education) && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Professional Information</h4>
          <div className="space-y-2">
            {mentorInfo.current_position && (
              <div className="flex items-center text-sm text-gray-600">
                <FiAward className="h-4 w-4 mr-2 flex-shrink-0" />
                <span><strong>Position:</strong> {mentorInfo.current_position}</span>
              </div>
            )}
            {mentorInfo.current_organization && (
              <div className="flex items-center text-sm text-gray-600">
                <FiUser className="h-4 w-4 mr-2 flex-shrink-0" />
                <span><strong>Organization:</strong> {mentorInfo.current_organization}</span>
              </div>
            )}
            {mentorInfo.education && (
              <div className="text-sm text-gray-600">
                <strong>Education:</strong>
                <p className="mt-1 ml-6">{mentorInfo.education}</p>
              </div>
            )}
            {mentorInfo.certifications && (
              <div className="text-sm text-gray-600">
                <strong>Certifications:</strong>
                <p className="mt-1 ml-6">{mentorInfo.certifications}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Contact & Links */}
      {showFullDetails && (mentorInfo.mobile || mentorInfo.linkedin_url || mentorInfo.website || mentorInfo.portfolio_url) && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Contact & Links</h4>
          <div className="space-y-2">
            {mentorInfo.mobile && (
              <div className="flex items-center text-sm text-gray-600">
                <FiPhone className="h-4 w-4 mr-2 flex-shrink-0" />
                <span>{mentorInfo.mobile}</span>
              </div>
            )}
            {mentorInfo.linkedin_url && (
              <div className="flex items-center text-sm text-gray-600">
                <FiGlobe className="h-4 w-4 mr-2 flex-shrink-0" />
                <a
                  href={mentorInfo.linkedin_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  LinkedIn Profile
                </a>
              </div>
            )}
            {mentorInfo.website && (
              <div className="flex items-center text-sm text-gray-600">
                <FiGlobe className="h-4 w-4 mr-2 flex-shrink-0" />
                <a
                  href={mentorInfo.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  Personal Website
                </a>
              </div>
            )}
            {mentorInfo.portfolio_url && (
              <div className="flex items-center text-sm text-gray-600">
                <FiGlobe className="h-4 w-4 mr-2 flex-shrink-0" />
                <a
                  href={mentorInfo.portfolio_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  Portfolio
                </a>
              </div>
            )}
            {mentorInfo.resume_url && (
              <div className="flex items-center text-sm text-gray-600">
                <FiGlobe className="h-4 w-4 mr-2 flex-shrink-0" />
                <a
                  href={mentorInfo.resume_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  Resume/CV
                </a>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Subjects */}
      {showFullDetails && (
        <>
          {mentorInfo.expertise_subjects.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Expertise Subjects</h4>
              <div className="flex flex-wrap gap-2">
                {mentorInfo.expertise_subjects.map((subject, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {subject.name || subject}
                  </span>
                ))}
              </div>
            </div>
          )}

          {mentorInfo.preferred_subjects.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Preferred Subjects</h4>
              <div className="flex flex-wrap gap-2">
                {mentorInfo.preferred_subjects.map((subject, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    {subject.name || subject}
                  </span>
                ))}
              </div>
            </div>
          )}
        </>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">{mentorInfo.total_students || 0}</div>
          <div className="text-xs text-gray-500">Students</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">{mentorInfo.total_sessions || 0}</div>
          <div className="text-xs text-gray-500">Sessions</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">
            {mentorInfo.rating ? parseFloat(mentorInfo.rating).toFixed(1) : 'N/A'}
          </div>
          <div className="text-xs text-gray-500">Rating ({mentorInfo.total_reviews || 0} reviews)</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">{mentorInfo.active_institutes || 0}</div>
          <div className="text-xs text-gray-500">Active Institutes</div>
        </div>
      </div>
    </div>
  );
};

export default MentorProfileDisplay;
