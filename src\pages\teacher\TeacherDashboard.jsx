import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { fetchAllOwnClasses } from '../../store/slices/ClassroomSlice';
import { fetchCurrentUser } from '../../store/slices/userSlice';
import { PageContainer, Stack } from '../../components/ui/layout';
import { DashboardGrid, QuickActions } from '../../components/dashboard';
import {
  FiUsers,
  FiBookOpen,
  FiCheckCircle,
  FiTrendingUp,
  FiPlus,
  FiEye
} from 'react-icons/fi';

function TeacherDashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { classrooms, loading: classroomsLoading } = useSelector((state) => state.classroom);
  const { currentUser, loading: userLoading } = useSelector((state) => state.users);

  useEffect(() => {
    dispatch(fetchCurrentUser());
    dispatch(fetchAllOwnClasses());
  }, [dispatch]);

  const stats = useMemo(() => {
    if (!classrooms) return [];

    const totalClasses = classrooms.length;
    const totalStudents = classrooms.reduce((sum, classroom) => sum + (classroom.students?.length || 0), 0);
    const activeTasks = Math.floor(totalClasses * 2.5);
    const completedTasks = Math.floor(totalClasses * 8.3);

    return [
      {
        key: 'classes',
        title: 'Total Classes',
        value: totalClasses,
        icon: FiBookOpen,
        color: 'blue',
        onClick: () => navigate('/teacher/classrooms')
      },
      {
        key: 'students',
        title: 'Total Students',
        value: totalStudents,
        icon: FiUsers,
        color: 'green'
      },
      {
        key: 'active',
        title: 'Active Tasks',
        value: activeTasks,
        icon: FiTrendingUp,
        color: 'yellow'
      },
      {
        key: 'completed',
        title: 'Completed Tasks',
        value: completedTasks,
        icon: FiCheckCircle,
        color: 'purple'
      }
    ];
  }, [classrooms, navigate]);

  const quickActions = useMemo(() => [
    {
      key: 'create-class',
      title: 'Create Class',
      description: 'Start a new classroom',
      icon: FiPlus,
      color: 'green',
      onClick: () => navigate('/teacher/classrooms/create')
    },
    {
      key: 'view-classes',
      title: 'View Classes',
      description: 'Manage your classrooms',
      icon: FiEye,
      color: 'blue',
      onClick: () => navigate('/teacher/classrooms')
    },
    {
      key: 'create-exam',
      title: 'Create Exam',
      description: 'Design a new exam',
      icon: FiBookOpen,
      color: 'purple',
      onClick: () => navigate('/teacher/exams/create')
    }
  ], [navigate]);

  const loading = classroomsLoading || userLoading;

  return (
    <PageContainer>
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
          Teacher Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Welcome back, {currentUser?.username || 'Teacher'}! Here's your teaching overview.
        </p>
      </div>

      <Stack gap="lg">
        <DashboardGrid stats={stats} loading={loading} />
        <QuickActions actions={quickActions} />
      </Stack>
    </PageContainer>
  );
}

export default TeacherDashboard;
