import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiClock,
  FiStar,
  FiAward
} from 'react-icons/fi';
import EventRegistrationModal from './EventRegistrationModalNew';

const PublicEventCard = ({ event, onViewDetails, onRegister, variant = 'default' }) => {
  const navigate = useNavigate();
  const [showRegistration, setShowRegistration] = useState(false);

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(event);
    } else {
      navigate(`/events/${event.id}`);
    }
  };

  const handleRegister = () => {
    if (onRegister) {
      onRegister(event);
    } else {
      setShowRegistration(true);
    }
  };

  const handleRegistrationSuccess = () => {
    setShowRegistration(false);
    // Show success message or redirect
    alert('Registration successful!');
  };

  const handleRegistrationClose = () => {
    setShowRegistration(false);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCategoryColor = (category) => {
    const colors = {
      WORKSHOP: 'bg-slate-100 text-slate-700 border-slate-200',
      CONFERENCE: 'bg-gray-100 text-gray-700 border-gray-200',
      WEBINAR: 'bg-neutral-100 text-neutral-700 border-neutral-200',
      COMPETITION: 'bg-stone-100 text-stone-700 border-stone-200',
      SEMINAR: 'bg-zinc-100 text-zinc-700 border-zinc-200'
    };
    return colors[category] || 'bg-gray-100 text-gray-700 border-gray-200';
  };

  return (
    <>
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 ${
      variant === 'featured' ? 'ring-1 ring-gray-300 shadow-md' : ''
    }`}>
      {/* Event Image */}
      {event.banner_image_url && (
        <div className="h-48 bg-gray-100 overflow-hidden">
          <img
            src={event.banner_image_url}
            alt={event.title}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {/* Event Content */}
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded text-xs font-medium border ${getCategoryColor(event.category)}`}>
              {event.category}
            </span>
            {event.is_featured && (
              <div className="bg-gray-100 p-1 rounded border border-gray-200">
                <FiStar className="h-3 w-3 text-gray-600" />
              </div>
            )}
            {event.is_competition && (
              <div className="bg-gray-100 p-1 rounded border border-gray-200">
                <FiAward className="h-3 w-3 text-gray-600" />
              </div>
            )}
          </div>
        </div>

        {/* Title */}
        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
          {event.title}
        </h3>

        {/* Description */}
        {event.short_description && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {event.short_description}
          </p>
        )}

        {/* Event Details */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-600">
            <FiCalendar className="h-4 w-4 mr-2 text-gray-500" />
            <span>{formatDate(event.start_datetime)}</span>
          </div>

          <div className="flex items-center text-sm text-gray-600">
            <FiClock className="h-4 w-4 mr-2 text-gray-500" />
            <span>{formatTime(event.start_datetime)}</span>
          </div>

          {event.location && (
            <div className="flex items-center text-sm text-gray-600">
              <FiMapPin className="h-4 w-4 mr-2 text-gray-500" />
              <span className="truncate">{event.location}</span>
            </div>
          )}

          {event.max_attendees && (
            <div className="flex items-center text-sm text-gray-600">
              <FiUsers className="h-4 w-4 mr-2 text-gray-500" />
              <span>Max {event.max_attendees} attendees</span>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex space-x-3">
          <button
            onClick={handleViewDetails}
            className="flex-1 px-4 py-2 border border-gray-300 rounded text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
          >
            View Details
          </button>
          <button
            onClick={handleRegister}
            className="flex-1 px-4 py-2 bg-gray-900 text-white rounded text-sm font-medium hover:bg-gray-800 transition-colors duration-200"
          >
            Register
          </button>
        </div>
      </div>
    </div>

    {/* Registration Modal */}
    <EventRegistrationModal
      isOpen={showRegistration}
      onClose={handleRegistrationClose}
      event={event}
    />
  </>
  );
};

export default PublicEventCard;
