import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { fetchCurrentUser } from '../../store/slices/userSlice';
import { <PERSON>Container, Stack } from '../../components/ui/layout';
import { DashboardGrid, QuickActions } from '../../components/dashboard';
import {
  FiClock,
  FiBookOpen,
  FiStar,
  FiUser,
  FiSettings,
  FiEye
} from 'react-icons/fi';

function MentorDashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { currentUser, loading } = useSelector((state) => state.users);
  const profile = currentUser?.mentor_profile || {};

  useEffect(() => {
    if (!currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  const greeting = useMemo(() => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  }, []);

  const stats = useMemo(() => [
    {
      key: 'experience',
      title: 'Experience',
      value: `${profile?.experience_years || 0} years`,
      icon: FiClock,
      color: 'green'
    },
    {
      key: 'subjects',
      title: 'Subjects',
      value: profile?.expertise_subjects?.length || 0,
      icon: FiBookOpen,
      color: 'blue'
    },
    {
      key: 'rating',
      title: 'Rating',
      value: profile?.average_rating ? `${profile.average_rating.toFixed(1)}/5.0` : 'N/A',
      icon: FiStar,
      color: 'yellow'
    },
    {
      key: 'students',
      title: 'Students Mentored',
      value: profile?.total_students || 0,
      icon: FiUser,
      color: 'purple'
    }
  ], [profile]);

  const quickActions = useMemo(() => [
    {
      key: 'edit-profile',
      title: 'Edit Profile',
      description: 'Update your mentor information',
      icon: FiUser,
      color: 'blue',
      onClick: () => navigate('/mentor/settings')
    },
    {
      key: 'view-sessions',
      title: 'View Sessions',
      description: 'Check your mentoring sessions',
      icon: FiEye,
      color: 'green',
      onClick: () => navigate('/mentor/sessions')
    },
    {
      key: 'settings',
      title: 'Settings',
      description: 'Configure your preferences',
      icon: FiSettings,
      color: 'purple',
      onClick: () => navigate('/mentor/settings')
    }
  ], [navigate]);

  return (
    <PageContainer>
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">
          {greeting}, {profile?.username || currentUser?.username || 'Mentor'}!
        </h1>
        <p className="mt-1 text-gray-600">
          Welcome to your mentor portal
        </p>
      </div>

      <Stack gap="lg">
        <DashboardGrid stats={stats} loading={loading} />
        <QuickActions actions={quickActions} />
      </Stack>
    </PageContainer>
  );
}

export default MentorDashboard;
