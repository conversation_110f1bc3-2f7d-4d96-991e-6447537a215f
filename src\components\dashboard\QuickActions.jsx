import React from 'react';
import { Card, Stack } from '../ui/layout';

const ActionButton = ({ title, description, icon: Icon, onClick, color = 'blue' }) => {
  const colorClasses = {
    blue: 'bg-blue-600 hover:bg-blue-700',
    green: 'bg-green-600 hover:bg-green-700',
    purple: 'bg-purple-600 hover:bg-purple-700',
    indigo: 'bg-indigo-600 hover:bg-indigo-700'
  };

  return (
    <button
      onClick={onClick}
      className={`w-full p-4 rounded-lg text-white text-left transition-colors ${colorClasses[color]}`}
    >
      <div className="flex items-center">
        <Icon className="h-6 w-6 mr-3" />
        <div>
          <h4 className="font-medium">{title}</h4>
          <p className="text-sm opacity-90">{description}</p>
        </div>
      </div>
    </button>
  );
};

const QuickActions = ({ title = "Quick Actions", actions = [] }) => {
  return (
    <Card>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <Stack gap="sm">
        {actions.map((action, index) => (
          <ActionButton
            key={action.key || index}
            title={action.title}
            description={action.description}
            icon={action.icon}
            onClick={action.onClick}
            color={action.color}
          />
        ))}
      </Stack>
    </Card>
  );
};

export default QuickActions;
