/**
 * VerificationStatus Component
 * Displays institute verification status using the new API
 */

import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiCheck, 
  FiClock, 
  FiX, 
  FiAlertCircle, 
  FiRefreshCw 
} from 'react-icons/fi';
import { 
  getInstituteVerificationStatus,
  selectVerificationStatus,
  selectVerificationStatusLoading,
  selectVerificationStatusError
} from '../../store/slices/InstituteProfileSlice';
import { useThemeProvider } from '../../providers/ThemeContext';

const VerificationStatus = ({ 
  showRefreshButton = true,
  className = ''
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  
  const verificationStatus = useSelector(selectVerificationStatus);
  const loading = useSelector(selectVerificationStatusLoading);
  const error = useSelector(selectVerificationStatusError);

  const isDark = currentTheme === 'dark';

  useEffect(() => {
    // Fetch verification status on component mount
    dispatch(getInstituteVerificationStatus());
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(getInstituteVerificationStatus());
  };

  const getStatusConfig = () => {
    if (!verificationStatus?.verification_status) {
      return {
        icon: FiClock,
        color: 'text-gray-500',
        bgColor: 'bg-gray-100 dark:bg-gray-800',
        borderColor: 'border-gray-300 dark:border-gray-600',
        status: 'Unknown',
        message: 'Verification status not available'
      };
    }

    const status = verificationStatus.verification_status.toLowerCase();
    
    switch (status) {
      case 'approved':
      case 'verified':
        return {
          icon: FiCheck,
          color: 'text-green-600',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800',
          status: 'Approved',
          message: 'Your institute has been verified and approved'
        };
      
      case 'pending':
        return {
          icon: FiClock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          status: 'Pending',
          message: 'Your institute verification is under review'
        };
      
      case 'rejected':
        return {
          icon: FiX,
          color: 'text-red-600',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          status: 'Rejected',
          message: 'Your institute verification was rejected'
        };
      
      default:
        return {
          icon: FiAlertCircle,
          color: 'text-gray-500',
          bgColor: 'bg-gray-50 dark:bg-gray-800',
          borderColor: 'border-gray-200 dark:border-gray-700',
          status: 'Unknown',
          message: 'Unknown verification status'
        };
    }
  };

  if (loading) {
    return (
      <div className={`p-4 border rounded-lg ${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} ${className}`}>
        <div className="flex items-center justify-center">
          <FiRefreshCw className="w-5 h-5 animate-spin text-blue-500 mr-2" />
          <span className={isDark ? 'text-gray-300' : 'text-gray-600'}>
            Loading verification status...
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 border rounded-lg bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FiAlertCircle className="w-5 h-5 text-red-600 mr-2" />
            <span className="text-red-700 dark:text-red-400">
              Failed to load verification status
            </span>
          </div>
          {showRefreshButton && (
            <button
              onClick={handleRefresh}
              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
              title="Retry"
            >
              <FiRefreshCw className="w-4 h-4" />
            </button>
          )}
        </div>
        {error.message && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-2">
            {error.message}
          </p>
        )}
      </div>
    );
  }

  const statusConfig = getStatusConfig();
  const StatusIcon = statusConfig.icon;

  return (
    <div className={`p-4 border rounded-lg ${statusConfig.bgColor} ${statusConfig.borderColor} ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <StatusIcon className={`w-5 h-5 ${statusConfig.color} mr-2`} />
          <h3 className={`font-medium ${isDark ? 'text-gray-100' : 'text-gray-900'}`}>
            Verification Status: {statusConfig.status}
          </h3>
        </div>
        {showRefreshButton && (
          <button
            onClick={handleRefresh}
            disabled={loading}
            className={`${statusConfig.color} hover:opacity-80 disabled:opacity-50`}
            title="Refresh Status"
          >
            <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
        )}
      </div>
      
      <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'} mb-3`}>
        {statusConfig.message}
      </p>

      {verificationStatus?.verification_notes && (
        <div className="mt-3 p-3 bg-white dark:bg-gray-700 rounded border">
          <h4 className={`text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-800'} mb-1`}>
            Notes:
          </h4>
          <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            {verificationStatus.verification_notes}
          </p>
        </div>
      )}

      {verificationStatus?.verified_at && (
        <div className="mt-3 text-xs text-gray-500">
          Last updated: {new Date(verificationStatus.verified_at).toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default VerificationStatus;
