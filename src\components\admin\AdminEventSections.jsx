import React from 'react';

const AdminEventSections = ({ event }) => {
  return (
    <>
      {/* Requirements */}
      {event.requirements && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Requirements</h3>
          <p className="text-gray-600">{event.requirements}</p>
        </div>
      )}

      {/* Tags */}
      {event.tags && event.tags.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {event.tags.map((tag, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* External Links */}
      {event.external_links && Object.keys(event.external_links).length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">External Links</h3>
          <div className="space-y-2">
            {Object.entries(event.external_links).map(([key, url]) => (
              <div key={key} className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 capitalize">{key}</span>
                <a
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:text-blue-800 underline"
                >
                  {url}
                </a>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Agenda */}
      {event.agenda && event.agenda.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Agenda</h3>
          <div className="space-y-3">
            {event.agenda.map((item, index) => (
              <div key={index} className="border-l-4 border-blue-500 pl-4">
                <pre className="text-sm text-gray-600 whitespace-pre-wrap">
                  {JSON.stringify(item, null, 2)}
                </pre>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Competition Rules */}
      {event.is_competition && event.competition_rules && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Competition Rules</h3>
          <p className="text-gray-600">{event.competition_rules}</p>
        </div>
      )}

      {/* Prize Details */}
      {event.is_competition && event.prize_details && Object.keys(event.prize_details).length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Prize Details</h3>
          <div className="bg-gray-50 rounded-lg p-4">
            <pre className="text-sm text-gray-600 whitespace-pre-wrap">
              {JSON.stringify(event.prize_details, null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Competition Exam */}
      {event.is_competition && event.competition_exam_id && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Competition Exam</h3>
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-900">Exam ID:</span>
            <span className="ml-2 text-sm text-gray-600 font-mono">{event.competition_exam_id}</span>
          </div>
        </div>
      )}
    </>
  );
};

export default AdminEventSections;
