/**
 * InstituteDocumentList Component
 * Example component showing how to use the new document fetch API
 */

import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { FiFile, FiDownload, FiEye, FiExternalLink } from 'react-icons/fi';
import { useThemeProvider } from '../../providers/ThemeContext';
import { useDocuments } from '../../hooks/useDocuments';
import { DocumentModal } from '../ui/modals';
import { selectProfile } from '../../store/slices/InstituteProfileSlice';
import InstituteDocumentViewer from './InstituteDocumentViewer';

const InstituteDocumentList = ({ showActions = true, layout = 'list' }) => {
  const { currentTheme } = useThemeProvider();
  const profile = useSelector(selectProfile);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [showModal, setShowModal] = useState(false);

  // Theme classes
  const isDark = currentTheme === 'dark';
  const bgClass = isDark ? 'bg-gray-800' : 'bg-white';
  const textClass = isDark ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = isDark ? 'text-gray-300' : 'text-gray-600';
  const borderClass = isDark ? 'border-gray-700' : 'border-gray-200';

  // Get documents from profile
  const documents = profile?.documents || [];

  const handleViewDocument = (document) => {
    setSelectedDocument(document);
    setShowModal(true);
  };

  if (!documents || documents.length === 0) {
    return (
      <div className={`p-6 text-center ${bgClass} rounded-lg border ${borderClass}`}>
        <FiFile className={`w-12 h-12 mx-auto mb-3 ${textSecondary}`} />
        <p className={textSecondary}>No documents uploaded yet</p>
      </div>
    );
  }

  if (layout === 'grid') {
    return (
      <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {documents.map((document, index) => (
            <InstituteDocumentViewer
              key={document.id || index}
              document={document}
              showActions={showActions}
              showPreview={false}
              className="h-full"
            />
          ))}
        </div>

        {/* Document Modal - Note: This would need to be updated to use the document fetch API */}
        {showModal && selectedDocument && (
          <DocumentModal
            isOpen={showModal}
            onClose={() => setShowModal(false)}
            src={selectedDocument.document_url}
            title={selectedDocument.document_name}
            downloadUrl={selectedDocument.document_url}
          />
        )}
      </>
    );
  }

  // List layout (default)
  return (
    <>
      <div className={`${bgClass} rounded-lg border ${borderClass} overflow-hidden`}>
        <div className={`px-6 py-4 border-b ${borderClass}`}>
          <h3 className={`text-lg font-medium ${textClass}`}>
            Institute Documents ({documents.length})
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {documents.map((document, index) => (
            <div key={document.id || index} className="p-4">
              <InstituteDocumentViewer
                document={document}
                showActions={showActions}
                showPreview={false}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Document Modal - Note: This would need to be updated to use the document fetch API */}
      {showModal && selectedDocument && (
        <DocumentModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          src={selectedDocument.document_url}
          title={selectedDocument.document_name}
          downloadUrl={selectedDocument.document_url}
        />
      )}
    </>
  );
};

export default InstituteDocumentList;
