import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiArrowLeft,
  FiEdit,
  FiTrash2,
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiClock,
  FiStar,
  FiAward,
  FiShare2,
  FiEye,
  FiSettings,
  FiTrendingUp
} from 'react-icons/fi';
import { format } from 'date-fns';
import {
  fetchInstituteEventById,
  deleteInstituteEvent,
  publishInstituteEvent,
  selectCurrentEvent,
  selectCurrentEventLoading,
  selectCurrentEventError,
  clearErrors
} from '../../store/slices/InstituteEventsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { getCategoryColor, getStatusBadgeColor } from '../../utils/eventUtils';

const InstituteEventDetailsPage = ({ eventId: propEventId }) => {
  const { eventId: paramEventId } = useParams();
  const eventId = propEventId || paramEventId; // Use prop first, fallback to params
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Redux state
  const event = useSelector(selectCurrentEvent);
  const loading = useSelector(selectCurrentEventLoading);
  const error = useSelector(selectCurrentEventError);

  // Helper function to validate UUID
  const isValidUUID = (str) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  };

  // Load event details on mount
  useEffect(() => {
    if (eventId && isValidUUID(eventId)) {
      dispatch(fetchInstituteEventById(eventId));
    }
  }, [dispatch, eventId]);

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      dispatch(clearErrors());
    };
  }, [dispatch]);

  const formatDate = (dateString) => {
    if (!dateString) return 'TBD';
    try {
      return format(new Date(dateString), 'PPP p');
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const handleDelete = async () => {
    try {
      await dispatch(deleteInstituteEvent(eventId));
      navigate('/institute/events');
    } catch (error) {
      console.error('Failed to delete event:', error);
    }
  };

  const handlePublish = async () => {
    try {
      await dispatch(publishInstituteEvent(eventId));
    } catch (error) {
      console.error('Failed to publish event:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  // Check for invalid eventId
  if (eventId && !isValidUUID(eventId)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Event ID</h2>
          <p className="text-gray-600 mb-4">The event ID provided is not valid.</p>
          <button
            onClick={() => navigate('/institute/events')}
            className="text-blue-600 hover:text-blue-800"
          >
            Back to Events
          </button>
        </div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Event not found</h2>
          <p className="text-gray-600 mb-4">The event you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/institute/events')}
            className="text-blue-600 hover:text-blue-800"
          >
            Back to Events
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/institute/events')}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <FiArrowLeft className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{event.title}</h1>
                <div className="flex items-center space-x-3 mt-1">
                  <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getCategoryColor(event.category)}`}>
                    {event.category}
                  </span>
                  <span className={`px-3 py-1 text-xs font-bold rounded-full ${getStatusBadgeColor(event.status)}`}>
                    {event.status?.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <button
                onClick={() => navigate(`/institute/events/${eventId}/edit`)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiEdit className="h-4 w-4 mr-2" />
                Edit Event
              </button>
              
              {event.status?.toLowerCase() === 'draft' && (
                <button
                  onClick={handlePublish}
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <FiTrendingUp className="h-4 w-4 mr-2" />
                  Publish
                </button>
              )}

              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <FiTrash2 className="h-4 w-4 mr-2" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Banner Image */}
            {event.banner_image_url && (
              <div className="aspect-w-16 aspect-h-9 rounded-xl overflow-hidden">
                <img
                  src={event.banner_image_url}
                  alt={event.title}
                  className="w-full h-64 object-cover"
                />
              </div>
            )}

            {/* Description */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">About This Event</h2>
              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed">{event.description}</p>
                {event.short_description && (
                  <p className="text-gray-600 mt-4 italic">{event.short_description}</p>
                )}
              </div>
            </div>

            {/* Requirements */}
            {event.requirements && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Requirements</h2>
                <p className="text-gray-700">{event.requirements}</p>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Event Details */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Event Details</h3>
              <div className="space-y-4">
                <div className="flex items-center text-gray-700">
                  <FiCalendar className="h-5 w-5 mr-3 text-blue-600" />
                  <div>
                    <p className="font-medium">Start Date</p>
                    <p className="text-sm text-gray-600">{formatDate(event.start_datetime)}</p>
                  </div>
                </div>
                
                <div className="flex items-center text-gray-700">
                  <FiClock className="h-5 w-5 mr-3 text-green-600" />
                  <div>
                    <p className="font-medium">End Date</p>
                    <p className="text-sm text-gray-600">{formatDate(event.end_datetime)}</p>
                  </div>
                </div>

                <div className="flex items-center text-gray-700">
                  <FiMapPin className="h-5 w-5 mr-3 text-red-600" />
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="text-sm text-gray-600">
                      {event.location || 'TBD'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center text-gray-700">
                  <FiUsers className="h-5 w-5 mr-3 text-purple-600" />
                  <div>
                    <p className="font-medium">Capacity</p>
                    <p className="text-sm text-gray-600">
                      {event.max_attendees || 'Unlimited'} attendees
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Registration Stats */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Registration Stats</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Registrations</span>
                  <span className="font-semibold">{event.total_registrations || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Available Spots</span>
                  <span className="font-semibold">
                    {event.max_attendees ? event.max_attendees - (event.total_registrations || 0) : '∞'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Delete Event</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this event? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstituteEventDetailsPage;
