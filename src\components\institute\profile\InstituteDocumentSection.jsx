import React from 'react';
import InstituteDocumentUpload from '../InstituteDocumentUpload';

/**
 * InstituteDocumentSection Component
 * Simplified wrapper for document upload section
 */
const InstituteDocumentSection = ({
  documents,
  onDocumentsChange,
  isEditing,
  profileNotFound
}) => {
  // Only show if editing or has documents
  if (!isEditing && (profileNotFound || documents.length === 0)) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Verification Documents</h3>
      </div>
      <div className="p-6">
        <InstituteDocumentUpload
          documents={documents}
          onDocumentsChange={onDocumentsChange}
          disabled={!isEditing}
        />
      </div>
    </div>
  );
};

export default InstituteDocumentSection;
