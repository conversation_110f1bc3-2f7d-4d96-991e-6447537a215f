import React from 'react';
import {
  FiCalendar,
  FiTrendingUp,
  FiAward,
  FiStar
} from 'react-icons/fi';

const EventStats = ({ events = [] }) => {
  const totalEvents = events.length;
  const publishedEvents = events.filter(e => e.status?.toLowerCase() === 'published').length;
  const competitionEvents = events.filter(e => e.category?.toLowerCase() === 'competition').length;
  const featuredEvents = events.filter(e => e.is_featured).length;

  const stats = [
    {
      title: 'Total Events',
      value: totalEvents,
      icon: FiCalendar,
      iconColor: 'text-blue-600',
      bgColor: 'bg-gradient-to-br from-blue-50 to-blue-100',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Published',
      value: publishedEvents,
      icon: FiTrendingUp,
      iconColor: 'text-green-600',
      bgColor: 'bg-gradient-to-br from-green-50 to-green-100',
      borderColor: 'border-green-200'
    },
    {
      title: 'Competitions',
      value: competitionEvents,
      icon: FiAward,
      iconColor: 'text-orange-600',
      bgColor: 'bg-gradient-to-br from-orange-50 to-orange-100',
      borderColor: 'border-orange-200'
    },
    {
      title: 'Featured',
      value: featuredEvents,
      icon: FiStar,
      iconColor: 'text-yellow-600',
      bgColor: 'bg-gradient-to-br from-yellow-50 to-yellow-100',
      borderColor: 'border-yellow-200'
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => {
        const IconComponent = stat.icon;
        return (
          <div key={index} className={`bg-white rounded-xl shadow-sm border-2 ${stat.borderColor} p-6 hover:shadow-md transition-all duration-200 transform hover:scale-105`}>
            <div className="flex items-center">
              <div className={`flex-shrink-0 p-3 rounded-xl ${stat.bgColor} shadow-sm`}>
                <IconComponent className={`h-7 w-7 ${stat.iconColor}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">{stat.title}</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">{stat.value}</p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default EventStats;
