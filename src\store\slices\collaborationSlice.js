import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import API_BASE_URL from '../../utils/api/API_URL';

const BASE_URL = `${API_BASE_URL}/api/institute/mentors`;
const MENTOR_BASE_URL = `${API_BASE_URL}/api/mentor`;
const getAuthToken = () => localStorage.getItem("token");

// Helper function to get auth headers
const getAuthHeaders = () => ({
  'Authorization': `Bearer ${getAuthToken()}`,
  'Content-Type': 'application/json'
});

// Async thunks for Invitation Management
export const fetchSentInvitations = createAsyncThunk(
  'collaboration/fetchSentInvitations',
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/sent`, {
        params: { page, size },
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });

      // Handle successful response with empty data
      if (res.status === 200) {
        return res.data || { invitations: [], total: 0, page: 1, size: 20 };
      }

      return res.data;
    } catch (err) {
      console.error('Error fetching sent invitations:', err);
      const errorMessage = err.response?.data?.message || err.response?.data?.error || err.message || 'Failed to fetch sent invitations';
      return thunkAPI.rejectWithValue(errorMessage);
    }
  }
);

export const fetchReceivedInvitations = createAsyncThunk(
  'collaboration/fetchReceivedInvitations',
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/received`, {
        params: { page, size },
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });

      // Handle successful response with empty data
      if (res.status === 200) {
        return res.data || { invitations: [], total: 0, page: 1, size: 20 };
      }

      return res.data;
    } catch (err) {
      console.error('Error fetching received invitations:', err);
      const errorMessage = err.response?.data?.message || err.response?.data?.error || err.message || 'Failed to fetch received invitations';
      return thunkAPI.rejectWithValue(errorMessage);
    }
  }
);

// Send invitation to mentor (from institute)
export const sendInvitationToMentor = createAsyncThunk(
  'collaboration/sendInvitationToMentor',
  async (invitationData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/send-to-mentor`, invitationData, {
        headers: getAuthHeaders()
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Send invitation to institute (from mentor)
export const sendInvitationToInstitute = createAsyncThunk(
  'collaboration/sendInvitationToInstitute',
  async (invitationData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/send-to-institute`, invitationData, {
        headers: getAuthHeaders()
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Accept invitation
export const acceptInvitation = createAsyncThunk(
  'collaboration/acceptInvitation',
  async (invitationId, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/${invitationId}/accept`, {}, {
        headers: getAuthHeaders()
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Reject invitation
export const rejectInvitation = createAsyncThunk(
  'collaboration/rejectInvitation',
  async (invitationId, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/${invitationId}/reject`, {}, {
        headers: getAuthHeaders()
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch all invitations (both sent and received)
export const fetchAllInvitations = createAsyncThunk(
  'collaboration/fetchAllInvitations',
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const [sentRes, receivedRes] = await Promise.all([
        axios.get(`${BASE_URL}/sent`, {
          params: { page, size },
          headers: { Authorization: `Bearer ${getAuthToken()}` }
        }),
        axios.get(`${BASE_URL}/received`, {
          params: { page, size },
          headers: { Authorization: `Bearer ${getAuthToken()}` }
        })
      ]);

      return {
        sent: sentRes.data,
        received: receivedRes.data
      };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Async thunks for Collaboration Management

// Get collaborations for current user
export const getCollaborations = createAsyncThunk(
  'collaboration/getCollaborations',
  async ({ statusFilter = '', page = 1, size = 20 } = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        size: size.toString()
      });

      if (statusFilter) {
        params.append('status_filter', statusFilter);
      }

      const response = await axios.get(
        `${MENTOR_BASE_URL}/collaborations/?${params.toString()}`,
        { headers: getAuthHeaders() }
      );

      // Handle successful response with empty data
      if (response.status === 200) {
        return response.data || { collaborations: [], total: 0, page: 1, size: 20 };
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching collaborations:', error);
      const errorMessage = error.response?.data?.message || error.response?.data?.error || error.message || 'Failed to fetch collaborations';
      return rejectWithValue(errorMessage);
    }
  }
);

// Get collaboration details by ID
export const getCollaborationById = createAsyncThunk(
  'collaboration/getCollaborationById',
  async (collaborationId, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${MENTOR_BASE_URL}/collaborations/${collaborationId}`,
        { headers: getAuthHeaders() }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Update collaboration
export const updateCollaboration = createAsyncThunk(
  'collaboration/updateCollaboration',
  async ({ collaborationId, updateData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(
        `${MENTOR_BASE_URL}/collaborations/${collaborationId}`,
        updateData,
        { headers: getAuthHeaders() }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Delete collaboration
export const deleteCollaboration = createAsyncThunk(
  'collaboration/deleteCollaboration',
  async (collaborationId, { rejectWithValue }) => {
    try {
      await axios.delete(
        `${MENTOR_BASE_URL}/collaborations/${collaborationId}`,
        { headers: getAuthHeaders() }
      );

      return collaborationId;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  sentInvitations: {
    data: [],
    total: 0,
    page: 1,
    size: 20,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null
  },
  receivedInvitations: {
    data: [],
    total: 0,
    page: 1,
    size: 20,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null
  },
  sendInvitation: {
    loading: false,
    error: null,
    success: false
  },
  respondInvitation: {
    loading: false,
    error: null,
    success: false
  },
  collaborations: {
    data: [],
    total: 0,
    page: 1,
    size: 20,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null
  },
  currentCollaboration: null,
  collaborationActions: {
    loading: false,
    error: null,
    success: false
  }
};

const collaborationSlice = createSlice({
  name: 'collaboration',
  initialState,
  reducers: {
    clearSendInvitationState: (state) => {
      state.sendInvitation = {
        loading: false,
        error: null,
        success: false
      };
    },
    clearRespondInvitationState: (state) => {
      state.respondInvitation = {
        loading: false,
        error: null,
        success: false
      };
    },
    clearCollaborationActions: (state) => {
      state.collaborationActions = {
        loading: false,
        error: null,
        success: false
      };
    },
    clearCurrentCollaboration: (state) => {
      state.currentCollaboration = null;
    },
    clearErrors: (state) => {
      state.sentInvitations.error = null;
      state.receivedInvitations.error = null;
      state.sendInvitation.error = null;
      state.respondInvitation.error = null;
      state.collaborations.error = null;
      state.collaborationActions.error = null;
    }
  },
  extraReducers: (builder) => {
    // Fetch sent invitations
    builder
      .addCase(fetchSentInvitations.pending, (state) => {
        state.sentInvitations.loading = true;
        state.sentInvitations.error = null;
      })
      .addCase(fetchSentInvitations.fulfilled, (state, action) => {
        state.sentInvitations.loading = false;
        // Handle the new API response structure
        const payload = action.payload || {};
        state.sentInvitations.data = payload.invitations || payload.data || payload.results || [];
        state.sentInvitations.total = payload.total || payload.count || 0;
        state.sentInvitations.page = payload.page || payload.current_page || 1;
        state.sentInvitations.size = payload.size || payload.page_size || 20;
        state.sentInvitations.hasNext = payload.has_next || payload.hasNext || false;
        state.sentInvitations.hasPrev = payload.has_prev || payload.hasPrev || false;

        console.log('Sent invitations loaded:', state.sentInvitations.data);
      })
      .addCase(fetchSentInvitations.rejected, (state, action) => {
        state.sentInvitations.loading = false;
        state.sentInvitations.error = action.payload;
      });

    // Fetch received invitations
    builder
      .addCase(fetchReceivedInvitations.pending, (state) => {
        state.receivedInvitations.loading = true;
        state.receivedInvitations.error = null;
      })
      .addCase(fetchReceivedInvitations.fulfilled, (state, action) => {
        state.receivedInvitations.loading = false;
        // Handle the new API response structure
        const payload = action.payload || {};
        state.receivedInvitations.data = payload.invitations || payload.data || payload.results || [];
        state.receivedInvitations.total = payload.total || payload.count || 0;
        state.receivedInvitations.page = payload.page || payload.current_page || 1;
        state.receivedInvitations.size = payload.size || payload.page_size || 20;
        state.receivedInvitations.hasNext = payload.has_next || payload.hasNext || false;
        state.receivedInvitations.hasPrev = payload.has_prev || payload.hasPrev || false;

        console.log('Received invitations loaded:', state.receivedInvitations.data);
      })
      .addCase(fetchReceivedInvitations.rejected, (state, action) => {
        state.receivedInvitations.loading = false;
        state.receivedInvitations.error = action.payload;
      });

    // Send invitation to mentor
    builder
      .addCase(sendInvitationToMentor.pending, (state) => {
        state.sendInvitation.loading = true;
        state.sendInvitation.error = null;
        state.sendInvitation.success = false;
      })
      .addCase(sendInvitationToMentor.fulfilled, (state) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.success = true;
      })
      .addCase(sendInvitationToMentor.rejected, (state, action) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.error = action.payload;
      });

    // Send invitation to institute
    builder
      .addCase(sendInvitationToInstitute.pending, (state) => {
        state.sendInvitation.loading = true;
        state.sendInvitation.error = null;
        state.sendInvitation.success = false;
      })
      .addCase(sendInvitationToInstitute.fulfilled, (state) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.success = true;
      })
      .addCase(sendInvitationToInstitute.rejected, (state, action) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.error = action.payload;
      });

    // Accept invitation
    builder
      .addCase(acceptInvitation.pending, (state) => {
        state.respondInvitation.loading = true;
        state.respondInvitation.error = null;
        state.respondInvitation.success = false;
      })
      .addCase(acceptInvitation.fulfilled, (state) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.success = true;
      })
      .addCase(acceptInvitation.rejected, (state, action) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.error = action.payload;
      });

    // Reject invitation
    builder
      .addCase(rejectInvitation.pending, (state) => {
        state.respondInvitation.loading = true;
        state.respondInvitation.error = null;
        state.respondInvitation.success = false;
      })
      .addCase(rejectInvitation.fulfilled, (state) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.success = true;
      })
      .addCase(rejectInvitation.rejected, (state, action) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.error = action.payload;
      });

    // Fetch all invitations
    builder
      .addCase(fetchAllInvitations.pending, (state) => {
        state.sentInvitations.loading = true;
        state.receivedInvitations.loading = true;
      })
      .addCase(fetchAllInvitations.fulfilled, (state, action) => {
        // Update sent invitations
        state.sentInvitations.loading = false;
        state.sentInvitations.data = action.payload.sent?.invitations || [];
        state.sentInvitations.total = action.payload.sent?.total || 0;
        
        // Update received invitations
        state.receivedInvitations.loading = false;
        state.receivedInvitations.data = action.payload.received?.invitations || [];
        state.receivedInvitations.total = action.payload.received?.total || 0;
      })
      .addCase(fetchAllInvitations.rejected, (state, action) => {
        state.sentInvitations.loading = false;
        state.receivedInvitations.loading = false;
        state.sentInvitations.error = action.payload;
        state.receivedInvitations.error = action.payload;
      });

    // Get collaborations
    builder
      .addCase(getCollaborations.pending, (state) => {
        state.collaborations.loading = true;
        state.collaborations.error = null;
      })
      .addCase(getCollaborations.fulfilled, (state, action) => {
        state.collaborations.loading = false;
        // Handle different possible response structures
        const payload = action.payload || {};
        state.collaborations.data = payload.collaborations || payload.data || payload.results || [];
        state.collaborations.total = payload.total || payload.count || 0;
        state.collaborations.page = payload.page || payload.current_page || 1;
        state.collaborations.size = payload.size || payload.page_size || 20;
        state.collaborations.hasNext = payload.has_next || payload.hasNext || false;
        state.collaborations.hasPrev = payload.has_prev || payload.hasPrev || false;
      })
      .addCase(getCollaborations.rejected, (state, action) => {
        state.collaborations.loading = false;
        state.collaborations.error = action.payload;
      });

    // Get collaboration by ID
    builder
      .addCase(getCollaborationById.pending, (state) => {
        state.collaborationActions.loading = true;
        state.collaborationActions.error = null;
      })
      .addCase(getCollaborationById.fulfilled, (state, action) => {
        state.collaborationActions.loading = false;
        state.currentCollaboration = action.payload;
      })
      .addCase(getCollaborationById.rejected, (state, action) => {
        state.collaborationActions.loading = false;
        state.collaborationActions.error = action.payload;
      });

    // Update collaboration
    builder
      .addCase(updateCollaboration.pending, (state) => {
        state.collaborationActions.loading = true;
        state.collaborationActions.error = null;
        state.collaborationActions.success = false;
      })
      .addCase(updateCollaboration.fulfilled, (state, action) => {
        state.collaborationActions.loading = false;
        state.collaborationActions.success = true;
        state.currentCollaboration = action.payload;

        // Update in collaborations list if it exists
        const index = state.collaborations.data.findIndex(
          collab => collab.id === action.payload.id
        );
        if (index !== -1) {
          state.collaborations.data[index] = action.payload;
        }
      })
      .addCase(updateCollaboration.rejected, (state, action) => {
        state.collaborationActions.loading = false;
        state.collaborationActions.error = action.payload;
      });

    // Delete collaboration
    builder
      .addCase(deleteCollaboration.pending, (state) => {
        state.collaborationActions.loading = true;
        state.collaborationActions.error = null;
        state.collaborationActions.success = false;
      })
      .addCase(deleteCollaboration.fulfilled, (state, action) => {
        state.collaborationActions.loading = false;
        state.collaborationActions.success = true;

        // Remove from collaborations list
        state.collaborations.data = state.collaborations.data.filter(
          collab => collab.id !== action.payload
        );

        // Clear current collaboration if it was deleted
        if (state.currentCollaboration?.id === action.payload) {
          state.currentCollaboration = null;
        }
      })
      .addCase(deleteCollaboration.rejected, (state, action) => {
        state.collaborationActions.loading = false;
        state.collaborationActions.error = action.payload;
      });
  }
});

export const {
  clearSendInvitationState,
  clearRespondInvitationState,
  clearCollaborationActions,
  clearCurrentCollaboration,
  clearErrors
} = collaborationSlice.actions;

// Selectors
export const selectSentInvitations = (state) => state.collaboration.sentInvitations;
export const selectReceivedInvitations = (state) => state.collaboration.receivedInvitations;
export const selectSendInvitationState = (state) => state.collaboration.sendInvitation;
export const selectRespondInvitationState = (state) => state.collaboration.respondInvitation;
export const selectCollaborations = (state) => state.collaboration.collaborations;
export const selectCurrentCollaboration = (state) => state.collaboration.currentCollaboration;
export const selectCollaborationActions = (state) => state.collaboration.collaborationActions;

export default collaborationSlice.reducer;
