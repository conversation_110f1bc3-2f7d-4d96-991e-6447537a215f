import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { updateUserProfile } from '../../store/slices/userSlice';
import { Card, Stack } from '../ui/layout';
import { FormField, TextInput } from '../ui/FormComponents';
import ProfilePictureUpload from '../ui/ProfilePictureUpload';
import { FiUser, FiMail, FiPhone, FiGlobe, FiSave, FiEdit3, FiCheck, FiX } from 'react-icons/fi';

const ProfileSettings = ({ user, loading }) => {
  const dispatch = useDispatch();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    mobile: user?.mobile || '',
    country: user?.country || ''
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    try {
      await dispatch(updateUserProfile(formData)).unwrap();
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  const handleCancel = () => {
    setFormData({
      username: user?.username || '',
      email: user?.email || '',
      mobile: user?.mobile || '',
      country: user?.country || ''
    });
    setIsEditing(false);
  };

  return (
    <Card className="overflow-hidden">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-8 space-y-4 sm:space-y-0">
        <div className="flex items-center">
          <div className="p-3 bg-blue-100 rounded-lg mr-4">
            <FiUser className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              Profile Information
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Update your personal information and profile picture
            </p>
          </div>
        </div>
        {!isEditing ? (
          <button
            onClick={() => setIsEditing(true)}
            className="flex items-center px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
          >
            <FiEdit3 className="mr-2 h-4 w-4" />
            Edit Profile
          </button>
        ) : (
          <div className="flex space-x-3">
            <button
              onClick={handleSave}
              disabled={loading}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              <FiCheck className="mr-2 h-4 w-4" />
              Save Changes
            </button>
            <button
              onClick={handleCancel}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <FiX className="mr-2 h-4 w-4" />
              Cancel
            </button>
          </div>
        )}
      </div>

      <Stack gap="xl">
        <div className="flex justify-center">
          <div className="relative">
            <ProfilePictureUpload
              currentImage={user?.profile_picture}
              onImageUpdate={(imageData) => {
                // Handle profile picture update
                dispatch(updateUserProfile({ profile_picture: imageData }));
              }}
              disabled={!isEditing}
            />
            {isEditing && (
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  Click to change
                </span>
              </div>
            )}
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
            Personal Information
          </h4>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField label="Username" required>
              <TextInput
                name="username"
                value={formData.username}
                onChange={handleChange}
                icon={FiUser}
                disabled={!isEditing}
                placeholder="Enter your username"
                className="transition-all duration-200"
              />
            </FormField>

            <FormField label="Email Address" required>
              <TextInput
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                icon={FiMail}
                disabled={!isEditing}
                placeholder="Enter your email"
                className="transition-all duration-200"
              />
            </FormField>

            <FormField label="Mobile Number">
              <TextInput
                name="mobile"
                value={formData.mobile}
                onChange={handleChange}
                icon={FiPhone}
                disabled={!isEditing}
                placeholder="Enter your mobile number"
                className="transition-all duration-200"
              />
            </FormField>

            <FormField label="Country">
              <TextInput
                name="country"
                value={formData.country}
                onChange={handleChange}
                icon={FiGlobe}
                disabled={!isEditing}
                placeholder="Enter your country"
                className="transition-all duration-200"
              />
            </FormField>
          </div>
        </div>
      </Stack>
    </Card>
  );
};

export default ProfileSettings;
