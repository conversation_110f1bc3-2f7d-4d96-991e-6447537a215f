# Event Rendering Fixes

## Overview
This document details the fixes applied to properly render the events data returned from the API in the frontend components.

## 🔍 **API Response Analysis**

The API returns events with the following structure:
```json
{
  "title": "WorkShop Test",
  "description": "WorkShop Test", 
  "short_description": "WorkShop Test",
  "banner_image_url": null,
  "gallery_images": null,
  "start_datetime": "2025-09-02T01:18:00+05:00",
  "end_datetime": "2025-09-02T03:13:00+05:00",
  "registration_start": "2025-09-02T00:13:00+05:00",
  "registration_end": "2025-09-02T00:30:00+05:00",
  "category": "WORKSHOP",
  "location_id": null,
  "status": "PUBLISHED",
  "max_attendees": 1,
  "min_attendees": 1,
  "is_featured": false,
  "is_competition": false,
  "organizer_id": "uuid",
  "id": "uuid"
}
```

## ❌ **Field Mapping Issues**

The frontend components were expecting different field names than what the API returns:

| Frontend Expected | API Returns | Issue |
|------------------|-------------|-------|
| `start_date` | `start_datetime` | Date field mismatch |
| `end_date` | `end_datetime` | Date field mismatch |
| `image_url` | `banner_image_url` | Image field mismatch |
| `max_participants` | `max_attendees` | Participants field mismatch |
| `registration_deadline` | `registration_end` | Registration field mismatch |
| `category.name` | `category` (string) | Category structure mismatch |
| `location.name` | `location_id` | Location structure mismatch |

## ✅ **Fixes Applied**

### 1. **EventCard.jsx - Field Mapping** ✅

**Updated destructuring to handle both API and legacy formats:**

```javascript
const {
  id,
  title,
  description,
  short_description,
  // Handle both API format and legacy format for dates
  start_datetime,
  start_date = start_datetime,
  end_datetime, 
  end_date = end_datetime,
  // Handle both API format and legacy format for images
  banner_image_url,
  image_url = banner_image_url,
  category,
  location,
  venue_address,
  // Handle both API format and legacy format for participants
  max_attendees,
  max_participants = max_attendees,
  current_participants = 0, // Default since API doesn't return this yet
  registration_fee = 0, // Default since API doesn't return this yet
  currency = 'ZAR',
  status,
  // Handle both API format and legacy format for registration deadline
  registration_end,
  registration_deadline = registration_end,
  organizer_name,
  organizer_id,
  tags,
  requirements,
  agenda,
  is_featured,
  is_competition,
  is_public
} = event;
```

**Updated conditional logic for safety:**

```javascript
// Safe registration check
const isRegistrationOpen = registration_deadline ? new Date() < new Date(registration_deadline) : true;

// Safe capacity check
const isFull = max_participants ? current_participants >= max_participants : false;

// Safe percentage calculation
const attendancePercentage = max_participants ? (current_participants / max_participants) * 100 : 0;
```

**Updated category display:**

```javascript
{category && (
  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
    {typeof category === 'string' ? category : category.name}
  </span>
)}
```

**Updated date display:**

```javascript
<div className="flex items-center text-sm text-gray-500">
  <FiClock className="h-4 w-4 mr-2" />
  {start_date && formatTime(start_date)} {start_date && end_date && ' - '} {end_date && formatTime(end_date)}
</div>
```

**Updated attendees display:**

```javascript
<span className="flex items-center">
  <FiUsers className="h-4 w-4 mr-1" />
  {current_participants} / {max_participants || 'Unlimited'} attendees
</span>
```

### 2. **InstituteEvents.jsx - Field Mapping** ✅

**Added formatDate helper function:**

```javascript
const formatDate = (dateString) => {
  if (!dateString) return 'TBD';
  try {
    return format(new Date(dateString), 'MMM dd, yyyy');
  } catch (error) {
    return 'Invalid Date';
  }
};
```

**Updated event display fields:**

```javascript
// Date display
<div className="flex items-center text-sm text-gray-500">
  <FiCalendar className="h-4 w-4 mr-2" />
  {formatDate(event.start_datetime || event.startDateTime)}
</div>

// Location display
<div className="flex items-center text-sm text-gray-500">
  <FiMapPin className="h-4 w-4 mr-2" />
  {event.location_name || event.location?.venue || 'TBD'}
</div>

// Attendees display
<div className="flex items-center text-sm text-gray-500">
  <FiUsers className="h-4 w-4 mr-2" />
  {event.max_attendees || event.attendeesCount || 0} max attendees
</div>
```

**Updated category color function:**

```javascript
const getCategoryColor = (category) => {
  const normalizedCategory = category?.toLowerCase();
  switch (normalizedCategory) {
    case 'workshop': return 'bg-blue-100 text-blue-800';
    case 'conference': return 'bg-purple-100 text-purple-800';
    case 'webinar': return 'bg-green-100 text-green-800';
    case 'competition': return 'bg-orange-100 text-orange-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};
```

**Updated category display:**

```javascript
<span className={`px-2 py-1 text-xs font-medium rounded ${getCategoryColor(event.category || 'other')}`}>
  {event.category ? event.category.charAt(0) + event.category.slice(1).toLowerCase() : 'Event'}
</span>
```

### 3. **Status Handling** ✅

**Updated status checks in EventCard:**

```javascript
// Handle both uppercase (API) and lowercase (legacy) status values
const normalizedStatus = status?.toLowerCase();
const isUpcoming = normalizedStatus === 'upcoming' || status === 'PUBLISHED';
const isOngoing = normalizedStatus === 'ongoing';
const isCompleted = normalizedStatus === 'completed' || status === 'COMPLETED';
const isCancelled = normalizedStatus === 'cancelled' || status === 'CANCELLED';
const isDraft = normalizedStatus === 'draft' || status === 'DRAFT';
```

## 🎯 **Backward Compatibility**

All fixes maintain backward compatibility by:
- **Checking both field names** (API and legacy)
- **Providing safe defaults** for missing fields
- **Handling both data formats** (string vs object)
- **Graceful error handling** for invalid dates

## 📋 **Field Mapping Summary**

| Display Element | API Field | Legacy Field | Default Value |
|----------------|-----------|--------------|---------------|
| Event Title | `title` | `title` | - |
| Event Description | `description` | `description` | - |
| Short Description | `short_description` | - | - |
| Start Date | `start_datetime` | `start_date` | - |
| End Date | `end_datetime` | `end_date` | - |
| Event Image | `banner_image_url` | `image_url` | - |
| Category | `category` (string) | `category.name` | 'Event' |
| Max Attendees | `max_attendees` | `max_participants` | 'Unlimited' |
| Registration End | `registration_end` | `registration_deadline` | - |
| Status | `status` (uppercase) | `status` (lowercase) | - |
| Featured | `is_featured` | - | `false` |
| Competition | `is_competition` | - | `false` |

## 📝 **Files Modified**

### **EventCard.jsx** ✅
- Updated field destructuring for API compatibility
- Added backward compatibility for legacy field names
- Enhanced safety checks for optional fields
- Updated status handling for uppercase enum values

### **InstituteEvents.jsx** ✅
- Added formatDate helper function
- Updated event display field references
- Enhanced category color function for uppercase values
- Improved category display formatting

## 🚀 **Result**

The events are now properly rendered with:
- ✅ **Correct field mapping** from API response
- ✅ **Proper date formatting** and display
- ✅ **Category styling** with correct colors
- ✅ **Status handling** for uppercase enum values
- ✅ **Backward compatibility** with legacy formats
- ✅ **Safe defaults** for missing optional fields

**The events should now display correctly in both the EventCard components and the InstituteEvents page!** 🎉
