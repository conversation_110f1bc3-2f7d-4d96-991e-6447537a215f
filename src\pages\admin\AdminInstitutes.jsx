import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  FiHome,
  FiMail,
  FiPhone,
  FiMapPin,
  FiGlobe,
  FiEye,
  FiRefreshCw,
  FiUsers,
  FiCalendar,
  FiFilter,
  FiSearch,
  FiCheck,
  FiX,
  FiClock,
  FiFileText
} from 'react-icons/fi';
import {
  fetchAllInstitutesList,
  fetchInstituteProfileById,
  selectInstitutesList,
  selectInstitutesListLoading,
  selectInstitutesListError,
  selectInstitutesListTotal,
  selectSelectedInstituteProfile,
  selectSelectedInstituteLoading,
  clearErrors,
  approveInstituteAdmin,
  rejectInstituteAdmin,
  selectAdminApprovalLoading,
  selectAdminRejectionLoading
} from '../../store/slices/InstituteProfileSlice';
import { LoadingSpinner } from '../../components/ui';
import SearchFilterCard from '../../components/ui/SearchFilterCard';

const AdminInstitutes = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux state
  const institutes = useSelector(selectInstitutesList);
  const loading = useSelector(selectInstitutesListLoading);
  const error = useSelector(selectInstitutesListError);
  const total = useSelector(selectInstitutesListTotal);
  const selectedProfile = useSelector(selectSelectedInstituteProfile);
  const profileLoading = useSelector(selectSelectedInstituteLoading);
  const approveLoading = useSelector(selectAdminApprovalLoading);
  const rejectLoading = useSelector(selectAdminRejectionLoading);

  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [refreshing, setRefreshing] = useState(false);
  const [pagination, setPagination] = useState({ skip: 0, limit: 20 });
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [selectedInstituteId, setSelectedInstituteId] = useState(null);

  // Approval/Rejection modal state
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [selectedInstitute, setSelectedInstitute] = useState(null);
  const [approvalNotes, setApprovalNotes] = useState('');
  const [rejectionNotes, setRejectionNotes] = useState('');

  // Load institutes on mount
  useEffect(() => {
    dispatch(fetchAllInstitutesList({
      skip: pagination.skip,
      limit: pagination.limit,
      status: statusFilter !== 'all' ? statusFilter : '',
      search: searchTerm
    }));
  }, [dispatch, pagination.skip, pagination.limit, statusFilter, searchTerm]);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await dispatch(fetchAllInstitutesList({
        skip: 0,
        limit: pagination.limit,
        status: statusFilter !== 'all' ? statusFilter : '',
        search: searchTerm
      }));
      setPagination(prev => ({ ...prev, skip: 0 }));
    } finally {
      setRefreshing(false);
    }
  };

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);
    setPagination(prev => ({ ...prev, skip: 0 }));
  };

  // Handle filter change
  const handleFilterChange = (filterType, value) => {
    if (filterType === 'status') {
      setStatusFilter(value);
    } else if (filterType === 'type') {
      setTypeFilter(value);
    }
    setPagination(prev => ({ ...prev, skip: 0 }));
  };

  // Handle view profile
  const handleViewProfile = async (instituteId) => {
    setSelectedInstituteId(instituteId);
    setShowProfileModal(true);
    await dispatch(fetchInstituteProfileById(instituteId));
  };

  // Close profile modal
  const closeProfileModal = () => {
    setShowProfileModal(false);
    setSelectedInstituteId(null);
  };

  // Handle approve action
  const handleApproveAction = (institute) => {
    setSelectedInstitute(institute);
    setShowApprovalModal(true);
    setApprovalNotes('');
  };

  // Handle reject action
  const handleRejectAction = (institute) => {
    setSelectedInstitute(institute);
    setShowRejectionModal(true);
    setRejectionNotes('');
  };

  // Handle approval submission
  const handleApprovalSubmit = async () => {
    if (!selectedInstitute) return;

    try {
      await dispatch(approveInstituteAdmin({
        institute_id: selectedInstitute.id,
        verification_notes: approvalNotes
      })).unwrap();

      alert('Institute approved successfully!');
      setShowApprovalModal(false);
      setApprovalNotes('');
      setSelectedInstitute(null);

      // Refresh the list
      dispatch(fetchAllInstitutesList({
        skip: pagination.skip,
        limit: pagination.limit,
        status: statusFilter !== 'all' ? statusFilter : '',
        search: searchTerm
      }));
    } catch (error) {
      console.error('Failed to approve institute:', error);
      alert(`Failed to approve institute: ${error.message || error}`);
    }
  };

  // Handle rejection submission
  const handleRejectionSubmit = async () => {
    if (!selectedInstitute || !rejectionNotes.trim()) return;

    try {
      await dispatch(rejectInstituteAdmin({
        institute_id: selectedInstitute.id,
        verification_notes: rejectionNotes
      })).unwrap();

      alert('Institute rejected successfully!');
      setShowRejectionModal(false);
      setRejectionNotes('');
      setSelectedInstitute(null);

      // Refresh the list
      dispatch(fetchAllInstitutesList({
        skip: pagination.skip,
        limit: pagination.limit,
        status: statusFilter !== 'all' ? statusFilter : '',
        search: searchTerm
      }));
    } catch (error) {
      console.error('Failed to reject institute:', error);
      alert(`Failed to reject institute: ${error.message || error}`);
    }
  };

  // Close approval modal
  const closeApprovalModal = () => {
    setShowApprovalModal(false);
    setApprovalNotes('');
    setSelectedInstitute(null);
  };

  // Close rejection modal
  const closeRejectionModal = () => {
    setShowRejectionModal(false);
    setRejectionNotes('');
    setSelectedInstitute(null);
  };

  // Get status badge
  const getStatusBadge = (status) => {
    const statusConfig = {
      approved: { color: 'bg-green-100 text-green-800', icon: FiCheck, label: 'Approved' },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: FiClock, label: 'Pending' },
      rejected: { color: 'bg-red-100 text-red-800', icon: FiX, label: 'Rejected' },
      draft: { color: 'bg-gray-100 text-gray-800', icon: FiFileText, label: 'Draft' }
    };

    const config = statusConfig[status] || statusConfig.draft;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </span>
    );
  };

  // Load more institutes
  const loadMore = () => {
    setPagination(prev => ({
      ...prev,
      skip: prev.skip + prev.limit
    }));
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <FiHome className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading institutes</h3>
          <p className="mt-1 text-sm text-gray-500">
            {typeof error === 'string' ? error : error?.detail || error?.message || 'An error occurred'}
          </p>
          <button
            onClick={handleRefresh}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <FiRefreshCw className="h-4 w-4 mr-2" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">All Institutes</h1>
          <p className="text-gray-600">Manage and view all registered institutes</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <FiRefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <SearchFilterCard
        searchTerm={searchTerm}
        onSearchChange={handleSearch}
        searchPlaceholder="Search institutes by name, city, or type..."
        filters={[
          {
            label: 'Status',
            value: statusFilter,
            onChange: (value) => handleFilterChange('status', value),
            options: [
              { value: 'all', label: 'All Status' },
              { value: 'approved', label: 'Approved' },
              { value: 'pending', label: 'Pending' },
              { value: 'rejected', label: 'Rejected' },
              { value: 'draft', label: 'Draft' }
            ]
          },
          {
            label: 'Type',
            value: typeFilter,
            onChange: (value) => handleFilterChange('type', value),
            options: [
              { value: 'all', label: 'All Types' },
              { value: 'university', label: 'University' },
              { value: 'college', label: 'College' },
              { value: 'school', label: 'School' },
              { value: 'training_center', label: 'Training Center' },
              { value: 'research_institute', label: 'Research Institute' },
              { value: 'vocational_school', label: 'Vocational School' },
              { value: 'online_platform', label: 'Online Platform' },
              { value: 'other', label: 'Other' }
            ]
          }
        ]}
        resultsCount={institutes.length}
        resultsType="institutes"
        showViewToggle={false}
      />

      {/* Loading State */}
      {loading && institutes.length === 0 && (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      )}

      {/* Institutes List */}
      {institutes.length === 0 && !loading ? (
        <div className="text-center py-12">
          <FiHome className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No institutes found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm ? "Try adjusting your search criteria" : "No institutes have been registered yet."}
          </p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {institutes.map((institute) => (
              <div key={institute.id} className="bg-white rounded-lg shadow border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <div className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      {institute.logo_url ? (
                        <img
                          src={institute.logo_url}
                          alt={institute.institute_name}
                          className="w-12 h-12 rounded-lg object-cover mr-3"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-3">
                          <FiHome className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                      <div className="min-w-0 flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">
                          {institute.institute_name || 'Unnamed Institute'}
                        </h3>
                        <p className="text-sm text-gray-500 capitalize">
                          {institute.institute_type || 'Unknown Type'}
                        </p>
                      </div>
                    </div>
                    {getStatusBadge(institute.verification_status || 'draft')}
                  </div>

                  {/* Details */}
                  <div className="space-y-2 mb-4">
                    {institute.city && institute.state && (
                      <div className="flex items-center text-sm text-gray-500">
                        <FiMapPin className="h-4 w-4 mr-2" />
                        {institute.city}, {institute.state}
                      </div>
                    )}
                    {institute.established_year && (
                      <div className="flex items-center text-sm text-gray-500">
                        <FiCalendar className="h-4 w-4 mr-2" />
                        Established {institute.established_year}
                      </div>
                    )}
                    {institute.created_at && (
                      <div className="flex items-center text-sm text-gray-500">
                        <FiClock className="h-4 w-4 mr-2" />
                        Registered {new Date(institute.created_at).toLocaleDateString()}
                      </div>
                    )}
                  </div>

                  {/* Description */}
                  {institute.description && (
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {institute.description}
                    </p>
                  )}

                  {/* Actions */}
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleViewProfile(institute.id)}
                      className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <FiEye className="h-4 w-4 mr-2" />
                      View Details
                    </button>

                    {/* Show approve/reject buttons only for pending institutes */}
                    {institute.verification_status === 'pending' && (
                      <>
                        <button
                          onClick={() => handleApproveAction(institute)}
                          disabled={approveLoading}
                          className="inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                        >
                          <FiCheck className="h-4 w-4 mr-1" />
                          Approve
                        </button>
                        <button
                          onClick={() => handleRejectAction(institute)}
                          disabled={rejectLoading}
                          className="inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                        >
                          <FiX className="h-4 w-4 mr-1" />
                          Reject
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Load More Button */}
          {institutes.length < total && (
            <div className="text-center">
              <button
                onClick={loadMore}
                disabled={loading}
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Loading...
                  </>
                ) : (
                  <>
                    Load More ({institutes.length} of {total})
                  </>
                )}
              </button>
            </div>
          )}
        </>
      )}

      {/* Institute Profile Modal */}
      {showProfileModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              {/* Modal Header */}
              <div className="flex items-center justify-between pb-4 border-b">
                <h3 className="text-lg font-semibold text-gray-900">
                  Institute Profile Details
                </h3>
                <button
                  onClick={closeProfileModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FiX className="h-6 w-6" />
                </button>
              </div>

              {/* Modal Content */}
              <div className="mt-4 max-h-96 overflow-y-auto">
                {profileLoading ? (
                  <div className="flex justify-center py-8">
                    <LoadingSpinner size="lg" />
                  </div>
                ) : selectedProfile ? (
                  <div className="space-y-6">
                    {/* Basic Information */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">Basic Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Institute Name</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedProfile.institute_name || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Type</label>
                          <p className="mt-1 text-sm text-gray-900 capitalize">{selectedProfile.institute_type || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Established Year</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedProfile.established_year || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Verification Status</label>
                          <div className="mt-1">
                            {getStatusBadge(selectedProfile.verification_status || 'draft')}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Contact Information */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">Contact Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Address</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedProfile.address || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">City, State</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {selectedProfile.city && selectedProfile.state
                              ? `${selectedProfile.city}, ${selectedProfile.state}`
                              : 'N/A'}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Website</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {selectedProfile.website ? (
                              <a href={selectedProfile.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                                {selectedProfile.website}
                              </a>
                            ) : 'N/A'}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Phone</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedProfile.phone || 'N/A'}</p>
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    {selectedProfile.description && (
                      <div>
                        <h4 className="text-md font-medium text-gray-900 mb-3">Description</h4>
                        <p className="text-sm text-gray-900">{selectedProfile.description}</p>
                      </div>
                    )}

                    {/* Documents */}
                    {selectedProfile.documents && selectedProfile.documents.length > 0 && (
                      <div>
                        <h4 className="text-md font-medium text-gray-900 mb-3">Verification Documents</h4>
                        <div className="space-y-3">
                          {selectedProfile.documents.map((doc, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-3">
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="text-sm font-medium text-gray-900">{doc.document_name}</p>
                                  <p className="text-xs text-gray-500 capitalize">{doc.document_type}</p>
                                  {doc.description && (
                                    <p className="text-xs text-gray-600 mt-1">{doc.description}</p>
                                  )}
                                </div>
                                <div className="flex items-center space-x-2">
                                  {doc.verified ? (
                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                      <FiCheck className="h-3 w-3 mr-1" />
                                      Verified
                                    </span>
                                  ) : (
                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                      <FiClock className="h-3 w-3 mr-1" />
                                      Pending
                                    </span>
                                  )}
                                  {doc.document_url && (
                                    <a
                                      href={doc.document_url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-blue-600 hover:text-blue-800"
                                    >
                                      <FiEye className="h-4 w-4" />
                                    </a>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Failed to load institute profile</p>
                  </div>
                )}
              </div>

              {/* Modal Footer */}
              <div className="flex justify-end pt-4 border-t mt-4">
                <button
                  onClick={closeProfileModal}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Approval Modal */}
      {showApprovalModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Approve Institute
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Are you sure you want to approve "{selectedInstitute?.institute_name}"?
              </p>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Approval Notes (Optional)
                </label>
                <textarea
                  value={approvalNotes}
                  onChange={(e) => setApprovalNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add any notes about the approval..."
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={closeApprovalModal}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={handleApprovalSubmit}
                  disabled={approveLoading}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                >
                  {approveLoading ? 'Approving...' : 'Approve'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Rejection Modal */}
      {showRejectionModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Reject Institute
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Are you sure you want to reject "{selectedInstitute?.institute_name}"?
              </p>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rejection Reason <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={rejectionNotes}
                  onChange={(e) => setRejectionNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Please provide a reason for rejection..."
                  required
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={closeRejectionModal}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRejectionSubmit}
                  disabled={rejectLoading || !rejectionNotes.trim()}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                >
                  {rejectLoading ? 'Rejecting...' : 'Reject'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminInstitutes;
