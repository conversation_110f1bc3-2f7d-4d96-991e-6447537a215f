import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>ontainer, Stack } from '../../components/ui/layout';
import { DashboardGrid, QuickActions } from '../../components/dashboard';
import {
  FiDollarSign,
  FiBookOpen,
  FiTrendingUp,
  FiSettings,
  FiEye,
  FiPlus
} from 'react-icons/fi';

function SponsorDashboard() {
  const navigate = useNavigate();

  const stats = useMemo(() => [
    {
      key: 'funding',
      title: 'Total Funding',
      value: '$125,000',
      icon: FiDollarSign,
      color: 'green',
      onClick: () => navigate('/sponsor/funding')
    },
    {
      key: 'institutes',
      title: 'Sponsored Institutes',
      value: '12',
      icon: FiBookOpen,
      color: 'blue',
      onClick: () => navigate('/sponsor/institutes')
    },
    {
      key: 'growth',
      title: 'Impact Growth',
      value: '+18.2%',
      icon: FiTrendingUp,
      color: 'purple'
    },
    {
      key: 'active',
      title: 'Active Programs',
      value: '8',
      icon: FiPlus,
      color: 'indigo'
    }
  ], [navigate]);

  const quickActions = useMemo(() => [
    {
      key: 'view-institutes',
      title: 'View Institutes',
      description: 'Browse sponsored institutes',
      icon: FiEye,
      color: 'blue',
      onClick: () => navigate('/sponsor/institutes')
    },
    {
      key: 'manage-funding',
      title: 'Manage Funding',
      description: 'Track and allocate funds',
      icon: FiDollarSign,
      color: 'green',
      onClick: () => navigate('/sponsor/funding')
    },
    {
      key: 'sponsor-settings',
      title: 'Settings',
      description: 'Configure sponsor preferences',
      icon: FiSettings,
      color: 'purple',
      onClick: () => navigate('/sponsor/settings')
    }
  ], [navigate]);

  return (
    <PageContainer>
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
          Sponsor Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Track your sponsorship impact and funding
        </p>
      </div>

      <Stack gap="lg">
        <DashboardGrid stats={stats} />
        <QuickActions actions={quickActions} />
      </Stack>
    </PageContainer>
  );
}

export default SponsorDashboard;
