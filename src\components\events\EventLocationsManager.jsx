import React, { useState } from 'react';
import {
  FiMapPin,
  FiPlus,
  FiEdit,
  FiTrash2,
  FiSave,
  FiX,
  FiWifi,
  FiMonitor,
  FiMic,
  FiCoffee,
  FiTruck
} from 'react-icons/fi';

const EventLocationsManager = ({ 
  locations = [], 
  onAddLocation, 
  onUpdateLocation, 
  onDeleteLocation,
  className = '' 
}) => {
  const [isAddingLocation, setIsAddingLocation] = useState(false);
  const [editingLocation, setEditingLocation] = useState(null);
  const [newLocation, setNewLocation] = useState({
    name: '',
    address: '',
    capacity: '',
    facilities: [],
    description: '',
    contact_person: '',
    contact_phone: '',
    contact_email: ''
  });

  const availableFacilities = [
    { id: 'wifi', label: 'WiFi', icon: FiWifi },
    { id: 'projector', label: 'Projector', icon: FiMonitor },
    { id: 'microphone', label: 'Microphone', icon: <PERSON>Mic },
    { id: 'catering', label: 'Catering', icon: Fi<PERSON>offee },
    { id: 'parking', label: 'Parking', icon: FiTruck }
  ];

  const handleAddLocation = () => {
    if (newLocation.name && newLocation.address) {
      onAddLocation(newLocation);
      setNewLocation({
        name: '',
        address: '',
        capacity: '',
        facilities: [],
        description: '',
        contact_person: '',
        contact_phone: '',
        contact_email: ''
      });
      setIsAddingLocation(false);
    }
  };

  const handleUpdateLocation = (locationId, updatedData) => {
    onUpdateLocation(locationId, updatedData);
    setEditingLocation(null);
  };

  const handleFacilityToggle = (facilityId, isEditing = false) => {
    if (isEditing && editingLocation) {
      const updatedFacilities = editingLocation.facilities.includes(facilityId)
        ? editingLocation.facilities.filter(f => f !== facilityId)
        : [...editingLocation.facilities, facilityId];
      setEditingLocation({ ...editingLocation, facilities: updatedFacilities });
    } else {
      const updatedFacilities = newLocation.facilities.includes(facilityId)
        ? newLocation.facilities.filter(f => f !== facilityId)
        : [...newLocation.facilities, facilityId];
      setNewLocation({ ...newLocation, facilities: updatedFacilities });
    }
  };

  const LocationForm = ({ location, isEditing, onSave, onCancel }) => (
    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Location Name *
          </label>
          <input
            type="text"
            value={location.name}
            onChange={(e) => isEditing 
              ? setEditingLocation({ ...location, name: e.target.value })
              : setNewLocation({ ...location, name: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="e.g., Main Auditorium"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Capacity
          </label>
          <input
            type="number"
            value={location.capacity}
            onChange={(e) => isEditing 
              ? setEditingLocation({ ...location, capacity: e.target.value })
              : setNewLocation({ ...location, capacity: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="e.g., 500"
          />
        </div>
        
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Address *
          </label>
          <input
            type="text"
            value={location.address}
            onChange={(e) => isEditing 
              ? setEditingLocation({ ...location, address: e.target.value })
              : setNewLocation({ ...location, address: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="e.g., Building A, Room 101"
          />
        </div>
        
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Facilities
          </label>
          <div className="flex flex-wrap gap-2">
            {availableFacilities.map((facility) => {
              const Icon = facility.icon;
              const isSelected = location.facilities.includes(facility.id);
              return (
                <button
                  key={facility.id}
                  type="button"
                  onClick={() => handleFacilityToggle(facility.id, isEditing)}
                  className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isSelected
                      ? 'bg-blue-100 text-blue-800 border border-blue-300'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {facility.label}
                </button>
              );
            })}
          </div>
        </div>
        
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            value={location.description}
            onChange={(e) => isEditing 
              ? setEditingLocation({ ...location, description: e.target.value })
              : setNewLocation({ ...location, description: e.target.value })
            }
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Additional details about the location..."
          />
        </div>
      </div>
      
      <div className="flex justify-end space-x-3 mt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <FiX className="h-4 w-4 mr-2 inline" />
          Cancel
        </button>
        <button
          type="button"
          onClick={onSave}
          className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
        >
          <FiSave className="h-4 w-4 mr-2 inline" />
          {isEditing ? 'Update' : 'Add'} Location
        </button>
      </div>
    </div>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Event Locations</h3>
        <button
          onClick={() => setIsAddingLocation(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <FiPlus className="h-4 w-4 mr-2" />
          Add Location
        </button>
      </div>

      {/* Add Location Form */}
      {isAddingLocation && (
        <LocationForm
          location={newLocation}
          isEditing={false}
          onSave={handleAddLocation}
          onCancel={() => setIsAddingLocation(false)}
        />
      )}

      {/* Locations List */}
      <div className="space-y-4">
        {locations.map((location) => (
          <div key={location.id} className="bg-white border border-gray-200 rounded-lg p-4">
            {editingLocation?.id === location.id ? (
              <LocationForm
                location={editingLocation}
                isEditing={true}
                onSave={() => handleUpdateLocation(location.id, editingLocation)}
                onCancel={() => setEditingLocation(null)}
              />
            ) : (
              <div>
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">{location.name}</h4>
                    <div className="flex items-center text-sm text-gray-500 mt-1">
                      <FiMapPin className="h-4 w-4 mr-1" />
                      {location.address}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setEditingLocation(location)}
                      className="text-gray-600 hover:text-gray-800"
                      title="Edit Location"
                    >
                      <FiEdit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onDeleteLocation(location.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Delete Location"
                    >
                      <FiTrash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {location.capacity && (
                  <p className="text-sm text-gray-600 mb-2">
                    Capacity: {location.capacity} people
                  </p>
                )}

                {location.description && (
                  <p className="text-sm text-gray-600 mb-3">{location.description}</p>
                )}

                {location.facilities && location.facilities.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {location.facilities.map((facilityId) => {
                      const facility = availableFacilities.find(f => f.id === facilityId);
                      if (!facility) return null;
                      const Icon = facility.icon;
                      return (
                        <span
                          key={facilityId}
                          className="inline-flex items-center px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                        >
                          <Icon className="h-3 w-3 mr-1" />
                          {facility.label}
                        </span>
                      );
                    })}
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {locations.length === 0 && !isAddingLocation && (
        <div className="text-center py-8 text-gray-500">
          <FiMapPin className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p>No locations added yet. Create your first event location.</p>
        </div>
      )}
    </div>
  );
};

export default EventLocationsManager;
