import { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { 
  canPurchaseEventTickets,
  getPaymentAccessMessage,
  getAvailablePaymentMethods,
  validatePaymentAmount,
  getPaymentFlowConfig,
  canUserMakePayments
} from '../utils/payment/paymentAccessControl';
import { getCurrentUser } from '../utils/helpers/authHelpers';
import { selectCurrentUser } from '../store/slices/userSlice';

/**
 * Custom hook for managing payment access control
 * Provides comprehensive payment access checking and configuration
 */
export const usePaymentAccess = (event = null) => {
  // Redux state
  const reduxUser = useSelector(selectCurrentUser);
  
  // Local state
  const [currentUser, setCurrentUser] = useState(null);
  const [paymentAccess, setPaymentAccess] = useState(null);
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState([]);
  const [paymentFlowConfig, setPaymentFlowConfig] = useState(null);
  const [loading, setLoading] = useState(true);

  // Initialize user data
  useEffect(() => {
    const user = reduxUser || getCurrentUser();
    setCurrentUser(user);
    setLoading(false);
  }, [reduxUser]);

  // Update payment access when user or event changes
  useEffect(() => {
    if (currentUser && event) {
      updatePaymentAccess();
    } else if (currentUser) {
      // Update general payment capabilities even without specific event
      updateGeneralPaymentAccess();
    }
  }, [currentUser, event]);

  const updatePaymentAccess = useCallback(() => {
    if (!currentUser || !event) return;

    try {
      // Check payment access for specific event
      const access = getPaymentAccessMessage(event, currentUser);
      setPaymentAccess(access);

      // Get available payment methods
      const methods = getAvailablePaymentMethods(currentUser);
      setAvailablePaymentMethods(methods);

      // Get payment flow configuration
      const flowConfig = getPaymentFlowConfig(currentUser);
      setPaymentFlowConfig(flowConfig);

    } catch (error) {
      console.error('Error updating payment access:', error);
      setPaymentAccess({
        canPurchase: false,
        reason: 'error',
        type: 'error',
        title: 'Access Error',
        message: 'Unable to determine payment access',
        showPaymentButton: false
      });
    }
  }, [currentUser, event]);

  const updateGeneralPaymentAccess = useCallback(() => {
    if (!currentUser) return;

    try {
      const userRole = currentUser.user_type || currentUser.role;
      const canPay = canUserMakePayments(userRole);

      // Get available payment methods
      const methods = getAvailablePaymentMethods(currentUser);
      setAvailablePaymentMethods(methods);

      // Get payment flow configuration
      const flowConfig = getPaymentFlowConfig(currentUser);
      setPaymentFlowConfig(flowConfig);

      // Set general payment access
      setPaymentAccess({
        canPurchase: canPay,
        reason: canPay ? 'allowed' : 'role_restricted',
        type: canPay ? 'success' : 'warning',
        title: canPay ? 'Payment Available' : 'Payment Restricted',
        message: canPay 
          ? 'You can purchase event tickets' 
          : `${userRole} users cannot purchase event tickets`,
        showPaymentButton: canPay
      });

    } catch (error) {
      console.error('Error updating general payment access:', error);
    }
  }, [currentUser]);

  // Check if user can purchase tickets for a specific event
  const checkEventAccess = useCallback((targetEvent) => {
    if (!currentUser) return null;
    return canPurchaseEventTickets(targetEvent, currentUser);
  }, [currentUser]);

  // Validate payment amount
  const validateAmount = useCallback((amount) => {
    if (!currentUser) return { valid: false, message: 'User not found' };
    return validatePaymentAmount(amount, currentUser);
  }, [currentUser]);

  // Check if user is event organizer
  const isEventOrganizer = useCallback((targetEvent) => {
    if (!currentUser || !targetEvent) return false;
    const userId = currentUser.id || currentUser.user_id;
    return targetEvent.organizer_id === userId;
  }, [currentUser]);

  // Check if user is admin
  const isAdmin = useCallback(() => {
    if (!currentUser) return false;
    const userRole = currentUser.user_type || currentUser.role;
    return userRole?.toLowerCase() === 'admin';
  }, [currentUser]);

  // Check if user is institute
  const isInstitute = useCallback(() => {
    if (!currentUser) return false;
    const userRole = currentUser.user_type || currentUser.role;
    return userRole?.toLowerCase() === 'institute';
  }, [currentUser]);

  // Get user role
  const getUserRole = useCallback(() => {
    if (!currentUser) return null;
    return currentUser.user_type || currentUser.role;
  }, [currentUser]);

  // Get payment button configuration
  const getPaymentButtonConfig = useCallback(() => {
    if (!paymentAccess) return null;

    const baseConfig = {
      show: paymentAccess.showPaymentButton,
      enabled: paymentAccess.canPurchase && !loading,
      text: 'Purchase Tickets',
      variant: 'primary'
    };

    // Customize based on access reason
    switch (paymentAccess.reason) {
      case 'admin_access':
        return {
          ...baseConfig,
          show: true,
          text: 'Admin Access',
          variant: 'secondary',
          enabled: false
        };
      
      case 'event_organizer':
        return {
          ...baseConfig,
          show: true,
          text: 'Event Organizer',
          variant: 'secondary',
          enabled: false
        };
      
      case 'role_restricted':
        return {
          ...baseConfig,
          show: false
        };
      
      default:
        return baseConfig;
    }
  }, [paymentAccess, loading]);

  // Get access status summary
  const getAccessSummary = useCallback(() => {
    if (!currentUser) {
      return {
        status: 'unauthenticated',
        message: 'Please log in to purchase tickets',
        canProceed: false
      };
    }

    if (!paymentAccess) {
      return {
        status: 'loading',
        message: 'Checking payment access...',
        canProceed: false
      };
    }

    return {
      status: paymentAccess.canPurchase ? 'allowed' : 'restricted',
      message: paymentAccess.message,
      canProceed: paymentAccess.canPurchase,
      reason: paymentAccess.reason,
      type: paymentAccess.type
    };
  }, [currentUser, paymentAccess]);

  return {
    // User information
    currentUser,
    userRole: getUserRole(),
    isAdmin: isAdmin(),
    isInstitute: isInstitute(),
    
    // Payment access
    paymentAccess,
    canPurchase: paymentAccess?.canPurchase || false,
    accessReason: paymentAccess?.reason,
    
    // Payment configuration
    availablePaymentMethods,
    paymentFlowConfig,
    
    // Utility functions
    checkEventAccess,
    validateAmount,
    isEventOrganizer,
    getPaymentButtonConfig,
    getAccessSummary,
    
    // State
    loading,
    
    // Actions
    refreshAccess: updatePaymentAccess
  };
};

/**
 * Hook for checking payment access for multiple events
 */
export const useMultiEventPaymentAccess = (events = []) => {
  const [eventAccessMap, setEventAccessMap] = useState({});
  const { currentUser } = usePaymentAccess();

  useEffect(() => {
    if (currentUser && events.length > 0) {
      const accessMap = {};
      
      events.forEach(event => {
        if (event && event.id) {
          accessMap[event.id] = canPurchaseEventTickets(event, currentUser);
        }
      });
      
      setEventAccessMap(accessMap);
    }
  }, [currentUser, events]);

  const getEventAccess = useCallback((eventId) => {
    return eventAccessMap[eventId] || null;
  }, [eventAccessMap]);

  const canPurchaseEvent = useCallback((eventId) => {
    const access = getEventAccess(eventId);
    return access?.canPurchase || false;
  }, [getEventAccess]);

  return {
    eventAccessMap,
    getEventAccess,
    canPurchaseEvent,
    currentUser
  };
};

export default usePaymentAccess;
