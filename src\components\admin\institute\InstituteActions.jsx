import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiCheck, FiX } from 'react-icons/fi';
import { Card, Stack } from '../../ui/layout';
import { LoadingSpinner } from '../../ui';
import {
  approveInstituteAdmin,
  rejectInstituteAdmin,
  selectAdminApprovalLoading,
  selectAdminRejectionLoading
} from '../../../store/slices/InstituteProfileSlice';

const ActionModal = ({ isOpen, onClose, title, onConfirm, loading, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="max-w-md w-full mx-4">
        <h3 className="text-lg font-semibold mb-4">{title}</h3>
        {children}
        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? <LoadingSpinner size="sm" /> : 'Confirm'}
          </button>
        </div>
      </Card>
    </div>
  );
};

const InstituteActions = ({ institute, profile }) => {
  const dispatch = useDispatch();
  const [approvalNotes, setApprovalNotes] = useState('');
  const [rejectionNotes, setRejectionNotes] = useState('');
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  
  const approveLoading = useSelector(selectAdminApprovalLoading);
  const rejectLoading = useSelector(selectAdminRejectionLoading);

  const handleApprove = async () => {
    try {
      await dispatch(approveInstituteAdmin({
        instituteId: institute.id,
        notes: approvalNotes
      })).unwrap();
      setShowApprovalModal(false);
      setApprovalNotes('');
    } catch (error) {
      console.error('Approval failed:', error);
    }
  };

  const handleReject = async () => {
    try {
      await dispatch(rejectInstituteAdmin({
        instituteId: institute.id,
        notes: rejectionNotes
      })).unwrap();
      setShowRejectionModal(false);
      setRejectionNotes('');
    } catch (error) {
      console.error('Rejection failed:', error);
    }
  };

  if (profile.verification_status === 'approved') {
    return (
      <Card className="bg-green-50 border-green-200">
        <div className="flex items-center">
          <FiCheck className="h-5 w-5 text-green-600 mr-2" />
          <span className="text-green-800 font-medium">Institute Approved</span>
        </div>
      </Card>
    );
  }

  if (profile.verification_status === 'rejected') {
    return (
      <Card className="bg-red-50 border-red-200">
        <div className="flex items-center">
          <FiX className="h-5 w-5 text-red-600 mr-2" />
          <span className="text-red-800 font-medium">Institute Rejected</span>
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Actions</h3>
        <Stack direction="row" gap="sm">
          <button
            onClick={() => setShowApprovalModal(true)}
            className="flex-1 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
          >
            <FiCheck className="h-4 w-4 mr-2 inline" />
            Approve
          </button>
          <button
            onClick={() => setShowRejectionModal(true)}
            className="flex-1 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            <FiX className="h-4 w-4 mr-2 inline" />
            Reject
          </button>
        </Stack>
      </Card>

      <ActionModal
        isOpen={showApprovalModal}
        onClose={() => setShowApprovalModal(false)}
        title="Approve Institute"
        onConfirm={handleApprove}
        loading={approveLoading}
      >
        <textarea
          value={approvalNotes}
          onChange={(e) => setApprovalNotes(e.target.value)}
          placeholder="Add approval notes (optional)"
          className="w-full p-3 border rounded resize-none"
          rows={3}
        />
      </ActionModal>

      <ActionModal
        isOpen={showRejectionModal}
        onClose={() => setShowRejectionModal(false)}
        title="Reject Institute"
        onConfirm={handleReject}
        loading={rejectLoading}
      >
        <textarea
          value={rejectionNotes}
          onChange={(e) => setRejectionNotes(e.target.value)}
          placeholder="Reason for rejection (required)"
          className="w-full p-3 border rounded resize-none"
          rows={3}
          required
        />
      </ActionModal>
    </>
  );
};

export default InstituteActions;
