# 🤝 Collaboration Integration Documentation

## Overview
This document outlines the complete integration of the 4 collaboration APIs for institute-mentor invitations and partnerships.

## 📋 API Endpoints Integrated

### 1. **GET** `/api/institute/mentors/invites/sent`
- **Purpose**: <PERSON>tch sent invitations
- **Parameters**: `page`, `size`
- **Response**: List of sent invitations with pagination

### 2. **GET** `/api/institute/mentors/invites/received`
- **Purpose**: <PERSON><PERSON> received invitations
- **Parameters**: `page`, `size`
- **Response**: List of received invitations with pagination

### 3. **POST** `/api/institute/mentors/invite`
- **Purpose**: Send new invitation
- **Body**: `receiver_id`, `hourly_rate`, `hours_per_week`, `received_by`
- **Response**: Created invitation object

### 4. **POST** `/api/institute/mentors/invite/{id}/respond`
- **Purpose**: Respond to invitation (accept/reject)
- **Parameters**: `invitation_id`
- **Body**: Response data
- **Response**: Success message

## 🏗️ Architecture

### Service Layer
- **File**: `src/services/collaborationService.js`
- **Functions**:
  - `getSentInvitations(page, size)`
  - `getReceivedInvitations(page, size)`
  - `sendInvitation(invitationData)`
  - `respondToInvitation(invitationId, response)`
  - `getAllInvitations(page, size)`

### Redux State Management
- **File**: `src/store/slices/collaborationSlice.js`
- **State Structure**:
  ```javascript
  {
    sentInvitations: { data, total, page, size, loading, error },
    receivedInvitations: { data, total, page, size, loading, error },
    sendInvitation: { loading, error, success },
    respondInvitation: { loading, error, success }
  }
  ```

### Components
- **CollaborationDashboard**: Main dashboard component
- **InviteModal**: Modal for sending invitations
- **ActionModal**: Reusable modal for confirmations

## 🎯 Features Implemented

### For Both Institute & Mentor Users:

#### 1. **Dashboard View**
- ✅ Tabbed interface (Received/Sent)
- ✅ Real-time invitation counts
- ✅ Beautiful card-based layout
- ✅ Loading states and error handling

#### 2. **Send Invitations**
- ✅ Modal form with validation
- ✅ Hourly rate and hours per week input
- ✅ Real-time cost calculation
- ✅ Success/error feedback

#### 3. **Manage Received Invitations**
- ✅ Accept/Reject buttons
- ✅ Confirmation modals
- ✅ Invitation details display
- ✅ Status indicators

#### 4. **Invitation Cards**
- ✅ Professional design
- ✅ Key information display (rate, hours, date)
- ✅ Action buttons
- ✅ Status indicators

## 🚀 Usage

### Institute Users
```jsx
import InstituteCollaborations from './pages/institute/InstituteCollaborations';

// In your route configuration
<Route path="/institute/collaborations" component={InstituteCollaborations} />
```

### Mentor Users
```jsx
import MentorCollaborations from './pages/mentor/MentorCollaborations';

// In your route configuration
<Route path="/mentor/collaborations" component={MentorCollaborations} />
```

### Direct Component Usage
```jsx
import { CollaborationDashboard } from './components/collaboration';

// For institute
<CollaborationDashboard userType="institute" />

// For mentor
<CollaborationDashboard userType="mentor" />
```

## 🎨 UI/UX Features

### Design Elements
- **Modern card-based layout**
- **Responsive design**
- **Loading spinners**
- **Error states**
- **Empty states with call-to-action**
- **Color-coded status indicators**

### User Experience
- **Intuitive tabbed navigation**
- **One-click actions**
- **Confirmation dialogs**
- **Real-time updates**
- **Form validation**
- **Success/error feedback**

## 🔧 Technical Implementation

### API Integration
```javascript
// Send invitation
const invitationData = {
  receiver_id: "user-uuid",
  hourly_rate: 50.00,
  hours_per_week: 20,
  received_by: "mentor" // or "institute"
};

dispatch(sendInvitation(invitationData));
```

### State Management
```javascript
// Fetch invitations
dispatch(fetchSentInvitations({ page: 1, size: 20 }));
dispatch(fetchReceivedInvitations({ page: 1, size: 20 }));

// Respond to invitation
dispatch(respondToInvitation({
  invitationId: "invitation-uuid",
  response: { status: "accept" }
}));
```

## 📱 Responsive Design
- **Mobile-first approach**
- **Tablet optimization**
- **Desktop enhancement**
- **Touch-friendly interactions**

## 🔒 Security Features
- **Authentication required**
- **Input validation**
- **Error handling**
- **Rate limiting support**

## 🧪 Testing Considerations
- **Unit tests for service functions**
- **Redux action/reducer tests**
- **Component integration tests**
- **API endpoint testing**

## 🚀 Future Enhancements
- **Real-time notifications**
- **Invitation templates**
- **Bulk operations**
- **Advanced filtering**
- **Analytics dashboard**
- **Integration with calendar systems**

## 📊 Data Flow
1. User opens collaboration dashboard
2. Component dispatches fetch actions
3. Service layer calls APIs
4. Redux updates state
5. Components re-render with new data
6. User interactions trigger new API calls
7. State updates reflect changes immediately

## 🎯 Benefits
- **Unified experience** for both user types
- **Reusable components** across the application
- **Consistent API integration** patterns
- **Professional UI/UX** design
- **Scalable architecture** for future features
