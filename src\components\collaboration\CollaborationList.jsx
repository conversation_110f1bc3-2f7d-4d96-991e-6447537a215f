import { useState } from 'react';
import { FiSearch, FiFilter, FiPlus } from 'react-icons/fi';
import CollaborationCard from './CollaborationCard';
import LoadingSpinner from '../ui/LoadingSpinner';
import Pagination from '../ui/Pagination';

const CollaborationList = ({
  collaborations = [],
  loading = false,
  error = null,
  pagination = {},
  onEdit,
  onDelete,
  onViewDetails,
  onPageChange,
  onFilterChange,
  onCreateNew,
  userRole = 'mentor',
  showCreateButton = true
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    // Debounce search if needed
  };

  const handleStatusFilterChange = (e) => {
    const status = e.target.value;
    setStatusFilter(status);
    onFilterChange?.({ status });
  };

  const filteredCollaborations = collaborations.filter(collaboration => {
    const matchesSearch = !searchTerm || 
      (userRole === 'mentor' 
        ? collaboration.institute?.institute_name?.toLowerCase().includes(searchTerm.toLowerCase())
        : collaboration.mentor?.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    const matchesStatus = !statusFilter || collaboration.status?.toLowerCase() === statusFilter.toLowerCase();
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-6 max-w-md mx-auto">
          <h3 className="text-lg font-medium text-red-800 dark:text-red-400 mb-2">
            Error Loading Collaborations
          </h3>
          <p className="text-red-600 dark:text-red-300 mb-4">
            {typeof error === 'string' ? error : error.message || 'An unexpected error occurred'}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            My Collaborations
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your active collaborations and partnerships
          </p>
        </div>
        
        {showCreateButton && onCreateNew && (
          <button
            onClick={onCreateNew}
            className="flex items-center space-x-2 px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors"
          >
            <FiPlus className="w-4 h-4" />
            <span>New Collaboration</span>
          </button>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder={`Search by ${userRole === 'mentor' ? 'institute name' : 'mentor name'}...`}
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-violet-500 focus:border-transparent"
          />
        </div>

        {/* Status Filter */}
        <div className="relative">
          <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <select
            value={statusFilter}
            onChange={handleStatusFilterChange}
            className="pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-violet-500 focus:border-transparent appearance-none min-w-[150px]"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="pending">Pending</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Collaborations Grid */}
      {filteredCollaborations.length === 0 ? (
        <div className="text-center py-12">
          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-8 max-w-md mx-auto">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Collaborations Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {searchTerm || statusFilter 
                ? 'No collaborations match your current filters.'
                : 'You don\'t have any collaborations yet.'}
            </p>
            {showCreateButton && onCreateNew && !searchTerm && !statusFilter && (
              <button
                onClick={onCreateNew}
                className="inline-flex items-center space-x-2 px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors"
              >
                <FiPlus className="w-4 h-4" />
                <span>Create Your First Collaboration</span>
              </button>
            )}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredCollaborations.map((collaboration) => (
            <CollaborationCard
              key={collaboration.id}
              collaboration={collaboration}
              onEdit={onEdit}
              onDelete={onDelete}
              onViewDetails={onViewDetails}
              userRole={userRole}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination.total > 0 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={pagination.page || 1}
            totalPages={Math.ceil(pagination.total / (pagination.size || 20))}
            onPageChange={onPageChange}
            hasNext={pagination.hasNext}
            hasPrev={pagination.hasPrev}
          />
        </div>
      )}
    </div>
  );
};

export default CollaborationList;
