# PayFast Payment Integration Guide

## Overview

This guide provides comprehensive instructions for integrating and testing the PayFast payment system in the EduFair platform. The integration supports event registration payments, subscription payments, and complete payment lifecycle management.

## Prerequisites

### 1. PayFast Account Setup
- Create a PayFast merchant account at [https://www.payfast.co.za](https://www.payfast.co.za)
- Obtain merchant credentials:
  - Merchant ID
  - Merchant Key
  - Passphrase
- Configure sandbox environment for testing

### 2. Environment Configuration
Add the following environment variables to your `.env` file:

```env
# PayFast Configuration
REACT_APP_PAYFAST_MERCHANT_ID=your_merchant_id
REACT_APP_PAYFAST_MERCHANT_KEY=your_merchant_key
REACT_APP_PAYFAST_PASSPHRASE=your_passphrase
REACT_APP_PAYFAST_ENVIRONMENT=sandbox  # or production
REACT_APP_PAYFAST_URL=https://sandbox.payfast.co.za/eng/process

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:8000
```

## Integration Components

### 1. Core Services
- **PaymentService** (`src/services/paymentService.js`): Handles PayFast API interactions
- **EventService** (`src/services/eventService.js`): Enhanced event management with payment support

### 2. Redux State Management
- **PaymentSlice** (`src/store/slices/PaymentSlice.js`): Payment state management
- **EventsSlice** (`src/store/slices/EventsSlice.js`): Enhanced with payment integration

### 3. UI Components
- **PaymentForm** (`src/components/payment/PaymentForm.jsx`): Payment form component
- **PaymentStatus** (`src/components/payment/PaymentStatus.jsx`): Payment status display
- **PaymentConfirmation** (`src/components/payment/PaymentConfirmation.jsx`): Success confirmation
- **PaymentError** (`src/components/payment/PaymentError.jsx`): Error handling

### 4. Pages
- **PaymentPage** (`src/pages/payment/PaymentPage.jsx`): Main payment processing
- **PaymentSuccessPage** (`src/pages/payment/PaymentSuccessPage.jsx`): Success handling
- **PaymentCancelPage** (`src/pages/payment/PaymentCancelPage.jsx`): Cancellation handling
- **PaymentStatusPage** (`src/pages/payment/PaymentStatusPage.jsx`): Status checking

## Testing Checklist

### 1. Unit Tests
Run the payment integration tests:
```bash
npm test src/tests/payment/PaymentIntegration.test.js
```

### 2. Component Tests
- [ ] PaymentForm renders correctly with event data
- [ ] PaymentForm validates user input
- [ ] PaymentStatus displays correct status information
- [ ] PaymentConfirmation shows success details
- [ ] PaymentError handles error scenarios

### 3. Service Tests
- [ ] PaymentService creates payments successfully
- [ ] PaymentService handles API errors gracefully
- [ ] PaymentService polls payment status correctly
- [ ] EventService integrates with payment flow

### 4. Integration Tests
- [ ] Free event registration works without payment
- [ ] Paid event registration redirects to PayFast
- [ ] Payment success flow updates registration status
- [ ] Payment cancellation handles gracefully
- [ ] Payment status polling works correctly

## Manual Testing Scenarios

### Scenario 1: Free Event Registration
1. Navigate to a free event (registration_fee = 0)
2. Click "Register" button
3. Fill out registration form
4. Submit registration
5. Verify immediate confirmation without payment

### Scenario 2: Paid Event Registration
1. Navigate to a paid event (registration_fee > 0)
2. Click "Register & Pay" button
3. Fill out registration form with payment details
4. Submit form
5. Verify redirect to PayFast payment page
6. Complete payment in sandbox
7. Verify return to success page
8. Check registration status is confirmed

### Scenario 3: Payment Cancellation
1. Start paid event registration
2. Proceed to PayFast payment page
3. Cancel payment
4. Verify return to cancellation page
5. Check registration status remains pending

### Scenario 4: Payment Status Checking
1. Navigate to payment status page
2. Enter payment ID
3. Verify status display
4. Test auto-refresh functionality
5. Test manual refresh

## PayFast Sandbox Testing

### Test Card Details
Use these test card details in PayFast sandbox:

**Successful Payment:**
- Card Number: ****************
- Expiry: Any future date
- CVV: Any 3 digits

**Failed Payment:**
- Card Number: ****************
- Expiry: Any future date
- CVV: Any 3 digits

### Test Bank Details
**Successful EFT:**
- Bank: Standard Bank
- Account Type: Cheque
- Account Number: *********

## Error Handling Testing

### 1. Network Errors
- Disconnect internet during payment creation
- Verify error messages display correctly
- Test retry functionality

### 2. API Errors
- Test with invalid payment data
- Test with expired tokens
- Test with insufficient permissions

### 3. PayFast Errors
- Test with invalid merchant credentials
- Test with amounts below minimum
- Test with invalid return URLs

## Performance Testing

### 1. Payment Form Loading
- Measure form render time
- Test with slow network conditions
- Verify loading states

### 2. Status Polling
- Test polling frequency
- Verify polling stops on completion
- Test timeout handling

### 3. Large Event Registration
- Test with high concurrent registrations
- Verify payment queue handling
- Test system under load

## Security Testing

### 1. Data Validation
- Test form input sanitization
- Verify server-side validation
- Test SQL injection prevention

### 2. Authentication
- Test payment access with invalid tokens
- Verify user authorization
- Test session timeout handling

### 3. Payment Data Security
- Verify no sensitive data in logs
- Test HTTPS enforcement
- Verify PCI compliance measures

## Deployment Checklist

### Pre-Production
- [ ] All tests passing
- [ ] Environment variables configured
- [ ] PayFast sandbox testing completed
- [ ] Error handling verified
- [ ] Performance benchmarks met

### Production Deployment
- [ ] Switch to production PayFast environment
- [ ] Update environment variables
- [ ] Configure production URLs
- [ ] Test with real payment methods
- [ ] Monitor payment success rates

### Post-Deployment
- [ ] Monitor payment logs
- [ ] Track conversion rates
- [ ] Monitor error rates
- [ ] Set up alerts for failures

## Troubleshooting

### Common Issues

**Payment Form Not Loading**
- Check API connectivity
- Verify authentication tokens
- Check browser console for errors

**PayFast Redirect Fails**
- Verify return URLs are accessible
- Check PayFast merchant configuration
- Validate form data format

**Payment Status Not Updating**
- Check webhook configuration
- Verify polling intervals
- Check payment ID format

**Registration Not Confirmed**
- Verify payment completion
- Check registration status API
- Review payment-registration linking

### Debug Tools

**Payment Logs**
```javascript
// Enable detailed payment logging
localStorage.setItem('payment_debug', 'true');
```

**Network Monitoring**
- Use browser dev tools Network tab
- Monitor API calls and responses
- Check for failed requests

**State Debugging**
```javascript
// Access Redux state in console
window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__
```

## Support and Documentation

### PayFast Resources
- [PayFast Developer Documentation](https://developers.payfast.co.za/)
- [PayFast API Reference](https://developers.payfast.co.za/api)
- [PayFast Support](https://www.payfast.co.za/help/)

### Internal Resources
- Payment service documentation: `src/services/paymentService.js`
- Component documentation: `src/components/payment/`
- Test examples: `src/tests/payment/`

### Contact Information
- Technical Support: <EMAIL>
- Payment Issues: <EMAIL>
- Emergency Contact: +27 12 345 6789

## Monitoring and Analytics

### Key Metrics to Track
- Payment success rate
- Average payment completion time
- Error rates by type
- User drop-off points
- Revenue by event type

### Monitoring Tools
- Payment dashboard in admin panel
- Real-time payment status monitoring
- Automated error alerts
- Weekly payment reports

## Compliance and Legal

### Data Protection
- POPIA compliance for payment data
- PCI DSS requirements
- Data retention policies
- User consent management

### Financial Regulations
- South African Reserve Bank compliance
- Tax reporting requirements
- Refund policy implementation
- Dispute resolution procedures

---

**Last Updated:** January 2024  
**Version:** 1.0  
**Maintained By:** EduFair Development Team
