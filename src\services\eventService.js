/**
 * Enhanced Event Service
 * 
 * Service for event management with payment integration,
 * registration fees, QR code generation, and enhanced features.
 */

import axios from 'axios';
import API_BASE_URL from '../utils/api/API_URL';
import { handleApiError } from '../utils/helpers/errorHandler';
import logger from '../utils/helpers/logger';

// Base URL for event endpoints - Updated to match API docs
const EVENT_API_BASE = `${API_BASE_URL}/api/events`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Helper function to create auth headers
const getAuthHeaders = () => ({
  'Authorization': `Bearer ${getAuthToken()}`,
  'Content-Type': 'application/json'
});

/**
 * Enhanced Event Service Class
 */
class EventService {
  
  /**
   * Create event with payment support - Updated to match API docs
   * @param {Object} eventData - Event data including registration fees
   * @returns {Promise<Object>} Created event
   */
  async createEventWithPayment(eventData) {
    try {
      logger.info('Creating event with payment support', { eventData });

      const response = await axios.post(
        `${EVENT_API_BASE}`,
        eventData,
        { headers: getAuthHeaders() }
      );

      logger.info('Event created successfully', { eventId: response.data.id });
      return response.data;
    } catch (error) {
      logger.error('Failed to create event', { error: error.message, eventData });
      throw handleApiError(error);
    }
  }

  /**
   * Update event with payment settings
   * @param {string} eventId - Event UUID
   * @param {Object} eventData - Updated event data
   * @returns {Promise<Object>} Updated event
   */
  async updateEventWithPayment(eventId, eventData) {
    try {
      logger.info('Updating event with payment settings', { eventId, eventData });
      
      const response = await axios.put(
        `${EVENT_API_BASE}/${eventId}`,
        eventData,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Event updated successfully', { eventId });
      return response.data;
    } catch (error) {
      logger.error('Failed to update event', { error: error.message, eventId, eventData });
      throw handleApiError(error);
    }
  }

  /**
   * Get event registrations with payment status
   * @param {string} eventId - Event UUID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Registrations with payment info
   */
  async getEventRegistrations(eventId, options = {}) {
    try {
      const {
        skip = 0,
        limit = 20,
        status = null,
        payment_status = null,
        search = null
      } = options;

      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString()
      });

      if (status) params.append('status', status);
      if (payment_status) params.append('payment_status', payment_status);
      if (search) params.append('search', search);

      logger.info('Getting event registrations', { eventId, options });
      
      const response = await axios.get(
        `${EVENT_API_BASE}/${eventId}/registrations?${params}`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Event registrations retrieved successfully', { 
        eventId, 
        count: response.data.registrations?.length 
      });
      return response.data;
    } catch (error) {
      logger.error('Failed to get event registrations', { error: error.message, eventId, options });
      throw handleApiError(error);
    }
  }

  /**
   * Generate QR code for event registration
   * @param {string} registrationId - Registration UUID
   * @returns {Promise<Object>} QR code data
   */
  async generateRegistrationQRCode(registrationId) {
    try {
      logger.info('Generating QR code for registration', { registrationId });
      
      const response = await axios.post(
        `${EVENT_API_BASE}/registrations/${registrationId}/qr-code`,
        {},
        { headers: getAuthHeaders() }
      );
      
      logger.info('QR code generated successfully', { registrationId });
      return response.data;
    } catch (error) {
      logger.error('Failed to generate QR code', { error: error.message, registrationId });
      throw handleApiError(error);
    }
  }

  /**
   * Verify QR code for event check-in
   * @param {string} eventId - Event UUID
   * @param {string} qrCode - QR code data
   * @returns {Promise<Object>} Verification result
   */
  async verifyQRCode(eventId, qrCode) {
    try {
      logger.info('Verifying QR code for check-in', { eventId, qrCode });
      
      const response = await axios.post(
        `${EVENT_API_BASE}/${eventId}/verify-qr`,
        { qr_code: qrCode },
        { headers: getAuthHeaders() }
      );
      
      logger.info('QR code verified successfully', { eventId, qrCode });
      return response.data;
    } catch (error) {
      logger.error('Failed to verify QR code', { error: error.message, eventId, qrCode });
      throw handleApiError(error);
    }
  }

  /**
   * Check-in attendee using QR code
   * @param {string} eventId - Event UUID
   * @param {string} registrationId - Registration UUID
   * @returns {Promise<Object>} Check-in result
   */
  async checkInAttendee(eventId, registrationId) {
    try {
      logger.info('Checking in attendee', { eventId, registrationId });
      
      const response = await axios.post(
        `${EVENT_API_BASE}/${eventId}/check-in`,
        { registration_id: registrationId },
        { headers: getAuthHeaders() }
      );
      
      logger.info('Attendee checked in successfully', { eventId, registrationId });
      return response.data;
    } catch (error) {
      logger.error('Failed to check in attendee', { error: error.message, eventId, registrationId });
      throw handleApiError(error);
    }
  }

  /**
   * Get event analytics with payment data
   * @param {string} eventId - Event UUID
   * @returns {Promise<Object>} Event analytics
   */
  async getEventAnalytics(eventId) {
    try {
      logger.info('Getting event analytics', { eventId });
      
      const response = await axios.get(
        `${EVENT_API_BASE}/${eventId}/analytics`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Event analytics retrieved successfully', { eventId });
      return response.data;
    } catch (error) {
      logger.error('Failed to get event analytics', { error: error.message, eventId });
      throw handleApiError(error);
    }
  }

  /**
   * Export event registrations
   * @param {string} eventId - Event UUID
   * @param {Object} options - Export options
   * @returns {Promise<Blob>} Export file
   */
  async exportEventRegistrations(eventId, options = {}) {
    try {
      const {
        format = 'csv',
        include_payment_data = true,
        include_qr_codes = false
      } = options;

      const params = new URLSearchParams({
        format,
        include_payment_data: include_payment_data.toString(),
        include_qr_codes: include_qr_codes.toString()
      });

      logger.info('Exporting event registrations', { eventId, options });
      
      const response = await axios.get(
        `${EVENT_API_BASE}/${eventId}/export?${params}`,
        { 
          headers: getAuthHeaders(),
          responseType: 'blob'
        }
      );
      
      logger.info('Event registrations exported successfully', { eventId });
      return response.data;
    } catch (error) {
      logger.error('Failed to export event registrations', { error: error.message, eventId, options });
      throw handleApiError(error);
    }
  }

  /**
   * Send event notifications
   * @param {string} eventId - Event UUID
   * @param {Object} notificationData - Notification data
   * @returns {Promise<Object>} Notification result
   */
  async sendEventNotification(eventId, notificationData) {
    try {
      logger.info('Sending event notification', { eventId, notificationData });
      
      const response = await axios.post(
        `${EVENT_API_BASE}/${eventId}/notifications`,
        notificationData,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Event notification sent successfully', { eventId });
      return response.data;
    } catch (error) {
      logger.error('Failed to send event notification', { error: error.message, eventId, notificationData });
      throw handleApiError(error);
    }
  }

  /**
   * Get event payment summary
   * @param {string} eventId - Event UUID
   * @returns {Promise<Object>} Payment summary
   */
  async getEventPaymentSummary(eventId) {
    try {
      logger.info('Getting event payment summary', { eventId });
      
      const response = await axios.get(
        `${EVENT_API_BASE}/${eventId}/payment-summary`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Event payment summary retrieved successfully', { eventId });
      return response.data;
    } catch (error) {
      logger.error('Failed to get event payment summary', { error: error.message, eventId });
      throw handleApiError(error);
    }
  }

  /**
   * Update registration status
   * @param {string} registrationId - Registration UUID
   * @param {string} status - New status
   * @param {string} reason - Reason for status change
   * @returns {Promise<Object>} Updated registration
   */
  async updateRegistrationStatus(registrationId, status, reason = null) {
    try {
      logger.info('Updating registration status', { registrationId, status, reason });
      
      const response = await axios.patch(
        `${EVENT_API_BASE}/registrations/${registrationId}/status`,
        { status, reason },
        { headers: getAuthHeaders() }
      );
      
      logger.info('Registration status updated successfully', { registrationId, status });
      return response.data;
    } catch (error) {
      logger.error('Failed to update registration status', { 
        error: error.message, 
        registrationId, 
        status, 
        reason 
      });
      throw handleApiError(error);
    }
  }

  /**
   * Get registration details with payment info
   * @param {string} registrationId - Registration UUID
   * @returns {Promise<Object>} Registration details
   */
  async getRegistrationDetails(registrationId) {
    try {
      logger.info('Getting registration details', { registrationId });
      
      const response = await axios.get(
        `${EVENT_API_BASE}/registrations/${registrationId}`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Registration details retrieved successfully', { registrationId });
      return response.data;
    } catch (error) {
      logger.error('Failed to get registration details', { error: error.message, registrationId });
      throw handleApiError(error);
    }
  }

  /**
   * Cancel registration with refund processing
   * @param {string} registrationId - Registration UUID
   * @param {Object} cancellationData - Cancellation data
   * @returns {Promise<Object>} Cancellation result
   */
  async cancelRegistrationWithRefund(registrationId, cancellationData) {
    try {
      logger.info('Cancelling registration with refund', { registrationId, cancellationData });
      
      const response = await axios.post(
        `${EVENT_API_BASE}/registrations/${registrationId}/cancel`,
        cancellationData,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Registration cancelled successfully', { registrationId });
      return response.data;
    } catch (error) {
      logger.error('Failed to cancel registration', { 
        error: error.message, 
        registrationId, 
        cancellationData 
      });
      throw handleApiError(error);
    }
  }

  /**
   * Get event capacity and availability
   * @param {string} eventId - Event UUID
   * @returns {Promise<Object>} Capacity information
   */
  async getEventCapacity(eventId) {
    try {
      logger.info('Getting event capacity', { eventId });
      
      const response = await axios.get(
        `${EVENT_API_BASE}/${eventId}/capacity`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Event capacity retrieved successfully', { eventId });
      return response.data;
    } catch (error) {
      logger.error('Failed to get event capacity', { error: error.message, eventId });
      throw handleApiError(error);
    }
  }
}

// Create and export service instance
const eventService = new EventService();
export default eventService;

// Export individual methods for convenience
export const {
  createEventWithPayment,
  updateEventWithPayment,
  getEventRegistrations,
  generateRegistrationQRCode,
  verifyQRCode,
  checkInAttendee,
  getEventAnalytics,
  exportEventRegistrations,
  sendEventNotification,
  getEventPaymentSummary,
  updateRegistrationStatus,
  getRegistrationDetails,
  cancelRegistrationWithRefund,
  getEventCapacity
} = eventService;
