import React from 'react';
import { FiMail, FiPhone, FiMapPin, FiGlobe } from 'react-icons/fi';
import { Card, Stack } from '../../ui/layout';

const ContactField = ({ icon: Icon, label, value, href }) => {
  const content = (
    <div className="flex items-center space-x-3">
      <Icon className="h-5 w-5 text-blue-600 flex-shrink-0" />
      <div>
        <label className="text-sm font-medium text-gray-500">{label}</label>
        <p className="text-gray-900">{value || 'Not provided'}</p>
      </div>
    </div>
  );

  return href ? (
    <a href={href} className="hover:bg-gray-50 p-2 rounded transition-colors">
      {content}
    </a>
  ) : (
    <div className="p-2">{content}</div>
  );
};

const InstituteContactInfo = ({ user, profile }) => {
  return (
    <Card>
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <FiMail className="h-5 w-5 mr-2 text-blue-600" />
        Contact Information
      </h3>
      
      <Stack gap="sm">
        <ContactField
          icon={FiMail}
          label="Email"
          value={user.email}
          href={`mailto:${user.email}`}
        />
        
        <ContactField
          icon={FiPhone}
          label="Phone"
          value={profile.phone}
          href={profile.phone ? `tel:${profile.phone}` : null}
        />
        
        <ContactField
          icon={FiMapPin}
          label="Address"
          value={profile.address}
        />
        
        <ContactField
          icon={FiGlobe}
          label="Website"
          value={profile.website}
          href={profile.website}
        />
      </Stack>
    </Card>
  );
};

export default InstituteContactInfo;
