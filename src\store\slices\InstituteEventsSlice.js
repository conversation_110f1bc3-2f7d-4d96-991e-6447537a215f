import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

// Updated to use correct API endpoint from documentation
const BASE_URL = `${URL}/api/events`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks for institute events APIs

// Fetch all events - Updated to use correct my-events endpoint
export const fetchInstituteEvents = createAsyncThunk(
  "instituteEvents/fetchEvents",
  async ({ skip = 0, limit = 20, status = "" } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      if (status) {
        params.append('status', status);
      }

      // Use the correct my-events endpoint for institutes
      const res = await axios.get(`${BASE_URL}/my-events?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Create new event - Updated to use correct events endpoint
export const createInstituteEvent = createAsyncThunk(
  "instituteEvents/createEvent",
  async (eventData, thunkAPI) => {
    try {
      // Use the main events endpoint for creating events
      const res = await axios.post(`${BASE_URL}`, eventData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get event by ID - Updated to use correct single event endpoint
export const fetchInstituteEventById = createAsyncThunk(
  "instituteEvents/fetchEventById",
  async (eventId, thunkAPI) => {
    try {
      // Use the correct single event endpoint from documentation
      const res = await axios.get(`${BASE_URL}/${eventId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update event
export const updateInstituteEvent = createAsyncThunk(
  "instituteEvents/updateEvent",
  async ({ eventId, eventData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${eventId}`, eventData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete event
export const deleteInstituteEvent = createAsyncThunk(
  "instituteEvents/deleteEvent",
  async (eventId, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/${eventId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return eventId;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Publish event
export const publishInstituteEvent = createAsyncThunk(
  "instituteEvents/publishEvent",
  async (eventId, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/${eventId}/publish`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Cancel event
export const cancelInstituteEvent = createAsyncThunk(
  "instituteEvents/cancelEvent",
  async (eventId, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/${eventId}/cancel`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get event attendees - DISABLED (endpoint doesn't exist)
export const fetchEventAttendees = createAsyncThunk(
  "instituteEvents/fetchAttendees",
  async ({ eventId, skip = 0, limit = 20 } = {}, thunkAPI) => {
    try {
      // TODO: Replace with actual API call when endpoint is available
      console.warn('Event attendees endpoint not available - using mock data');

      await new Promise(resolve => setTimeout(resolve, 300));

      const mockAttendees = {
        data: [
          { id: '1', name: 'John Doe', email: '<EMAIL>', registeredAt: new Date().toISOString() },
          { id: '2', name: 'Jane Smith', email: '<EMAIL>', registeredAt: new Date().toISOString() }
        ],
        total: 2,
        skip: skip,
        limit: limit
      };

      return mockAttendees;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Register user for event
export const registerForEvent = createAsyncThunk(
  "instituteEvents/registerForEvent",
  async ({ eventId, userId }, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/${eventId}/register`, { userId }, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Send event reminder
export const sendEventReminder = createAsyncThunk(
  "instituteEvents/sendReminder",
  async ({ eventId, message }, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/${eventId}/send-reminder`, { message }, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get event analytics - DISABLED (endpoint doesn't exist)
export const fetchEventAnalytics = createAsyncThunk(
  "instituteEvents/fetchAnalytics",
  async (eventId, thunkAPI) => {
    try {
      // TODO: Replace with actual API call when endpoint is available
      console.warn('Event analytics endpoint not available - using mock data');

      await new Promise(resolve => setTimeout(resolve, 300));

      const mockAnalytics = {
        eventId: eventId,
        totalRegistrations: 45,
        totalAttendees: 38,
        attendanceRate: 84.4,
        registrationsByDay: [
          { date: '2024-01-01', count: 12 },
          { date: '2024-01-02', count: 18 },
          { date: '2024-01-03', count: 15 }
        ],
        demographics: {
          students: 25,
          teachers: 10,
          others: 3
        }
      };

      return mockAnalytics;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get event categories - DISABLED (endpoint doesn't exist)
export const fetchEventCategories = createAsyncThunk(
  "instituteEvents/fetchCategories",
  async (_, thunkAPI) => {
    try {
      // TODO: Replace with actual API call when endpoint is available
      console.warn('Event categories endpoint not available - using mock data');

      await new Promise(resolve => setTimeout(resolve, 300));

      const mockCategories = [
        { id: 'workshop', name: 'Workshop', description: 'Educational workshops' },
        { id: 'seminar', name: 'Seminar', description: 'Academic seminars' },
        { id: 'conference', name: 'Conference', description: 'Professional conferences' },
        { id: 'competition', name: 'Competition', description: 'Academic competitions' }
      ];

      return mockCategories;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial state
const initialState = {
  // Events list
  events: {
    data: [],
    total: 0,
    pagination: {
      skip: 0,
      limit: 20,
      hasMore: true,
    },
  },
  eventsLoading: false,
  eventsError: null,

  // Current event
  currentEvent: null,
  currentEventLoading: false,
  currentEventError: null,

  // Event creation/update
  createLoading: false,
  createError: null,
  createSuccess: false,
  updateLoading: false,
  updateError: null,
  updateSuccess: false,

  // Event actions
  publishLoading: false,
  publishError: null,
  cancelLoading: false,
  cancelError: null,
  deleteLoading: false,
  deleteError: null,

  // Event attendees
  attendees: {
    data: [],
    total: 0,
    pagination: {
      skip: 0,
      limit: 20,
      hasMore: true,
    },
  },
  attendeesLoading: false,
  attendeesError: null,

  // Event analytics
  analytics: {},
  analyticsLoading: false,
  analyticsError: null,

  // Event categories
  categories: [],
  categoriesLoading: false,
  categoriesError: null,

  // Registration
  registrationLoading: false,
  registrationError: null,
  registrationSuccess: false,

  // Reminders
  reminderLoading: false,
  reminderError: null,
  reminderSuccess: false,
};

// Institute Events Slice
const instituteEventsSlice = createSlice({
  name: "instituteEvents",
  initialState,
  reducers: {
    // Clear all errors
    clearErrors: (state) => {
      state.eventsError = null;
      state.currentEventError = null;
      state.createError = null;
      state.updateError = null;
      state.publishError = null;
      state.cancelError = null;
      state.deleteError = null;
      state.attendeesError = null;
      state.analyticsError = null;
      state.categoriesError = null;
      state.registrationError = null;
      state.reminderError = null;
    },

    // Clear success states
    clearSuccessStates: (state) => {
      state.createSuccess = false;
      state.updateSuccess = false;
      state.registrationSuccess = false;
      state.reminderSuccess = false;
    },

    // Reset current event
    resetCurrentEvent: (state) => {
      state.currentEvent = null;
      state.currentEventError = null;
    },

    // Reset pagination
    resetPagination: (state, action) => {
      const { dataType } = action.payload;
      if (state[dataType]) {
        state[dataType].pagination = {
          skip: 0,
          limit: 20,
          hasMore: true,
        };
        state[dataType].data = [];
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Events
      .addCase(fetchInstituteEvents.pending, (state) => {
        state.eventsLoading = true;
        state.eventsError = null;
      })
      .addCase(fetchInstituteEvents.fulfilled, (state, action) => {
        state.eventsLoading = false;

        // Handle both array response and object response
        let eventsData, total, skip, limit;

        if (Array.isArray(action.payload)) {
          // Direct array response from API
          eventsData = action.payload;
          total = action.payload.length;
          skip = 0; // Assume first page for direct array
          limit = 20; // Default limit
        } else {
          // Object response with pagination info
          const { data, total: responseTotal, skip: responseSkip, limit: responseLimit } = action.payload;
          eventsData = data || [];
          total = responseTotal || 0;
          skip = responseSkip || 0;
          limit = responseLimit || 20;
        }

        if (skip === 0) {
          state.events.data = eventsData;
        } else {
          state.events.data = [...state.events.data, ...eventsData];
        }

        state.events.total = total;
        state.events.pagination = {
          skip: skip + eventsData.length,
          limit,
          hasMore: eventsData.length === limit,
        };
      })
      .addCase(fetchInstituteEvents.rejected, (state, action) => {
        state.eventsLoading = false;
        state.eventsError = action.payload;
      })

      // Create Event
      .addCase(createInstituteEvent.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
        state.createSuccess = false;
      })
      .addCase(createInstituteEvent.fulfilled, (state, action) => {
        state.createLoading = false;
        state.createSuccess = true;
        // Add new event to the beginning of the list
        state.events.data.unshift(action.payload);
        state.events.total += 1;
      })
      .addCase(createInstituteEvent.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Fetch Event By ID
      .addCase(fetchInstituteEventById.pending, (state) => {
        state.currentEventLoading = true;
        state.currentEventError = null;
      })
      .addCase(fetchInstituteEventById.fulfilled, (state, action) => {
        state.currentEventLoading = false;
        state.currentEvent = action.payload;
      })
      .addCase(fetchInstituteEventById.rejected, (state, action) => {
        state.currentEventLoading = false;
        state.currentEventError = action.payload;
      })

      // Update Event
      .addCase(updateInstituteEvent.pending, (state) => {
        state.updateLoading = true;
        state.updateError = null;
        state.updateSuccess = false;
      })
      .addCase(updateInstituteEvent.fulfilled, (state, action) => {
        state.updateLoading = false;
        state.updateSuccess = true;
        state.currentEvent = action.payload;
        // Update event in the list
        const index = state.events.data.findIndex(event => event.id === action.payload.id);
        if (index !== -1) {
          state.events.data[index] = action.payload;
        }
      })
      .addCase(updateInstituteEvent.rejected, (state, action) => {
        state.updateLoading = false;
        state.updateError = action.payload;
      })

      // Delete Event
      .addCase(deleteInstituteEvent.pending, (state) => {
        state.deleteLoading = true;
        state.deleteError = null;
      })
      .addCase(deleteInstituteEvent.fulfilled, (state, action) => {
        state.deleteLoading = false;
        // Remove event from the list
        state.events.data = state.events.data.filter(event => event.id !== action.payload);
        state.events.total -= 1;
      })
      .addCase(deleteInstituteEvent.rejected, (state, action) => {
        state.deleteLoading = false;
        state.deleteError = action.payload;
      })

      // Publish Event
      .addCase(publishInstituteEvent.pending, (state) => {
        state.publishLoading = true;
        state.publishError = null;
      })
      .addCase(publishInstituteEvent.fulfilled, (state, action) => {
        state.publishLoading = false;
        state.currentEvent = action.payload;
        // Update event status in the list
        const index = state.events.data.findIndex(event => event.id === action.payload.id);
        if (index !== -1) {
          state.events.data[index] = action.payload;
        }
      })
      .addCase(publishInstituteEvent.rejected, (state, action) => {
        state.publishLoading = false;
        state.publishError = action.payload;
      })

      // Cancel Event
      .addCase(cancelInstituteEvent.pending, (state) => {
        state.cancelLoading = true;
        state.cancelError = null;
      })
      .addCase(cancelInstituteEvent.fulfilled, (state, action) => {
        state.cancelLoading = false;
        state.currentEvent = action.payload;
        // Update event status in the list
        const index = state.events.data.findIndex(event => event.id === action.payload.id);
        if (index !== -1) {
          state.events.data[index] = action.payload;
        }
      })
      .addCase(cancelInstituteEvent.rejected, (state, action) => {
        state.cancelLoading = false;
        state.cancelError = action.payload;
      })

      // Fetch Event Attendees
      .addCase(fetchEventAttendees.pending, (state) => {
        state.attendeesLoading = true;
        state.attendeesError = null;
      })
      .addCase(fetchEventAttendees.fulfilled, (state, action) => {
        state.attendeesLoading = false;
        const { data, total, skip, limit } = action.payload;

        if (skip === 0) {
          state.attendees.data = data || [];
        } else {
          state.attendees.data = [...state.attendees.data, ...(data || [])];
        }

        state.attendees.total = total || 0;
        state.attendees.pagination = {
          skip: skip + (data?.length || 0),
          limit,
          hasMore: (data?.length || 0) === limit,
        };
      })
      .addCase(fetchEventAttendees.rejected, (state, action) => {
        state.attendeesLoading = false;
        state.attendeesError = action.payload;
      })

      // Register for Event
      .addCase(registerForEvent.pending, (state) => {
        state.registrationLoading = true;
        state.registrationError = null;
        state.registrationSuccess = false;
      })
      .addCase(registerForEvent.fulfilled, (state, action) => {
        state.registrationLoading = false;
        state.registrationSuccess = true;
        // Add new attendee to the list
        state.attendees.data.unshift(action.payload);
        state.attendees.total += 1;
      })
      .addCase(registerForEvent.rejected, (state, action) => {
        state.registrationLoading = false;
        state.registrationError = action.payload;
      })

      // Send Event Reminder
      .addCase(sendEventReminder.pending, (state) => {
        state.reminderLoading = true;
        state.reminderError = null;
        state.reminderSuccess = false;
      })
      .addCase(sendEventReminder.fulfilled, (state, action) => {
        state.reminderLoading = false;
        state.reminderSuccess = true;
      })
      .addCase(sendEventReminder.rejected, (state, action) => {
        state.reminderLoading = false;
        state.reminderError = action.payload;
      })

      // Fetch Event Analytics
      .addCase(fetchEventAnalytics.pending, (state) => {
        state.analyticsLoading = true;
        state.analyticsError = null;
      })
      .addCase(fetchEventAnalytics.fulfilled, (state, action) => {
        state.analyticsLoading = false;
        state.analytics = action.payload;
      })
      .addCase(fetchEventAnalytics.rejected, (state, action) => {
        state.analyticsLoading = false;
        state.analyticsError = action.payload;
      })

      // Fetch Event Categories
      .addCase(fetchEventCategories.pending, (state) => {
        state.categoriesLoading = true;
        state.categoriesError = null;
      })
      .addCase(fetchEventCategories.fulfilled, (state, action) => {
        state.categoriesLoading = false;
        state.categories = action.payload?.categories || action.payload || [];
      })
      .addCase(fetchEventCategories.rejected, (state, action) => {
        state.categoriesLoading = false;
        state.categoriesError = action.payload;
      });
  },
});

// Export actions
export const { clearErrors, clearSuccessStates, resetCurrentEvent, resetPagination } = instituteEventsSlice.actions;

// Export selectors
export const selectEvents = (state) => state.instituteEvents.events;
export const selectEventsLoading = (state) => state.instituteEvents.eventsLoading;
export const selectEventsError = (state) => state.instituteEvents.eventsError;

export const selectCurrentEvent = (state) => state.instituteEvents.currentEvent;
export const selectCurrentEventLoading = (state) => state.instituteEvents.currentEventLoading;
export const selectCurrentEventError = (state) => state.instituteEvents.currentEventError;

export const selectCreateLoading = (state) => state.instituteEvents.createLoading;
export const selectCreateError = (state) => state.instituteEvents.createError;
export const selectCreateSuccess = (state) => state.instituteEvents.createSuccess;

export const selectUpdateLoading = (state) => state.instituteEvents.updateLoading;
export const selectUpdateError = (state) => state.instituteEvents.updateError;
export const selectUpdateSuccess = (state) => state.instituteEvents.updateSuccess;

export const selectDeleteLoading = (state) => state.instituteEvents.deleteLoading;
export const selectDeleteError = (state) => state.instituteEvents.deleteError;

export const selectPublishLoading = (state) => state.instituteEvents.publishLoading;
export const selectPublishError = (state) => state.instituteEvents.publishError;

export const selectCancelLoading = (state) => state.instituteEvents.cancelLoading;
export const selectCancelError = (state) => state.instituteEvents.cancelError;

export const selectAttendees = (state) => state.instituteEvents.attendees;
export const selectAttendeesLoading = (state) => state.instituteEvents.attendeesLoading;
export const selectAttendeesError = (state) => state.instituteEvents.attendeesError;

export const selectEventAnalytics = (state) => state.instituteEvents.analytics;
export const selectEventAnalyticsLoading = (state) => state.instituteEvents.analyticsLoading;
export const selectEventAnalyticsError = (state) => state.instituteEvents.analyticsError;

export const selectEventCategories = (state) => state.instituteEvents.categories;
export const selectEventCategoriesLoading = (state) => state.instituteEvents.categoriesLoading;
export const selectEventCategoriesError = (state) => state.instituteEvents.categoriesError;

export const selectRegistrationLoading = (state) => state.instituteEvents.registrationLoading;
export const selectRegistrationError = (state) => state.instituteEvents.registrationError;
export const selectRegistrationSuccess = (state) => state.instituteEvents.registrationSuccess;

export const selectReminderLoading = (state) => state.instituteEvents.reminderLoading;
export const selectReminderError = (state) => state.instituteEvents.reminderError;
export const selectReminderSuccess = (state) => state.instituteEvents.reminderSuccess;

export default instituteEventsSlice.reducer;
