import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import API_BASE_URL from "../../utils/api/API_URL";

// Using existing API structure
const INSTITUTE_API = `${API_BASE_URL}/api/institute/dashboard`;
const getAuthToken = () => localStorage.getItem("token") || sessionStorage.getItem("token");

// Thunks for institute dashboard APIs

// Fetch complete dashboard data
export const fetchInstituteDashboard = createAsyncThunk(
  "instituteDashboard/fetchDashboard",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${INSTITUTE_API}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch dashboard summary/overview
export const fetchInstituteDashboardSummary = createAsyncThunk(
  "instituteDashboard/fetchSummary",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${INSTITUTE_API}/summary`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch institute analytics
export const fetchInstituteAnalytics = createAsyncThunk(
  "instituteDashboard/fetchAnalytics",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${INSTITUTE_API}/analytics`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      console.error('Analytics API Error:', err.response?.data || err.message);

      // Return empty analytics data if there's a backend error
      if (err.response?.status === 200 && err.response?.data) {
        // If response is 200 but has errors, return partial data
        return {
          mentorEffectiveness: [],
          studentPerformance: [],
          competitionStats: [],
          monthlyGrowth: [],
          ...err.response.data
        };
      }

      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch institute events - Updated to use correct my-events endpoint
export const fetchInstituteEvents = createAsyncThunk(
  "instituteDashboard/fetchEvents",
  async ({ skip = 0, limit = 20, status = "" } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      if (status) {
        params.append('status', status);
      }

      // Use the correct my-events endpoint for institutes
      const res = await axios.get(`${API_BASE_URL}/api/events/my-events?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch institute mentors and applications
export const fetchInstituteMentors = createAsyncThunk(
  "instituteDashboard/fetchMentors",
  async ({ skip = 0, limit = 20, status = "" } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });
      
      if (status) {
        params.append('status', status);
      }

      const res = await axios.get(`${API_BASE_URL}/api/institute/mentors?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch recent activities
export const fetchInstituteRecentActivities = createAsyncThunk(
  "instituteDashboard/fetchRecentActivities",
  async ({ limit = 10 } = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${INSTITUTE_API}/recent-activities?limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch dashboard notifications
export const fetchInstituteNotifications = createAsyncThunk(
  "instituteDashboard/fetchNotifications",
  async ({ limit = 10 } = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${INSTITUTE_API}/notifications?limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch quick stats
export const fetchInstituteQuickStats = createAsyncThunk(
  "instituteDashboard/fetchQuickStats",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${INSTITUTE_API}/quick-stats`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial state
const initialState = {
  // Dashboard overview
  dashboardData: null,
  dashboardLoading: false,
  dashboardError: null,

  // Summary data
  summary: {
    totalMentors: 0,
    activeMentors: 0,
    pendingMentorApplications: 0,
    totalEvents: 0,
    upcomingEvents: 0,
    completedEvents: 0,
    totalEventAttendees: 0,
    monthlyGrowth: {
      mentors: 0,
      events: 0,
      eventAttendees: 0,
    },
  },
  summaryLoading: false,
  summaryError: null,

  // Analytics data
  analytics: {
    mentorPerformance: [],
    eventStats: [],
    mentorEffectiveness: [],
    monthlyGrowth: [],
  },
  analyticsLoading: false,
  analyticsError: null,

  // Events data
  events: {
    data: [],
    total: 0,
    pagination: {
      skip: 0,
      limit: 20,
      hasMore: true,
    },
  },
  eventsLoading: false,
  eventsError: null,

  // Mentors data
  mentors: {
    data: [],
    applications: [],
    total: 0,
    pagination: {
      skip: 0,
      limit: 20,
      hasMore: true,
    },
  },
  mentorsLoading: false,
  mentorsError: null,

  // Recent activities
  recentActivities: [],
  recentActivitiesLoading: false,
  recentActivitiesError: null,

  // Notifications
  notifications: [],
  notificationsLoading: false,
  notificationsError: null,

  // Quick stats
  quickStats: {},
  quickStatsLoading: false,
  quickStatsError: null,
};

// Institute Dashboard Slice
const instituteDashboardSlice = createSlice({
  name: "instituteDashboard",
  initialState,
  reducers: {
    // Clear all errors
    clearErrors: (state) => {
      state.dashboardError = null;
      state.summaryError = null;
      state.analyticsError = null;
      state.eventsError = null;
      state.mentorsError = null;
      state.recentActivitiesError = null;
      state.notificationsError = null;
      state.quickStatsError = null;
    },

    // Clear dashboard data
    clearDashboardData: (state) => {
      state.dashboardData = null;
      state.summary = initialState.summary;
      state.analytics = initialState.analytics;
      state.events = initialState.events;
      state.mentors = initialState.mentors;
      state.recentActivities = [];
      state.notifications = [];
      state.quickStats = {};
    },

    // Reset pagination for a specific data type
    resetPagination: (state, action) => {
      const { dataType } = action.payload;
      if (state[dataType]) {
        state[dataType].pagination = {
          skip: 0,
          limit: 20,
          hasMore: true,
        };
        state[dataType].data = [];
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Institute Dashboard
      .addCase(fetchInstituteDashboard.pending, (state) => {
        state.dashboardLoading = true;
        state.dashboardError = null;
      })
      .addCase(fetchInstituteDashboard.fulfilled, (state, action) => {
        state.dashboardLoading = false;
        state.dashboardData = action.payload;
      })
      .addCase(fetchInstituteDashboard.rejected, (state, action) => {
        state.dashboardLoading = false;
        state.dashboardError = action.payload;
      })

      // Fetch Summary
      .addCase(fetchInstituteDashboardSummary.pending, (state) => {
        state.summaryLoading = true;
        state.summaryError = null;
      })
      .addCase(fetchInstituteDashboardSummary.fulfilled, (state, action) => {
        state.summaryLoading = false;
        state.summary = { ...state.summary, ...action.payload };
      })
      .addCase(fetchInstituteDashboardSummary.rejected, (state, action) => {
        state.summaryLoading = false;
        state.summaryError = action.payload;
      })

      // Fetch Analytics
      .addCase(fetchInstituteAnalytics.pending, (state) => {
        state.analyticsLoading = true;
        state.analyticsError = null;
      })
      .addCase(fetchInstituteAnalytics.fulfilled, (state, action) => {
        state.analyticsLoading = false;
        state.analytics = { ...state.analytics, ...action.payload };
      })
      .addCase(fetchInstituteAnalytics.rejected, (state, action) => {
        state.analyticsLoading = false;
        state.analyticsError = action.payload;
      })

      // Fetch Events
      .addCase(fetchInstituteEvents.pending, (state) => {
        state.eventsLoading = true;
        state.eventsError = null;
      })
      .addCase(fetchInstituteEvents.fulfilled, (state, action) => {
        state.eventsLoading = false;

        // Handle both array response and object response
        let eventsData, total, skip, limit;

        if (Array.isArray(action.payload)) {
          // Direct array response from API
          eventsData = action.payload;
          total = action.payload.length;
          skip = 0; // Assume first page for direct array
          limit = 20; // Default limit
        } else {
          // Object response with pagination info
          const { data, total: responseTotal, skip: responseSkip, limit: responseLimit } = action.payload;
          eventsData = data || [];
          total = responseTotal || 0;
          skip = responseSkip || 0;
          limit = responseLimit || 20;
        }

        if (skip === 0) {
          // First page - replace data
          state.events.data = eventsData;
        } else {
          // Subsequent pages - append data
          state.events.data = [...state.events.data, ...eventsData];
        }

        state.events.total = total;
        state.events.pagination = {
          skip: skip + eventsData.length,
          limit,
          hasMore: eventsData.length === limit,
        };
      })
      .addCase(fetchInstituteEvents.rejected, (state, action) => {
        state.eventsLoading = false;
        state.eventsError = action.payload;
      })

      // Fetch Mentors
      .addCase(fetchInstituteMentors.pending, (state) => {
        state.mentorsLoading = true;
        state.mentorsError = null;
      })
      .addCase(fetchInstituteMentors.fulfilled, (state, action) => {
        state.mentorsLoading = false;
        const { data, applications, total, skip, limit } = action.payload;

        if (skip === 0) {
          // First page - replace data
          state.mentors.data = data || [];
          state.mentors.applications = applications || [];
        } else {
          // Subsequent pages - append data
          state.mentors.data = [...state.mentors.data, ...(data || [])];
        }

        state.mentors.total = total || 0;
        state.mentors.pagination = {
          skip: skip + (data?.length || 0),
          limit,
          hasMore: (data?.length || 0) === limit,
        };
      })
      .addCase(fetchInstituteMentors.rejected, (state, action) => {
        state.mentorsLoading = false;
        state.mentorsError = action.payload;
      })

      // Fetch Recent Activities
      .addCase(fetchInstituteRecentActivities.pending, (state) => {
        state.recentActivitiesLoading = true;
        state.recentActivitiesError = null;
      })
      .addCase(fetchInstituteRecentActivities.fulfilled, (state, action) => {
        state.recentActivitiesLoading = false;
        state.recentActivities = action.payload?.activities || action.payload || [];
      })
      .addCase(fetchInstituteRecentActivities.rejected, (state, action) => {
        state.recentActivitiesLoading = false;
        state.recentActivitiesError = action.payload;
      })

      // Fetch Notifications
      .addCase(fetchInstituteNotifications.pending, (state) => {
        state.notificationsLoading = true;
        state.notificationsError = null;
      })
      .addCase(fetchInstituteNotifications.fulfilled, (state, action) => {
        state.notificationsLoading = false;
        state.notifications = action.payload?.notifications || action.payload || [];
      })
      .addCase(fetchInstituteNotifications.rejected, (state, action) => {
        state.notificationsLoading = false;
        state.notificationsError = action.payload;
      })

      // Fetch Quick Stats
      .addCase(fetchInstituteQuickStats.pending, (state) => {
        state.quickStatsLoading = true;
        state.quickStatsError = null;
      })
      .addCase(fetchInstituteQuickStats.fulfilled, (state, action) => {
        state.quickStatsLoading = false;
        state.quickStats = action.payload || {};
      })
      .addCase(fetchInstituteQuickStats.rejected, (state, action) => {
        state.quickStatsLoading = false;
        state.quickStatsError = action.payload;
      });
  },
});

// Export actions
export const { clearErrors, clearDashboardData, resetPagination } = instituteDashboardSlice.actions;

// Export selectors
export const selectDashboardData = (state) => state.instituteDashboard.dashboardData;
export const selectDashboardLoading = (state) => state.instituteDashboard.dashboardLoading;
export const selectDashboardError = (state) => state.instituteDashboard.dashboardError;

export const selectSummary = (state) => state.instituteDashboard.summary;
export const selectSummaryLoading = (state) => state.instituteDashboard.summaryLoading;
export const selectSummaryError = (state) => state.instituteDashboard.summaryError;

export const selectAnalytics = (state) => state.instituteDashboard.analytics;
export const selectAnalyticsLoading = (state) => state.instituteDashboard.analyticsLoading;
export const selectAnalyticsError = (state) => state.instituteDashboard.analyticsError;

export const selectEvents = (state) => state.instituteDashboard.events;
export const selectEventsLoading = (state) => state.instituteDashboard.eventsLoading;
export const selectEventsError = (state) => state.instituteDashboard.eventsError;

export const selectMentors = (state) => state.instituteDashboard.mentors;
export const selectMentorsLoading = (state) => state.instituteDashboard.mentorsLoading;
export const selectMentorsError = (state) => state.instituteDashboard.mentorsError;

export const selectRecentActivities = (state) => state.instituteDashboard.recentActivities;
export const selectRecentActivitiesLoading = (state) => state.instituteDashboard.recentActivitiesLoading;
export const selectRecentActivitiesError = (state) => state.instituteDashboard.recentActivitiesError;

export const selectNotifications = (state) => state.instituteDashboard.notifications;
export const selectNotificationsLoading = (state) => state.instituteDashboard.notificationsLoading;
export const selectNotificationsError = (state) => state.instituteDashboard.notificationsError;

export const selectQuickStats = (state) => state.instituteDashboard.quickStats;
export const selectQuickStatsLoading = (state) => state.instituteDashboard.quickStatsLoading;
export const selectQuickStatsError = (state) => state.instituteDashboard.quickStatsError;

export default instituteDashboardSlice.reducer;
