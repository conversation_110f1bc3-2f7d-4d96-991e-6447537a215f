import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ye, <PERSON><PERSON><PERSON><PERSON>, FiX } from 'react-icons/fi';
import { ListCard } from '../ui/cards';

/**
 * InstituteList - Displays a list of institutes with actions
 * Replaces complex table structure with simpler card-based list
 */
const InstituteList = ({
  institutes = [],
  onView,
  onApprove,
  onReject,
  approveLoading = false,
  rejectLoading = false,
  emptyMessage = "No institutes found"
}) => {
  // Debug: Log the institutes data
  console.log('InstituteList received institutes:', institutes);
  console.log('InstituteList institutes length:', institutes.length);
  if (institutes.length > 0) {
    console.log('First institute structure:', institutes[0]);
  }
  const renderInstituteItem = (institute) => {
    const actions = [
      {
        icon: FiEye,
        onClick: () => onView(institute),
        tooltip: "View Details",
        variant: "default"
      },
      {
        icon: FiCheck,
        onClick: () => {
          console.log('InstituteList - Approve clicked for:', institute);
          onApprove(institute);
        },
        tooltip: "Approve",
        variant: "success",
        disabled: approveLoading
      },
      {
        icon: FiX,
        onClick: () => {
          console.log('InstituteList - Reject clicked for:', institute);
          onReject(institute);
        },
        tooltip: "Reject",
        variant: "danger",
        disabled: rejectLoading
      }
    ];

    return (
      <div className="flex items-center justify-between p-6 bg-white border border-gray-200 rounded-xl hover:shadow-lg transition-all duration-200 w-full">
        <div className="flex items-center space-x-6 min-w-0 flex-1">

          {/* Institute Info */}
          <div className="min-w-0 flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <FiHome className="h-6 w-6 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <h4 className="text-lg font-semibold text-gray-900 truncate">
                  {institute.user?.institute_profile?.institute_name ||
                   institute.institute_profile?.institute_name ||
                   institute.profile?.institute_name ||
                   institute.institute_name ||
                   'Unnamed Institute'}
                </h4>
                <p className="text-sm text-gray-500 truncate">
                  {institute.user?.institute_profile?.institute_type ||
                   institute.institute_profile?.institute_type ||
                   institute.profile?.institute_type ||
                   institute.institute_type ||
                   'Unknown Type'}
                </p>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              <span className="inline-flex items-center">
                📍 {institute.user?.institute_profile?.city ||
                     institute.institute_profile?.city ||
                     institute.profile?.city ||
                     institute.city || 'Unknown City'}, {institute.user?.institute_profile?.state ||
                                                        institute.institute_profile?.state ||
                                                        institute.profile?.state ||
                                                        institute.state || 'Unknown State'}
              </span>
            </div>
          </div>

        </div>

        {/* Status and Actions */}
        <div className="flex flex-col items-end space-y-3">
          {/* Status Badge */}
          <div className="flex-shrink-0">
            <span className={`
              inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
              ${(institute.user?.institute_profile?.verification_status ||
                 institute.institute_profile?.verification_status ||
                 institute.profile?.verification_status ||
                 institute.verification_status) === 'approved'
                ? 'bg-green-100 text-green-800'
                : (institute.user?.institute_profile?.verification_status ||
                   institute.institute_profile?.verification_status ||
                   institute.profile?.verification_status ||
                   institute.verification_status) === 'rejected'
                ? 'bg-red-100 text-red-800'
                : 'bg-yellow-100 text-yellow-800'
              }
            `}>
              {institute.user?.institute_profile?.verification_status ||
               institute.institute_profile?.verification_status ||
               institute.profile?.verification_status ||
               institute.verification_status ||
               'pending'}
            </span>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            {actions.map((action, index) => {
              const ActionIcon = action.icon;
              return (
                <button
                  key={index}
                  onClick={action.onClick}
                  disabled={action.disabled}
                  className={`
                    p-3 rounded-lg transition-all duration-200 font-medium
                    ${action.variant === 'success'
                      ? 'bg-green-50 text-green-700 hover:bg-green-100 border border-green-200'
                      : action.variant === 'danger'
                      ? 'bg-red-50 text-red-700 hover:bg-red-100 border border-red-200'
                      : 'bg-blue-50 text-blue-700 hover:bg-blue-100 border border-blue-200'
                    }
                    ${action.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-sm'}
                  `}
                  title={action.tooltip}
                >
                  <ActionIcon className="h-5 w-5" />
                </button>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full">
      <ListCard
        items={institutes}
        renderItem={renderInstituteItem}
        emptyMessage={emptyMessage}
        className="w-full"
        contentClassName="space-y-4"
      />
    </div>
  );
};

export default InstituteList;
