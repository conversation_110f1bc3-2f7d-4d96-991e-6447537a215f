/**
 * InstituteDocumentViewer Component
 * Simple document viewer with download functionality only
 */

import React from 'react';
import DocumentCard from '../ui/DocumentCard';
import { useDocuments } from '../../hooks/useDocuments';

const InstituteDocumentViewer = ({ 
  document, 
  showActions = true, 
  className = '',
  onRemove,
  showRemove = false,
  disabled = false
}) => {
  const { downloadDocumentFile } = useDocuments();

  const handleDownload = async () => {
    if (disabled) return;
    
    try {
      const documentPath = document?.document_url || document?.path;
      const documentName = document?.document_name || document?.name;
      
      if (documentPath) {
        await downloadDocumentFile(documentPath, documentName);
      }
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  if (!document) {
    return null;
  }

  return (
    <DocumentCard
      document={document}
      onDownload={handleDownload}
      onRemove={onRemove}
      showRemove={showRemove}
      disabled={disabled}
      className={className}
    />
  );
};

export default InstituteDocumentViewer;
