import React, { useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { createExamWithAssignment, updateExam } from '../../store/slices/ExamSlice';
import { PageContainer, Stack, Card } from '../ui/layout';
import StepIndicator from './StepIndicator';
import ExamDetailsForm from './ExamDetailsForm';
import QuestionForm from './QuestionForm';
import QuestionList from './QuestionList';
import StudentAssignmentSelector from './StudentAssignmentSelector';
import { FiSave, FiArrowRight, FiArrowLeft, FiCheck } from 'react-icons/fi';

const STEPS = [
  { id: 1, title: 'Exam Details', description: 'Basic exam information' },
  { id: 2, title: 'Questions', description: 'Add exam questions' },
  { id: 3, title: 'Assignment', description: 'Assign to students' },
  { id: 4, title: 'Review', description: 'Review and publish' }
];

const ExamCreationWizard = ({ examId, isEditing = false }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [examData, setExamData] = useState({
    title: '',
    description: '',
    total_marks: 0,
    total_duration: 0,
    start_time: '',
    question_ids: []
  });
  const [questions, setQuestions] = useState([]);
  const [selectedStudents, setSelectedStudents] = useState([]);

  const handleExamDataChange = useCallback((data) => {
    setExamData(prev => ({ ...prev, ...data }));
  }, []);

  const handleQuestionsChange = useCallback((newQuestions) => {
    setQuestions(newQuestions);
    const totalMarks = newQuestions.reduce((sum, q) => sum + (q.marks || 0), 0);
    setExamData(prev => ({ ...prev, total_marks: totalMarks }));
  }, []);

  const handleNext = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSave = async () => {
    try {
      const examPayload = {
        ...examData,
        question_ids: questions.map(q => q.id),
        assigned_students: selectedStudents
      };

      if (isEditing) {
        await dispatch(updateExam({ examId, examData: examPayload })).unwrap();
      } else {
        await dispatch(createExamWithAssignment(examPayload)).unwrap();
      }
      
      navigate('/teacher/exams');
    } catch (error) {
      console.error('Failed to save exam:', error);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <ExamDetailsForm
            examData={examData}
            onChange={handleExamDataChange}
          />
        );
      case 2:
        return (
          <Stack gap="lg">
            <QuestionForm
              onQuestionAdd={(question) => {
                setQuestions(prev => [...prev, { ...question, id: Date.now() }]);
              }}
            />
            <QuestionList
              questions={questions}
              onQuestionsChange={handleQuestionsChange}
            />
          </Stack>
        );
      case 3:
        return (
          <StudentAssignmentSelector
            selectedStudents={selectedStudents}
            onSelectionChange={setSelectedStudents}
          />
        );
      case 4:
        return (
          <Card>
            <h3 className="text-lg font-semibold mb-4">Review Exam</h3>
            <Stack gap="md">
              <div>
                <strong>Title:</strong> {examData.title}
              </div>
              <div>
                <strong>Duration:</strong> {examData.total_duration} minutes
              </div>
              <div>
                <strong>Total Marks:</strong> {examData.total_marks}
              </div>
              <div>
                <strong>Questions:</strong> {questions.length}
              </div>
              <div>
                <strong>Students:</strong> {selectedStudents.length}
              </div>
            </Stack>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <PageContainer>
      <Stack gap="lg">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {isEditing ? 'Edit Exam' : 'Create New Exam'}
          </h1>
          <p className="text-gray-600 mt-1">
            Follow the steps to create your exam
          </p>
        </div>

        <StepIndicator steps={STEPS} currentStep={currentStep} />

        <div className="min-h-96">
          {renderStepContent()}
        </div>

        <div className="flex justify-between">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 disabled:opacity-50"
          >
            <FiArrowLeft className="mr-2" />
            Previous
          </button>

          <div className="flex space-x-3">
            <button
              onClick={handleSave}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              <FiSave className="mr-2" />
              Save Draft
            </button>

            {currentStep < STEPS.length ? (
              <button
                onClick={handleNext}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Next
                <FiArrowRight className="ml-2" />
              </button>
            ) : (
              <button
                onClick={handleSave}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                <FiCheck className="mr-2" />
                Publish Exam
              </button>
            )}
          </div>
        </div>
      </Stack>
    </PageContainer>
  );
};

export default ExamCreationWizard;
