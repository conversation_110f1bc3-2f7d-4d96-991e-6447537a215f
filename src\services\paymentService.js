/**
 * PayFast Payment Service
 * 
 * This service provides functions for PayFast payment operations
 * including event payments, subscription payments, and payment status tracking.
 */

import axios from 'axios';
import API_BASE_URL from '../utils/api/API_URL';
import { handleApiError } from '../utils/helpers/errorHandler';
import logger from '../utils/helpers/logger';

// Base URL for payment endpoints
const PAYMENT_API_BASE = `${API_BASE_URL}/api/payments/payfast`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Helper function to create auth headers
const getAuthHeaders = () => ({
  'Authorization': `Bearer ${getAuthToken()}`,
  'Content-Type': 'application/json'
});

/**
 * PayFast Payment Service Class
 */
class PaymentService {
  
  /**
   * Create a payment for event registration
   * @param {Object} paymentData - Payment data
   * @param {string} paymentData.registration_id - Registration UUID
   * @param {number} paymentData.amount - Payment amount
   * @param {string} paymentData.currency - Currency code (default: ZAR)
   * @param {string} paymentData.user_email - User email
   * @param {string} paymentData.user_name - User full name
   * @param {string} paymentData.return_url - Success return URL
   * @param {string} paymentData.cancel_url - Cancel return URL
   * @returns {Promise<Object>} Payment response with payment_id and form data
   */
  async createEventPayment(paymentData) {
    try {
      logger.info('Creating event payment', { paymentData });
      
      const response = await axios.post(
        `${PAYMENT_API_BASE}/events/payment`,
        paymentData,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Event payment created successfully', { paymentId: response.data.payment_id });
      return response.data;
    } catch (error) {
      logger.error('Failed to create event payment', { error: error.message, paymentData });
      throw handleApiError(error);
    }
  }

  /**
   * Create a payment for subscription
   * @param {Object} paymentData - Payment data
   * @param {string} paymentData.subscription_id - Subscription UUID
   * @param {number} paymentData.amount - Payment amount
   * @param {string} paymentData.currency - Currency code (default: ZAR)
   * @param {string} paymentData.user_email - User email
   * @param {string} paymentData.user_name - User full name
   * @param {string} paymentData.return_url - Success return URL
   * @param {string} paymentData.cancel_url - Cancel return URL
   * @returns {Promise<Object>} Payment response with payment data
   */
  async createSubscriptionPayment(paymentData) {
    try {
      logger.info('Creating subscription payment', { paymentData });
      
      const response = await axios.post(
        `${PAYMENT_API_BASE}/subscriptions/payment`,
        paymentData,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Subscription payment created successfully', { paymentId: response.data.payfast_payment_id });
      return response.data;
    } catch (error) {
      logger.error('Failed to create subscription payment', { error: error.message, paymentData });
      throw handleApiError(error);
    }
  }

  /**
   * Get payment form data for frontend integration
   * @param {string} paymentId - Payment UUID
   * @returns {Promise<Object>} Payment form data for PayFast submission
   */
  async getPaymentFormData(paymentId) {
    try {
      logger.info('Getting payment form data', { paymentId });
      
      const response = await axios.get(
        `${PAYMENT_API_BASE}/events/payment/${paymentId}/form`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Payment form data retrieved successfully', { paymentId });
      return response.data;
    } catch (error) {
      logger.error('Failed to get payment form data', { error: error.message, paymentId });
      throw handleApiError(error);
    }
  }

  /**
   * Get payment status
   * @param {string} paymentId - Payment UUID
   * @returns {Promise<Object>} Payment status information
   */
  async getPaymentStatus(paymentId) {
    try {
      logger.info('Getting payment status', { paymentId });
      
      const response = await axios.get(
        `${PAYMENT_API_BASE}/payment/${paymentId}/status`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Payment status retrieved successfully', { 
        paymentId, 
        status: response.data.status 
      });
      return response.data;
    } catch (error) {
      logger.error('Failed to get payment status', { error: error.message, paymentId });
      throw handleApiError(error);
    }
  }

  /**
   * Process payment refund
   * @param {string} paymentId - Payment UUID
   * @param {Object} refundData - Refund data
   * @param {number} refundData.refund_amount - Amount to refund
   * @param {string} refundData.reason - Refund reason
   * @returns {Promise<Object>} Refund response
   */
  async processRefund(paymentId, refundData) {
    try {
      logger.info('Processing payment refund', { paymentId, refundData });
      
      const response = await axios.post(
        `${PAYMENT_API_BASE}/payment/${paymentId}/refund`,
        refundData,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Payment refund processed successfully', { paymentId });
      return response.data;
    } catch (error) {
      logger.error('Failed to process payment refund', { error: error.message, paymentId, refundData });
      throw handleApiError(error);
    }
  }

  /**
   * Get PayFast configuration
   * @returns {Promise<Object>} PayFast configuration data
   */
  async getPayFastConfig() {
    try {
      logger.info('Getting PayFast configuration');
      
      const response = await axios.get(`${PAYMENT_API_BASE}/config`);
      
      logger.info('PayFast configuration retrieved successfully');
      return response.data;
    } catch (error) {
      logger.error('Failed to get PayFast configuration', { error: error.message });
      throw handleApiError(error);
    }
  }

  /**
   * Submit payment form to PayFast
   * @param {Object} formData - PayFast form data
   * @param {string} actionUrl - PayFast action URL
   */
  submitPaymentForm(formData, actionUrl) {
    try {
      logger.info('Submitting payment form to PayFast', { actionUrl });
      
      // Create form element
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = actionUrl;
      form.style.display = 'none';

      // Add form fields
      Object.entries(formData).forEach(([key, value]) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
      });

      // Submit form
      document.body.appendChild(form);
      form.submit();
      
      logger.info('Payment form submitted successfully');
    } catch (error) {
      logger.error('Failed to submit payment form', { error: error.message, formData });
      throw error;
    }
  }

  /**
   * Poll payment status until completion or timeout
   * @param {string} paymentId - Payment UUID
   * @param {number} maxAttempts - Maximum polling attempts (default: 30)
   * @param {number} interval - Polling interval in ms (default: 2000)
   * @returns {Promise<Object>} Final payment status
   */
  async pollPaymentStatus(paymentId, maxAttempts = 30, interval = 2000) {
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      try {
        const status = await this.getPaymentStatus(paymentId);
        
        // Check if payment is in final state
        if (['completed', 'failed', 'refunded'].includes(status.status)) {
          return status;
        }
        
        // Wait before next attempt
        await new Promise(resolve => setTimeout(resolve, interval));
        attempts++;
        
      } catch (error) {
        logger.error('Error polling payment status', { error: error.message, paymentId, attempts });
        attempts++;
        
        if (attempts >= maxAttempts) {
          throw error;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }
    
    throw new Error('Payment status polling timeout');
  }

  /**
   * Generate return URLs for payment
   * @param {string} baseUrl - Base application URL
   * @param {string} paymentId - Payment UUID
   * @returns {Object} Return URLs object
   */
  generateReturnUrls(baseUrl, paymentId) {
    return {
      return_url: `${baseUrl}/payment/success?payment_id=${paymentId}`,
      cancel_url: `${baseUrl}/payment/cancel?payment_id=${paymentId}`
    };
  }

  /**
   * Get payment history for current user
   * @param {Object} filters - Optional filters for payment history
   * @param {string} filters.status - Filter by payment status
   * @param {string} filters.dateRange - Filter by date range
   * @param {number} filters.page - Page number for pagination
   * @param {number} filters.per_page - Items per page
   * @returns {Promise<Array>} Array of payment records
   */
  async getPaymentHistory(filters = {}) {
    try {
      logger.info('Getting payment history', { filters });

      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await axios.get(
        `${PAYMENT_API_BASE}/history?${params}`,
        { headers: getAuthHeaders() }
      );

      logger.info('Payment history retrieved successfully', {
        count: response.data?.length || 0
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to get payment history', { error: error.message });
      throw handleApiError(error);
    }
  }

  /**
   * Get payment statistics for current user
   * @returns {Promise<Object>} Payment statistics object
   */
  async getPaymentStats() {
    try {
      logger.info('Getting payment statistics');

      const response = await axios.get(
        `${PAYMENT_API_BASE}/stats`,
        { headers: getAuthHeaders() }
      );

      logger.info('Payment statistics retrieved successfully');
      return response.data;
    } catch (error) {
      logger.error('Failed to get payment statistics', { error: error.message });
      throw handleApiError(error);
    }
  }
}

// Create and export service instance
const paymentService = new PaymentService();
export default paymentService;

// Export individual methods for convenience
export const {
  createEventPayment,
  createSubscriptionPayment,
  getPaymentFormData,
  getPaymentStatus,
  processRefund,
  getPayFastConfig,
  submitPaymentForm,
  pollPaymentStatus,
  generateReturnUrls,
  getPaymentHistory,
  getPaymentStats
} = paymentService;
