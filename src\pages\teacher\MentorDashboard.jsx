import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>ontainer, Stack } from '../../components/ui/layout';
import { DashboardGrid, QuickActions } from '../../components/dashboard';
import {
  FiUsers,
  FiBookOpen,
  FiTrendingUp,
  FiSettings,
  FiEye,
  FiPlus
} from 'react-icons/fi';

function MentorDashboard() {
  const navigate = useNavigate();

  const stats = useMemo(() => [
    {
      key: 'students',
      title: 'Students',
      value: '45',
      icon: FiUsers,
      color: 'blue'
    },
    {
      key: 'sessions',
      title: 'Sessions',
      value: '128',
      icon: FiBookOpen,
      color: 'green'
    },
    {
      key: 'progress',
      title: 'Progress',
      value: '+15%',
      icon: FiTrendingUp,
      color: 'purple'
    },
    {
      key: 'active',
      title: 'Active Programs',
      value: '6',
      icon: FiPlus,
      color: 'indigo'
    }
  ], []);

  const quickActions = useMemo(() => [
    {
      key: 'view-students',
      title: 'View Students',
      description: 'Manage your mentees',
      icon: FiEye,
      color: 'blue',
      onClick: () => navigate('/teacher/students')
    },
    {
      key: 'create-session',
      title: 'Create Session',
      description: 'Schedule a mentoring session',
      icon: FiPlus,
      color: 'green',
      onClick: () => navigate('/teacher/sessions/create')
    },
    {
      key: 'settings',
      title: 'Settings',
      description: 'Configure mentor preferences',
      icon: FiSettings,
      color: 'purple',
      onClick: () => navigate('/teacher/settings')
    }
  ], [navigate]);

  return (
    <PageContainer>
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
          Mentor Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Guide and support your students
        </p>
      </div>

      <Stack gap="lg">
        <DashboardGrid stats={stats} />
        <QuickActions actions={quickActions} />
      </Stack>
    </PageContainer>
  );
}

export default MentorDashboard;
