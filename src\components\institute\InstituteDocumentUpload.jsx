import React, { useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiUpload,
  FiFile,
  FiTrash2,
  FiDownload,
  <PERSON>Eye,
  FiAlertCircle,
  <PERSON>Check,
  FiX,
  FiLoader
} from 'react-icons/fi';
import { useThemeProvider } from '../../providers/ThemeContext';
import { getErrorMessage } from '../../utils/helpers/errorHandler';
import { validateInstituteDocument, formatFileSize } from '../../utils/fileValidation';
import { createDocumentObject, updateDocumentAtIndex, removeDocumentAtIndex } from '../../utils/documentHelpers';
import DocumentList from '../ui/DocumentList';
import DocumentCard from '../ui/DocumentCard';

/**
 * InstituteDocumentUpload Component
 * Handles document uploads for institute verification
 *
 * Props:
 * - documents: Array of existing documents
 * - onDocumentsChange: Callback when documents are added/removed
 * - maxFileSize: Maximum file size in MB (default: 20)
 * - disabled: Whether the component is disabled (default: false)
 * - className: Additional CSS classes
 */
const InstituteDocumentUpload = ({
  documents = [],
  onDocumentsChange,
  maxFileSize = 20,
  disabled = false,
  className = ''
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const fileInputRef = useRef(null);

  // Local state
  const [dragOver, setDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [error, setError] = useState(null);

  // Document types
  const documentTypes = [
    { value: 'accreditation', label: 'Accreditation Certificate' },
    { value: 'license', label: 'Operating License' },
    { value: 'certificate', label: 'Other Certificate' },
    { value: 'other', label: 'Other Document' }
  ];

  // Allowed file types
  const allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'];
  const allowedMimeTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/rtf',
    'application/vnd.oasis.opendocument.text'
  ];

  // Theme-based styling
  const {
    bgPrimary,
    bgSecondary,
    textPrimary,
    textSecondary,
    borderColor,
    cardBg
  } = currentTheme;

  // Validate file using the utility function
  const validateFile = (file) => {
    const validation = validateInstituteDocument(file);
    return validation.isValid ? null : validation.error;
  };

  // Handle file selection
  const handleFileSelect = (files) => {
    const fileArray = Array.from(files);
    const validFiles = [];
    const errors = [];

    fileArray.forEach(file => {
      const validationError = validateFile(file);
      if (validationError) {
        errors.push(`${file.name}: ${validationError}`);
      } else {
        validFiles.push(createDocumentObject(file));
      }
    });

    if (errors.length > 0) {
      setError(errors.join('\n'));
      return;
    }

    setError(null);
    const updatedDocuments = [...documents, ...validFiles];
    onDocumentsChange(updatedDocuments);
  };

  // Handle file input change
  const handleFileInputChange = (e) => {
    if (e.target.files.length > 0) {
      handleFileSelect(e.target.files);
      e.target.value = ''; // Reset input
    }
  };

  // Handle drag and drop
  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(false);

      if (e.dataTransfer.files.length > 0) {
        handleFileSelect(e.dataTransfer.files);
      }
    }
  };

  // Remove document
  const handleRemoveDocument = (index) => {
    onDocumentsChange(removeDocumentAtIndex(documents, index));
  };

  // Update document type
  const handleDocumentTypeChange = (index, type) => {
    onDocumentsChange(updateDocumentAtIndex(documents, index, { type }));
  };

  // Update document description
  const handleDocumentDescriptionChange = (index, description) => {
    onDocumentsChange(updateDocumentAtIndex(documents, index, { description }));
  };

  // Get file icon based on type
  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    return <FiFile className="w-5 h-5" />;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Area */}
      <div className={`border-2 border-dashed rounded-lg p-6 transition-all duration-200 ${
        disabled
          ? 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 cursor-not-allowed opacity-60'
          : dragOver
          ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
          : `border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-600 ${bgSecondary}`
      }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="text-center">
          <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-4 ${
            dragOver ? 'bg-blue-100 dark:bg-blue-900/40' : 'bg-gray-100 dark:bg-gray-700'
          }`}>
            <FiUpload className={`w-6 h-6 ${dragOver ? 'text-blue-600' : textSecondary}`} />
          </div>

          <h4 className={`text-sm font-medium ${textPrimary} mb-2`}>
            {dragOver ? 'Drop documents here' : 'Upload Verification Documents'}
          </h4>

          <p className={`text-sm ${textSecondary} mb-4`}>
            Drag and drop documents here, or{' '}
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled}
              className={`font-medium underline ${disabled ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:text-blue-700'}`}
            >
              browse
            </button>
          </p>

          <div className={`text-xs ${textSecondary} space-y-1`}>
            <p>Maximum file size: {maxFileSize}MB per file</p>
            <p>Supported formats: {allowedTypes.map(type => `.${type.toUpperCase()}`).join(', ')}</p>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={allowedMimeTypes.join(',')}
          onChange={handleFileInputChange}
          disabled={disabled}
          className="hidden"
        />
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-start gap-2 text-red-600 dark:text-red-400">
            <FiAlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
            <div className="text-sm whitespace-pre-line">{error}</div>
          </div>
        </div>
      )}

      {/* Documents List */}
      {documents.length > 0 && (
        <div className="space-y-4">
          <h4 className={`text-sm font-medium ${textPrimary}`}>
            Selected Documents ({documents.length})
          </h4>
          
          <div className="space-y-3">
            {documents.map((doc, index) => {
              // Check if this is an existing document (has document_url) or a new file upload
              const isExistingDocument = doc.document_url && !doc.file;

              if (isExistingDocument) {
                // Use the simplified document viewer for existing documents
                return (
                  <DocumentCard
                    key={doc.id || index}
                    document={doc}
                    onDownload={() => {
                      // Handle download for existing documents
                      const documentPath = doc.document_url;
                      const documentName = doc.document_name;
                      if (documentPath) {
                        // Use the download hook here
                        console.log('Download:', documentPath, documentName);
                      }
                    }}
                    onRemove={() => handleRemoveDocument(index)}
                    showRemove={!disabled}
                    disabled={disabled}
                  />
                );
              }

              // Render file upload interface for new documents
              return (
                <div key={doc.id || index} className={`p-4 border rounded-lg ${borderColor} ${cardBg}`}>
                  <div className="flex items-start gap-3">
                    {/* File Icon */}
                    <div className={`p-2 rounded-lg bg-gray-100 dark:bg-gray-700 ${textSecondary}`}>
                      {getFileIcon(doc.file?.name || doc.document_name || 'file')}
                    </div>

                    {/* File Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h5 className={`text-sm font-medium ${textPrimary} truncate`}>
                          {doc.file?.name || doc.document_name || 'Unknown File'}
                        </h5>
                        {doc.file && (
                          <span className={`text-xs ${textSecondary}`}>
                            {formatFileSize(doc.file.size)}
                          </span>
                        )}
                      </div>

                    {/* Document Type Selection */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                      <div>
                        <label className={`block text-xs font-medium ${textSecondary} mb-1`}>
                          Document Type *
                        </label>
                        <select
                          value={doc.type || 'other'}
                          onChange={(e) => handleDocumentTypeChange(index, e.target.value)}
                          className={`w-full px-3 py-2 text-sm border rounded-md ${borderColor} ${bgPrimary} ${textPrimary} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                        >
                          {documentTypes.map(type => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className={`block text-xs font-medium ${textSecondary} mb-1`}>
                          Description (Optional)
                        </label>
                        <input
                          type="text"
                          value={doc.description || ''}
                          onChange={(e) => handleDocumentDescriptionChange(index, e.target.value)}
                          placeholder="Brief description..."
                          className={`w-full px-3 py-2 text-sm border rounded-md ${borderColor} ${bgPrimary} ${textPrimary} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                        />
                      </div>
                    </div>

                    {/* Verification Status (for new file uploads) */}
                    {doc.verified !== undefined && (
                      <div className="flex items-center gap-2 mb-2">
                        {doc.verified ? (
                          <div className="flex items-center gap-1 text-green-600">
                            <FiCheck className="w-4 h-4" />
                            <span className="text-xs">Verified</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 text-yellow-600">
                            <FiAlertCircle className="w-4 h-4" />
                            <span className="text-xs">Pending Verification</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Actions for new file uploads */}
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleRemoveDocument(index)}
                      disabled={disabled}
                      className={`p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary} hover:text-red-600 transition-colors ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                      title="Remove Document"
                    >
                      <FiTrash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default InstituteDocumentUpload;
