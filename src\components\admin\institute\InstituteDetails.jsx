import React from 'react';
import { FiHome, FiCalendar } from 'react-icons/fi';
import { Card, GridWrapper } from '../../ui/layout';

const DetailField = ({ label, value }) => (
  <div>
    <label className="text-sm font-medium text-gray-500">{label}</label>
    <p className="text-gray-900">{value || 'Not provided'}</p>
  </div>
);

const InstituteDetails = ({ user, profile }) => {
  return (
    <Card className="bg-gray-50">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <FiHome className="h-5 w-5 mr-2 text-blue-600" />
        Institute Details
      </h3>
      
      <GridWrapper columns={2} gap="lg">
        <DetailField label="Established Year" value={profile.established_year} />
        <DetailField label="Accreditation" value={profile.accreditation} />
        <DetailField label="User Type" value={user.user_type} />
        <DetailField label="Registration Date" value={new Date(user.created_at).toLocaleDateString()} />
        <DetailField label="Last Updated" value={new Date(profile.updated_at).toLocaleDateString()} />
        <DetailField label="Status" value={profile.verification_status} />
      </GridWrapper>
      
      {profile.description && (
        <div className="mt-4">
          <label className="text-sm font-medium text-gray-500">Description</label>
          <p className="text-gray-900 mt-1">{profile.description}</p>
        </div>
      )}
    </Card>
  );
};

export default InstituteDetails;
