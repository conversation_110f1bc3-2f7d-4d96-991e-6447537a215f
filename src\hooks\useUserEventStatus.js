import { useSelector, useDispatch } from 'react-redux';
import { useCallback, useMemo } from 'react';
import {
  fetchPublicEventsWithUserStatus,
  fetchUserRegisteredEventsForPublic,
  checkUserRegistrationStatus,
  updateUserEventRegistration,
  cancelUserEventRegistration,
  selectPublicEventsWithUserStatus,
  selectPublicEventsWithUserStatusLoading,
  selectPublicEventsWithUserStatusError,
  selectUserRegisteredEventsForPublic,
  selectUserRegistrationStatusMap,
  selectUserRegistrationStatusLoading,
  selectRegisteredPublicEvents,
  selectUnregisteredPublicEvents,
  selectUpcomingRegisteredEvents,
  selectPendingPaymentEvents,
  selectIsUserRegisteredForEvent,
  selectUserRegistrationForEvent,
  selectUpdateUserRegistrationLoading,
  selectUpdateUserRegistrationSuccess,
  selectCancelUserRegistrationLoading,
  selectCancelUserRegistrationSuccess
} from '../store/slices/EventsSlice';

/**
 * Custom hook for managing user event status and registration
 * Provides easy access to user's event registration status and actions
 */
export const useUserEventStatus = () => {
  const dispatch = useDispatch();

  // Basic selectors
  const events = useSelector(selectPublicEventsWithUserStatus);
  const loading = useSelector(selectPublicEventsWithUserStatusLoading);
  const error = useSelector(selectPublicEventsWithUserStatusError);
  const userRegisteredEvents = useSelector(selectUserRegisteredEventsForPublic);
  const registrationStatusMap = useSelector(selectUserRegistrationStatusMap);
  const registrationStatusLoading = useSelector(selectUserRegistrationStatusLoading);

  // Filtered event selectors
  const registeredEvents = useSelector(selectRegisteredPublicEvents);
  const unregisteredEvents = useSelector(selectUnregisteredPublicEvents);
  const upcomingRegisteredEvents = useSelector(selectUpcomingRegisteredEvents);
  const pendingPaymentEvents = useSelector(selectPendingPaymentEvents);

  // Action loading states
  const updateRegistrationLoading = useSelector(selectUpdateUserRegistrationLoading);
  const updateRegistrationSuccess = useSelector(selectUpdateUserRegistrationSuccess);
  const cancelRegistrationLoading = useSelector(selectCancelUserRegistrationLoading);
  const cancelRegistrationSuccess = useSelector(selectCancelUserRegistrationSuccess);

  // Actions
  const loadEventsWithUserStatus = useCallback((queryParams = {}) => {
    return dispatch(fetchPublicEventsWithUserStatus(queryParams));
  }, [dispatch]);

  const loadUserRegisteredEvents = useCallback((options = {}) => {
    return dispatch(fetchUserRegisteredEventsForPublic(options));
  }, [dispatch]);

  const checkRegistrationStatus = useCallback((eventIds) => {
    return dispatch(checkUserRegistrationStatus(eventIds));
  }, [dispatch]);

  const updateRegistration = useCallback((eventId, registrationData) => {
    return dispatch(updateUserEventRegistration({ eventId, registrationData }));
  }, [dispatch]);

  const cancelRegistration = useCallback((eventId) => {
    return dispatch(cancelUserEventRegistration(eventId));
  }, [dispatch]);

  // Helper functions
  const isUserRegisteredForEvent = useCallback((eventId) => {
    return selectIsUserRegisteredForEvent(eventId);
  }, []);

  const getUserRegistrationForEvent = useCallback((eventId) => {
    return selectUserRegistrationForEvent(eventId);
  }, []);

  const getEventsByStatus = useCallback((status) => {
    return events.filter(event => event.userRegistrationStatus === status);
  }, [events]);

  const getEventsByPaymentStatus = useCallback((paymentStatus) => {
    return events.filter(event => event.userPaymentStatus === paymentStatus);
  }, [events]);

  const getEventsByCategory = useCallback((category) => {
    return events.filter(event => event.category === category);
  }, [events]);

  // Statistics
  const stats = useMemo(() => {
    const totalEvents = events.length;
    const totalRegistered = registeredEvents.length;
    const totalUpcoming = upcomingRegisteredEvents.length;
    const totalPendingPayment = pendingPaymentEvents.length;
    
    const confirmedRegistrations = events.filter(e => e.userRegistrationStatus === 'confirmed').length;
    const pendingRegistrations = events.filter(e => e.userRegistrationStatus === 'pending').length;
    const cancelledRegistrations = events.filter(e => e.userRegistrationStatus === 'cancelled').length;
    
    const completedPayments = events.filter(e => e.userPaymentStatus === 'completed').length;
    const failedPayments = events.filter(e => e.userPaymentStatus === 'failed').length;

    return {
      totalEvents,
      totalRegistered,
      totalUpcoming,
      totalPendingPayment,
      registrationStats: {
        confirmed: confirmedRegistrations,
        pending: pendingRegistrations,
        cancelled: cancelledRegistrations
      },
      paymentStats: {
        completed: completedPayments,
        pending: totalPendingPayment,
        failed: failedPayments
      },
      registrationRate: totalEvents > 0 ? (totalRegistered / totalEvents * 100).toFixed(1) : 0
    };
  }, [events, registeredEvents, upcomingRegisteredEvents, pendingPaymentEvents]);

  // Filter and search helpers
  const filterEvents = useCallback((filters = {}) => {
    let filteredEvents = [...events];

    if (filters.registered !== undefined) {
      filteredEvents = filters.registered ? registeredEvents : unregisteredEvents;
    }

    if (filters.status) {
      filteredEvents = filteredEvents.filter(event => event.userRegistrationStatus === filters.status);
    }

    if (filters.paymentStatus) {
      filteredEvents = filteredEvents.filter(event => event.userPaymentStatus === filters.paymentStatus);
    }

    if (filters.category) {
      filteredEvents = filteredEvents.filter(event => event.category === filters.category);
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredEvents = filteredEvents.filter(event =>
        event.title?.toLowerCase().includes(searchTerm) ||
        event.description?.toLowerCase().includes(searchTerm) ||
        event.category?.toLowerCase().includes(searchTerm) ||
        event.location?.toLowerCase().includes(searchTerm)
      );
    }

    if (filters.upcoming) {
      const now = new Date();
      filteredEvents = filteredEvents.filter(event =>
        new Date(event.start_datetime || event.start_date) > now
      );
    }

    return filteredEvents;
  }, [events, registeredEvents, unregisteredEvents]);

  // Get unique categories from events
  const categories = useMemo(() => {
    const categorySet = new Set();
    events.forEach(event => {
      if (event.category) {
        categorySet.add(event.category);
      }
    });
    return Array.from(categorySet).sort();
  }, [events]);

  // Get unique registration statuses
  const registrationStatuses = useMemo(() => {
    const statusSet = new Set();
    events.forEach(event => {
      if (event.userRegistrationStatus) {
        statusSet.add(event.userRegistrationStatus);
      }
    });
    return Array.from(statusSet).sort();
  }, [events]);

  // Get unique payment statuses
  const paymentStatuses = useMemo(() => {
    const statusSet = new Set();
    events.forEach(event => {
      if (event.userPaymentStatus) {
        statusSet.add(event.userPaymentStatus);
      }
    });
    return Array.from(statusSet).sort();
  }, [events]);

  return {
    // Data
    events,
    userRegisteredEvents,
    registeredEvents,
    unregisteredEvents,
    upcomingRegisteredEvents,
    pendingPaymentEvents,
    registrationStatusMap,
    stats,
    categories,
    registrationStatuses,
    paymentStatuses,

    // Loading states
    loading,
    registrationStatusLoading,
    updateRegistrationLoading,
    cancelRegistrationLoading,

    // Error states
    error,

    // Success states
    updateRegistrationSuccess,
    cancelRegistrationSuccess,

    // Actions
    loadEventsWithUserStatus,
    loadUserRegisteredEvents,
    checkRegistrationStatus,
    updateRegistration,
    cancelRegistration,

    // Helper functions
    isUserRegisteredForEvent,
    getUserRegistrationForEvent,
    getEventsByStatus,
    getEventsByPaymentStatus,
    getEventsByCategory,
    filterEvents
  };
};

export default useUserEventStatus;
