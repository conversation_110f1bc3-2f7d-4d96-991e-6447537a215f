/**
 * Payment Access Control Utilities
 * 
 * Manages payment access rules for different user types and event scenarios
 */

import { getCurrentUser, getUserRole } from '../helpers/authHelpers';

/**
 * User types that can make payments for events
 */
export const PAYMENT_ALLOWED_ROLES = [
  'student',
  'teacher', 
  'mentor',
  'sponsor'
];

/**
 * User types that cannot make payments (have special access)
 */
export const PAYMENT_RESTRICTED_ROLES = [
  'admin',     // Admin has access to all events without payment
  'institute'  // Institute cannot pay for their own events (but can pay for others)
];

/**
 * Check if a user can make payments for events in general
 * @param {string} userRole - User's role
 * @returns {boolean} - True if user can make payments
 */
export const canUserMakePayments = (userRole) => {
  if (!userRole) return false;
  return PAYMENT_ALLOWED_ROLES.includes(userRole.toLowerCase());
};

/**
 * Check if a user can purchase tickets for a specific event
 * @param {Object} event - Event object with organizer_id
 * @param {Object} user - Current user object
 * @returns {Object} - Access control result
 */
export const canPurchaseEventTickets = (event, user = null) => {
  // Get current user if not provided
  if (!user) {
    user = getCurrentUser();
  }

  if (!user || !event) {
    return {
      canPurchase: false,
      reason: 'missing_data',
      message: 'User or event information is missing'
    };
  }

  const userRole = user.user_type || user.role;
  const userId = user.id || user.user_id;

  // Admin cannot purchase tickets (has admin access)
  if (userRole?.toLowerCase() === 'admin') {
    return {
      canPurchase: false,
      reason: 'admin_access',
      message: 'Administrators have access to all events without payment'
    };
  }

  // Institute cannot purchase tickets for their own events
  if (userRole?.toLowerCase() === 'institute') {
    if (event.organizer_id === userId) {
      return {
        canPurchase: false,
        reason: 'event_organizer',
        message: 'You cannot purchase tickets for your own event'
      };
    }
    // Institute can purchase tickets for other institutes' events
    return {
      canPurchase: true,
      reason: 'allowed',
      message: 'You can purchase tickets for this event'
    };
  }

  // Check if user role is allowed to make payments
  if (!canUserMakePayments(userRole)) {
    return {
      canPurchase: false,
      reason: 'role_restricted',
      message: `${userRole} users cannot purchase event tickets`
    };
  }

  // All other allowed roles can purchase tickets
  return {
    canPurchase: true,
    reason: 'allowed',
    message: 'You can purchase tickets for this event'
  };
};

/**
 * Get payment access message for UI display
 * @param {Object} event - Event object
 * @param {Object} user - Current user object
 * @returns {Object} - UI message object
 */
export const getPaymentAccessMessage = (event, user = null) => {
  const access = canPurchaseEventTickets(event, user);
  
  const messageConfig = {
    allowed: {
      type: 'success',
      title: 'Payment Available',
      message: access.message,
      showPaymentButton: true
    },
    admin_access: {
      type: 'info',
      title: 'Admin Access',
      message: 'You have administrative access to this event',
      showPaymentButton: false
    },
    event_organizer: {
      type: 'info',
      title: 'Event Organizer',
      message: 'As the event organizer, you have full access without payment',
      showPaymentButton: false
    },
    role_restricted: {
      type: 'warning',
      title: 'Payment Not Available',
      message: access.message,
      showPaymentButton: false
    },
    missing_data: {
      type: 'error',
      title: 'Access Error',
      message: 'Unable to determine payment access. Please try again.',
      showPaymentButton: false
    }
  };

  return {
    ...messageConfig[access.reason],
    canPurchase: access.canPurchase,
    reason: access.reason
  };
};

/**
 * Check if user needs to register before payment
 * @param {Object} event - Event object
 * @param {Object} user - Current user object
 * @returns {boolean} - True if registration is required before payment
 */
export const requiresRegistrationBeforePayment = (event, user = null) => {
  // Some events might require approval before payment
  return event?.requires_approval === true;
};

/**
 * Get available payment methods for user
 * @param {Object} user - Current user object
 * @returns {Array} - Array of available payment methods
 */
export const getAvailablePaymentMethods = (user = null) => {
  if (!user) {
    user = getCurrentUser();
  }

  const userRole = user?.user_type || user?.role;

  // Base payment methods available to all paying users
  const baseMethods = [
    {
      id: 'payfast',
      name: 'PayFast',
      description: 'Secure online payment with credit/debit cards',
      icon: 'credit-card',
      enabled: true,
      fees: 'Standard processing fees apply'
    }
  ];

  // Role-specific payment methods
  const roleSpecificMethods = {
    sponsor: [
      {
        id: 'corporate_transfer',
        name: 'Corporate Transfer',
        description: 'Bank transfer for corporate sponsors',
        icon: 'building',
        enabled: true,
        fees: 'No additional fees'
      }
    ],
    institute: [
      {
        id: 'institutional_account',
        name: 'Institutional Account',
        description: 'Payment through institutional account',
        icon: 'university',
        enabled: true,
        fees: 'Institutional rates apply'
      }
    ]
  };

  // Combine base methods with role-specific methods
  const availableMethods = [
    ...baseMethods,
    ...(roleSpecificMethods[userRole?.toLowerCase()] || [])
  ];

  return availableMethods;
};

/**
 * Validate payment amount and user limits
 * @param {number} amount - Payment amount
 * @param {Object} user - Current user object
 * @returns {Object} - Validation result
 */
export const validatePaymentAmount = (amount, user = null) => {
  if (!user) {
    user = getCurrentUser();
  }

  const userRole = user?.user_type || user?.role;

  // Define payment limits by role
  const paymentLimits = {
    student: { min: 1, max: 5000 },
    teacher: { min: 1, max: 10000 },
    mentor: { min: 1, max: 10000 },
    sponsor: { min: 1, max: 100000 },
    institute: { min: 1, max: 50000 }
  };

  const limits = paymentLimits[userRole?.toLowerCase()] || { min: 1, max: 5000 };

  if (amount < limits.min) {
    return {
      valid: false,
      reason: 'amount_too_low',
      message: `Minimum payment amount is R${limits.min}`
    };
  }

  if (amount > limits.max) {
    return {
      valid: false,
      reason: 'amount_too_high',
      message: `Maximum payment amount for ${userRole} is R${limits.max}`
    };
  }

  return {
    valid: true,
    reason: 'valid',
    message: 'Payment amount is valid'
  };
};

/**
 * Get payment flow configuration for user role
 * @param {Object} user - Current user object
 * @returns {Object} - Payment flow configuration
 */
export const getPaymentFlowConfig = (user = null) => {
  if (!user) {
    user = getCurrentUser();
  }

  const userRole = user?.user_type || user?.role;

  const flowConfigs = {
    student: {
      requiresVerification: false,
      allowsInstallments: false,
      defaultCurrency: 'ZAR',
      autoApprove: true
    },
    teacher: {
      requiresVerification: false,
      allowsInstallments: true,
      defaultCurrency: 'ZAR',
      autoApprove: true
    },
    mentor: {
      requiresVerification: false,
      allowsInstallments: true,
      defaultCurrency: 'ZAR',
      autoApprove: true
    },
    sponsor: {
      requiresVerification: true,
      allowsInstallments: true,
      defaultCurrency: 'ZAR',
      autoApprove: false // May require manual approval for large amounts
    },
    institute: {
      requiresVerification: true,
      allowsInstallments: true,
      defaultCurrency: 'ZAR',
      autoApprove: false
    }
  };

  return flowConfigs[userRole?.toLowerCase()] || flowConfigs.student;
};

export default {
  canUserMakePayments,
  canPurchaseEventTickets,
  getPaymentAccessMessage,
  requiresRegistrationBeforePayment,
  getAvailablePaymentMethods,
  validatePaymentAmount,
  getPaymentFlowConfig,
  PAYMENT_ALLOWED_ROLES,
  PAYMENT_RESTRICTED_ROLES
};
