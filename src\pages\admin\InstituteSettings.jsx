import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchInstituteProfile,
  fetchProfileStatus,
  saveInstituteProfile,
  saveInstituteProfileWithDocuments,
  submitForApproval,
  selectProfile,
  selectProfileLoading,
  selectProfileError,
  selectProfileNotFound,
  selectSaveLoading,
  selectSaveError,
  selectSaveSuccess,
  selectSubmitLoading,
  selectSubmitError,
  selectSubmitSuccess,
  selectApprovalStatus,
  selectIsProfileComplete,
  selectRejectionReason,
  clearErrors,
  clearSuccessStates
} from '../../store/slices/InstituteProfileSlice';
import { LoadingSpinner } from '../../components/ui';

// Import profile components
import InstituteProfileHeader from '../../components/institute/profile/InstituteProfileHeader';
import InstituteBasicInfoForm from '../../components/institute/profile/InstituteBasicInfoForm';
import InstituteContactForm from '../../components/institute/profile/InstituteContactForm';
import InstituteSocialLinksForm from '../../components/institute/profile/InstituteSocialLinksForm';
import InstituteProfileActions from '../../components/institute/profile/InstituteProfileActions';
import InstituteDocumentSection from '../../components/institute/profile/InstituteDocumentSection';
import { getErrorMessage } from '../../utils/helpers/errorHandler';
import { hasDocumentChanges, getValidDocumentsForUpload } from '../../utils/documentHelpers';

const InstituteSettings = () => {
  const dispatch = useDispatch();

  // Redux state
  const profile = useSelector(selectProfile);
  const profileLoading = useSelector(selectProfileLoading);
  const profileError = useSelector(selectProfileError);
  const profileNotFound = useSelector(selectProfileNotFound);
  const saveLoading = useSelector(selectSaveLoading);
  const saveError = useSelector(selectSaveError);
  const saveSuccess = useSelector(selectSaveSuccess);
  const submitLoading = useSelector(selectSubmitLoading);
  const submitError = useSelector(selectSubmitError);
  const submitSuccess = useSelector(selectSubmitSuccess);
  const approvalStatus = useSelector(selectApprovalStatus);
  const isProfileComplete = useSelector(selectIsProfileComplete);
  const rejectionReason = useSelector(selectRejectionReason);

  // Local state
  const [formData, setFormData] = useState({
    institute_name: '',
    description: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    website: '',
    phone: '',
    institute_email: '',
    established_year: '',
    institute_type: '',
    accreditation: '',
    linkedin_url: '',
    facebook_url: '',
    twitter_url: '',
    logo_url: '',
    banner_url: ''
  });

  const [isEditing, setIsEditing] = useState(false);
  const [documents, setDocuments] = useState([]);
  const [originalDocuments, setOriginalDocuments] = useState([]);
  const [fieldErrors, setFieldErrors] = useState({});
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [originalFormData, setOriginalFormData] = useState({});

  // Mandatory fields for validation
  const mandatoryFields = {
    institute_name: 'Institute Name',
    institute_type: 'Institute Type',
    description: 'Description',
    address: 'Address',
    city: 'City',
    state: 'State',
    documents: 'Verification Documents'
  };

  // Load profile on component mount
  useEffect(() => {
    if (!profile && !profileError && !profileNotFound && !profileLoading) {
      dispatch(fetchInstituteProfile());
    }
  }, [dispatch, profile, profileError, profileNotFound, profileLoading]);

  // Auto-enable editing mode when profile is not found (create mode)
  useEffect(() => {
    if (profileNotFound) {
      setIsEditing(true);
    }
  }, [profileNotFound]);

  // Update form data when profile is loaded
  useEffect(() => {
    if (profile) {
      const profileData = {
        institute_name: profile.institute_name || '',
        description: profile.description || '',
        address: profile.address || '',
        city: profile.city || '',
        state: profile.state || '',
        postal_code: profile.postal_code || '',
        website: profile.website || '',
        phone: profile.phone || '',
        institute_email: profile.institute_email || '',
        established_year: profile.established_year || '',
        institute_type: profile.institute_type || '',
        accreditation: profile.accreditation || '',
        linkedin_url: profile.linkedin_url || '',
        facebook_url: profile.facebook_url || '',
        twitter_url: profile.twitter_url || '',
        logo_url: profile.logo_url || '',
        banner_url: profile.banner_url || ''
      };
      
      setFormData(profileData);
      setOriginalFormData(profileData);

      // Load existing documents if available
      const existingDocuments = profile.documents || [];
      setDocuments(existingDocuments);
      setOriginalDocuments(existingDocuments);
    }
  }, [profile]);

  // Handle profile not found case
  useEffect(() => {
    if (profileNotFound) {
      setIsEditing(true);
    }
  }, [profileNotFound]);

  // Clear success states after showing them
  useEffect(() => {
    if (saveSuccess || submitSuccess) {
      const timer = setTimeout(() => {
        dispatch(clearSuccessStates());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [saveSuccess, submitSuccess, dispatch]);

  // Helper functions
  const hasUnsavedChanges = () => {
    const formDataChanged = JSON.stringify(formData) !== JSON.stringify(originalFormData);
    const documentsChanged = hasDocumentChanges(documents, originalDocuments);
    return formDataChanged || documentsChanged;
  };

  const validateField = (name, value) => {
    if (mandatoryFields[name]) {
      if (name === 'documents') {
        if (!documents || documents.length === 0) {
          return `${mandatoryFields[name]} are required`;
        }
      } else {
        if (!value || (typeof value === 'string' && value.trim() === '')) {
          return `${mandatoryFields[name]} is required`;
        }
      }
    }
    return null;
  };

  const validateForm = () => {
    const errors = {};
    let isValid = true;

    Object.keys(mandatoryFields).forEach(field => {
      let value;
      if (field === 'documents') {
        value = documents;
      } else {
        value = formData[field];
      }

      const error = validateField(field, value);
      if (error) {
        errors[field] = error;
        isValid = false;
      }
    });

    setFieldErrors(errors);
    return isValid;
  };

  const isFormValidForSubmit = () => {
    return Object.keys(mandatoryFields).every(field => {
      if (field === 'documents') {
        return documents && documents.length > 0;
      } else {
        const value = formData[field];
        return value && (typeof value !== 'string' || value.trim() !== '');
      }
    });
  };

  // Event handlers
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const handleToggleEdit = () => {
    if (isEditing) {
      // Cancel editing - reset form data and documents
      setFormData(originalFormData);
      setDocuments(originalDocuments);
      setFieldErrors({});
      setHasAttemptedSubmit(false);
    }
    setIsEditing(!isEditing);
  };

  const handleCancelEdit = () => {
    setFormData(originalFormData);
    setDocuments(originalDocuments);
    setFieldErrors({});
    setHasAttemptedSubmit(false);
    setIsEditing(false);
  };

  const handleSave = async () => {
    setHasAttemptedSubmit(true);

    if (profileNotFound && !validateForm()) {
      return;
    }

    try {
      const newDocuments = getValidDocumentsForUpload(documents);

      // Use appropriate endpoint based on whether there are new documents
      if (newDocuments.length > 0) {
        await dispatch(saveInstituteProfileWithDocuments({
          profileData: formData,
          documents: newDocuments
        })).unwrap();
      } else {
        await dispatch(saveInstituteProfile(formData)).unwrap();
      }

      // Update original state after successful save
      setOriginalFormData(formData);
      setOriginalDocuments(documents);
      setHasAttemptedSubmit(false);
      setFieldErrors({});
    } catch (error) {
      console.error('Failed to save profile:', error);
    }
  };

  const handleSubmit = async () => {
    setHasAttemptedSubmit(true);

    if (!validateForm()) {
      return;
    }

    try {
      // Save first, then submit
      await handleSave();
      await dispatch(submitForApproval()).unwrap();
    } catch (error) {
      console.error('Failed to submit for approval:', error);
    }
  };

  // Loading state
  if (profileLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Error state - but only show error if it's not a "profile not found" case
  if (profileError && !profileNotFound) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <p>Error loading profile: {getErrorMessage(profileError)}</p>
        </div>
        <button
          onClick={() => dispatch(fetchInstituteProfile())}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Profile Header */}
      <InstituteProfileHeader
        profile={profile}
        profileNotFound={profileNotFound}
        approvalStatus={approvalStatus}
        isEditing={isEditing}
        onToggleEdit={handleToggleEdit}
        onCancelEdit={handleCancelEdit}
      />

      {/* Basic Information Form */}
      <InstituteBasicInfoForm
        formData={formData}
        onChange={handleInputChange}
        isEditing={isEditing}
        fieldErrors={fieldErrors}
        mandatoryFields={mandatoryFields}
        hasAttemptedSubmit={hasAttemptedSubmit}
      />

      {/* Contact Information Form */}
      <InstituteContactForm
        formData={formData}
        onChange={handleInputChange}
        isEditing={isEditing}
        fieldErrors={fieldErrors}
        mandatoryFields={mandatoryFields}
        hasAttemptedSubmit={hasAttemptedSubmit}
      />

      {/* Social Links Form */}
      <InstituteSocialLinksForm
        formData={formData}
        onChange={handleInputChange}
        isEditing={isEditing}
        fieldErrors={fieldErrors}
        hasAttemptedSubmit={hasAttemptedSubmit}
      />

      {/* Documents Section */}
      <InstituteDocumentSection
        documents={documents}
        onDocumentsChange={setDocuments}
        isEditing={isEditing}
        profileNotFound={profileNotFound}
      />

      {/* Profile Actions */}
      <InstituteProfileActions
        isEditing={isEditing}
        profileNotFound={profileNotFound}
        approvalStatus={approvalStatus}
        saveLoading={saveLoading}
        submitLoading={submitLoading}
        saveSuccess={saveSuccess}
        submitSuccess={submitSuccess}
        isFormValid={isFormValidForSubmit()}
        hasUnsavedChanges={hasUnsavedChanges()}
        onSave={handleSave}
        onSubmit={handleSubmit}
        onToggleEdit={handleToggleEdit}
        onCancelEdit={handleCancelEdit}
      />
    </div>
  );
};

export default InstituteSettings;
