import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

import URL from "../../utils/api/API_URL";

// NOTE: Subject endpoints don't exist in the actual API
// These are mock implementations until backend endpoints are available
const BASE_URL = `${URL}/api/subjects`;

// Get token from localStorage
const getAuthToken = () => localStorage.getItem("token");

// Mock data for development
const mockSubjects = {
  subjects: [
    { id: '1', name: 'Mathematics', description: 'Advanced Mathematics', created_at: new Date().toISOString() },
    { id: '2', name: 'Physics', description: 'General Physics', created_at: new Date().toISOString() },
    { id: '3', name: 'Chemistry', description: 'Organic Chemistry', created_at: new Date().toISOString() },
    { id: '4', name: 'Biology', description: 'Cell Biology', created_at: new Date().toISOString() },
    { id: '5', name: 'Computer Science', description: 'Programming Fundamentals', created_at: new Date().toISOString() }
  ],
  total: 5
};

// Thunks - MOCK IMPLEMENTATIONS

// Fetch all subjects with pagination - MOCK IMPLEMENTATION
export const fetchSubjects = createAsyncThunk(
  "subjects/fetchSubjects",
  async ({ skip = 0, limit = 100 } = {}, thunkAPI) => {
    try {
      // TODO: Replace with actual API call when endpoint is available
      console.warn('Subjects endpoint not available - using mock data');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Simulate pagination
      const startIndex = skip;
      const endIndex = Math.min(skip + limit, mockSubjects.subjects.length);
      const paginatedSubjects = mockSubjects.subjects.slice(startIndex, endIndex);

      return {
        subjects: paginatedSubjects,
        total: mockSubjects.total
      };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Create new subject - MOCK IMPLEMENTATION
export const createSubject = createAsyncThunk(
  "subjects/createSubject",
  async (subjectData, thunkAPI) => {
    try {
      // TODO: Replace with actual API call when endpoint is available
      console.warn('Create subject endpoint not available - using mock implementation');

      await new Promise(resolve => setTimeout(resolve, 500));

      const newSubject = {
        id: String(mockSubjects.subjects.length + 1),
        ...subjectData,
        created_at: new Date().toISOString()
      };

      // Add to mock data (for this session only)
      mockSubjects.subjects.push(newSubject);
      mockSubjects.total++;

      return newSubject;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchSubjectById = createAsyncThunk(
  "subjects/fetchSubjectById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update subject
export const updateSubject = createAsyncThunk(
  "subjects/updateSubject",
  async ({ id, subjectData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${id}`, subjectData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch subject hierarchy
export const fetchSubjectHierarchy = createAsyncThunk(
  "subjects/fetchSubjectHierarchy",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}/hierarchy`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete subject (handle new response shape)
export const deleteSubject = createAsyncThunk(
  "subjects/deleteSubject",
  async (id, thunkAPI) => {
    try {
      const res = await axios.delete(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return { id, detail: res.data.detail };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  subjects: [],
  total: 0,
  currentSubject: null,
  hierarchy: null,
  loading: false,
  error: null,
  deleteDetail: null,
};

// Slice
const subjectSlice = createSlice({
  name: "subjects",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch All
      .addCase(fetchSubjects.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjects.fulfilled, (state, action) => {
        state.loading = false;
        state.subjects = action.payload.subjects;
        state.total = action.payload.total;
      })
      .addCase(fetchSubjects.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create
      .addCase(createSubject.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSubject.fulfilled, (state, action) => {
        state.loading = false;
        state.subjects.push(action.payload);
      })
      .addCase(createSubject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch by ID
      .addCase(fetchSubjectById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjectById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSubject = action.payload;
      })
      .addCase(fetchSubjectById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update
      .addCase(updateSubject.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSubject.fulfilled, (state, action) => {
        state.loading = false;
        // Update the subject in the array
        const idx = state.subjects.findIndex(s => s.id === action.payload.id);
        if (idx !== -1) {
          state.subjects[idx] = action.payload;
        }
        // If currentSubject is the updated one, update it too
        if (state.currentSubject && state.currentSubject.id === action.payload.id) {
          state.currentSubject = action.payload;
        }
      })
      .addCase(updateSubject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Hierarchy
      .addCase(fetchSubjectHierarchy.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjectHierarchy.fulfilled, (state, action) => {
        state.loading = false;
        state.hierarchy = action.payload;
      })
      .addCase(fetchSubjectHierarchy.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete
      .addCase(deleteSubject.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.deleteDetail = null;
      })
      .addCase(deleteSubject.fulfilled, (state, action) => {
        state.loading = false;
        state.subjects = state.subjects.filter(
          (subj) => subj.id !== action.payload.id
        );
        state.deleteDetail = action.payload.detail;
      })
      .addCase(deleteSubject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default subjectSlice.reducer;
