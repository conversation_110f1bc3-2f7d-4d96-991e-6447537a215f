import React, { useState } from 'react';
import CountrySelector from './CountrySelector';

const PhoneInput = ({ 
  value = '', 
  onChange, 
  onCountryChange,
  countryCode = 'PK',
  placeholder = 'Enter your phone number',
  className = '',
  error = false,
  disabled = false,
  ...props 
}) => {
  const [selectedCountryCode, setSelectedCountryCode] = useState(countryCode);
  const [dialCode, setDialCode] = useState('+92');

  const handleCountryChange = (newCountryCode, newDialCode) => {
    setSelectedCountryCode(newCountryCode);
    setDialCode(newDialCode);
    onCountryChange?.(newCountryCode, newDialCode);
  };

  const handlePhoneChange = (e) => {
    const phoneNumber = e.target.value;
    onChange?.(phoneNumber);
  };

  return (
    <div className={`grid grid-cols-1 md:grid-cols-5 gap-3 ${className}`}>
      {/* Country Selector */}
      <div className="md:col-span-2">
        <CountrySelector
          value={selectedCountryCode}
          onChange={handleCountryChange}
          error={error}
          placeholder="Select country"
        />
      </div>

      {/* Phone Number Input */}
      <div className="md:col-span-3">
        <div className="relative">
          <input
            type="tel"
            value={value}
            onChange={handlePhoneChange}
            placeholder={placeholder}
            disabled={disabled}
            className={`
              w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 text-base min-h-[48px]
              bg-white dark:bg-gray-700 text-gray-900 dark:text-white
              placeholder-gray-500 dark:placeholder-gray-400
              focus:outline-none focus:ring-2 focus:ring-violet-500/50
              ${error 
                ? 'border-red-400 dark:border-red-500 focus:border-red-500' 
                : 'border-gray-300 dark:border-gray-600 focus:border-violet-500 hover:border-gray-400 dark:hover:border-gray-500'
              }
              ${disabled ? 'bg-gray-100 dark:bg-gray-800 cursor-not-allowed opacity-60' : ''}
            `}
            {...props}
          />
          
          {/* Dial Code Indicator */}
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 text-sm pointer-events-none">
            {dialCode}
          </div>
          
          {/* Adjust padding to account for dial code */}
          <style jsx>{`
            input {
              padding-left: ${dialCode.length * 8 + 24}px !important;
            }
          `}</style>
        </div>
      </div>
    </div>
  );
};

export default PhoneInput;
