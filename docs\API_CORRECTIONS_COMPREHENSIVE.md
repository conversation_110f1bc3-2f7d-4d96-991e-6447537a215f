# Comprehensive API Corrections

## Overview
This document identifies all frontend API calls that don't match the actual backend API endpoints based on the OpenAPI documentation at `localhost:8000/docs`.

## ✅ Existing API Endpoints (Available)

Based on the OpenAPI documentation, these endpoints are actually implemented:

### Health Endpoints
- `GET /api/health/health` - Basic health check
- `GET /api/health/health/detailed` - Detailed health check
- `GET /api/health/health/readiness` - Readiness probe
- `GET /api/health/health/liveness` - Liveness probe
- `GET /api/health/metrics` - Prometheus metrics

### User Management
- `POST /api/users/signin` - User sign in
- `POST /api/users/signup` - User sign up
- `GET /api/users/me` - Get current user
- `GET /api/users/` - Get all users
- `POST /api/users/` - Create user
- `GET /api/users/{user_id}` - Get user by ID
- `DELETE /api/users/{user_id}` - Delete user
- `GET /api/users/students/all` - Get all students
- `GET /api/users/teachers/all` - Get all teachers
- `GET /api/users/sponsors/all` - Get all sponsors
- `GET /api/users/institutes/all` - Get all institutes

### Authentication
- `POST /api/auth/send-verification` - Send verification email
- `POST /api/auth/verify-email` - Verify email
- `POST /api/auth/resend-verification` - Resend verification
- `GET /api/auth/verification-status` - Get verification status
- `POST /api/auth/forgot-password` - Forgot password
- `POST /api/auth/reset-password` - Reset password

### Classrooms
- `POST /api/classrooms/create` - Create classroom
- `GET /api/classrooms/all` - Get all own classes
- `GET /api/classrooms/my/students` - Get students in teacher's classes
- `GET /api/classrooms/{classroom_id}` - Get classroom by ID
- `PUT /api/classrooms/{classroom_id}` - Update classroom
- `DELETE /api/classrooms/{classroom_id}` - Delete classroom
- And many more classroom-related endpoints...

### Tasks
- `POST /api/tasks/for-student` - Create task for student
- `POST /api/tasks/for-classroom` - Create task for classroom
- `POST /api/tasks/for-multiple-students` - Create task for multiple students
- `POST /api/tasks/for-multiple-classrooms` - Create task for multiple classrooms
- `GET /api/tasks/minimal` - Get minimal task list
- `GET /api/tasks/{task_id}` - Get task by ID
- And many more task-related endpoints...

### Exams
- Multiple exam-related endpoints for creation, management, sessions, etc.

### Profiles
- Various profile management endpoints for different user types

### Subscriptions
- Subscription and plan management endpoints

## ❌ Non-Existent Endpoints (Frontend Issues)

These endpoints are called by the frontend but don't exist in the actual API:

### Student Dashboard
**Files affected:** `src/store/slices/StudentDashboardSlice.js`
```javascript
// These endpoints don't exist:
GET /api/student/dashboard
GET /api/student/dashboard/summary
GET /api/student/dashboard/quick-actions
GET /api/student/dashboard/performance
GET /api/student/dashboard/schedule
```

### Subjects Management
**Files affected:** `src/store/slices/SubjectSlice.js`
```javascript
// These endpoints don't exist:
GET /api/subjects/
POST /api/subjects/
GET /api/subjects/{id}
PUT /api/subjects/{id}
DELETE /api/subjects/{id}
```

### Chapters Management
**Files affected:** `src/store/slices/ChapterSlice.js`
```javascript
// These endpoints don't exist:
GET /api/chapters/
POST /api/chapters/
GET /api/chapters/{id}
PUT /api/chapters/{id}
DELETE /api/chapters/{id}
GET /api/chapters/by-subject/{subject_id}
```

### Topics Management
**Files affected:** `src/store/slices/TopicSlice.js`
```javascript
// These endpoints don't exist:
GET /api/topics/
POST /api/topics/
GET /api/topics/{id}
PUT /api/topics/{id}
DELETE /api/topics/{id}
GET /api/topics/by-chapter/{chapter_id}
```

### Questions Management
**Files affected:** `src/store/slices/QuestionSlice.js`
```javascript
// These endpoints don't exist:
GET /api/questions/
POST /api/questions/
GET /api/questions/{id}
PUT /api/questions/{id}
DELETE /api/questions/{id}
POST /api/questions/ai-generate
```

### Home Tutoring
**Files affected:** `src/store/slices/HomeTutoringSlice.js`
```javascript
// These endpoints don't exist:
POST /api/subscriptions/home-tutors/search
GET /api/subscriptions/home-tutors
POST /api/subscriptions/home-tutors/book
GET /api/subscriptions/home-tutors/{id}
```

### Events (Already Corrected)
**Files affected:** `src/store/slices/EventsSlice.js`, `src/services/eventService.js`
- Events endpoints don't exist but have been updated to match documentation

## 🔧 Recommended Corrections

### Immediate Actions Required:

1. **Disable Non-Existent Endpoints**
   - Add error handling for missing endpoints
   - Show "Feature Coming Soon" messages
   - Prevent API calls to non-existent endpoints

2. **Update API Base URLs**
   - Use existing endpoints where possible
   - Map functionality to available endpoints

3. **Create Fallback Components**
   - Show placeholder content for missing features
   - Provide user feedback about unavailable features

### Files Requiring Updates:

1. `src/store/slices/StudentDashboardSlice.js` - Disable or mock
2. `src/store/slices/SubjectSlice.js` - Disable or mock
3. `src/store/slices/ChapterSlice.js` - Disable or mock
4. `src/store/slices/TopicSlice.js` - Disable or mock
5. `src/store/slices/QuestionSlice.js` - Disable or mock
6. `src/store/slices/HomeTutoringSlice.js` - Disable or mock

## ✅ Corrections Applied

The following files have been updated with mock implementations:

### 1. StudentDashboardSlice.js ✅
- **Status:** Updated with mock data
- **Changes:** All dashboard endpoints now use mock implementations
- **Mock Data:** Summary stats, recent activity, performance metrics, schedule
- **Warning:** Console warnings added to indicate mock usage

### 2. SubjectSlice.js ✅
- **Status:** Updated with mock data
- **Changes:** CRUD operations now use mock implementations
- **Mock Data:** 5 sample subjects (Math, Physics, Chemistry, Biology, CS)
- **Features:** Pagination simulation, in-memory CRUD operations

### 3. QuestionSlice.js ✅
- **Status:** Updated with mock data
- **Changes:** Question management now uses mock implementations
- **Mock Data:** Sample multiple choice and essay questions
- **Features:** Different question types and difficulty levels

### 4. HomeTutoringSlice.js ✅
- **Status:** Updated with mock data
- **Changes:** Tutor search and booking now use mock implementations
- **Mock Data:** Sample tutors with ratings, availability, subjects
- **Features:** Search filtering by subject and location

### 5. EventsSlice.js ✅
- **Status:** Previously updated to match API documentation
- **Changes:** Endpoints corrected, field names updated
- **Note:** Events endpoints still don't exist in actual API

## 🚀 Next Steps

1. **Phase 1:** ✅ **COMPLETED** - Disabled non-existent API calls with mock data
2. **Phase 2:** Implement fallback UI components with "Coming Soon" indicators
3. **Phase 3:** Map existing functionality to available endpoints where possible
4. **Phase 4:** Request backend implementation for missing endpoints

## 📝 Implementation Priority

**High Priority:** ✅ **COMPLETED**
- Student Dashboard (core functionality) - Mock implementation active
- Subjects/Chapters/Topics (educational content) - Mock implementation active
- Questions (exam system dependency) - Mock implementation active

**Medium Priority:** ✅ **COMPLETED**
- Home Tutoring (additional feature) - Mock implementation active
- Events (already documented) - Endpoints corrected

**Low Priority:**
- Institute-specific endpoints (specialized features) - Still needs attention

## 🔧 Developer Notes

### Console Warnings
All mock implementations include console warnings to help developers identify when mock data is being used instead of real API calls.

### Mock Data Persistence
Mock data changes (create, update, delete) persist only for the current browser session. Page refresh will reset to default mock data.

### Testing
All mock implementations include realistic delays (300-500ms) to simulate network requests and provide realistic user experience during development.

### Future Migration
When actual API endpoints become available:
1. Remove console.warn statements
2. Replace mock implementations with actual API calls
3. Update mock data structure to match real API responses
4. Test thoroughly with real backend integration
