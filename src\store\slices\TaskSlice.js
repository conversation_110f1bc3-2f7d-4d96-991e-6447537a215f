import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import API_BASE_URL from '../../utils/api/API_URL';

// Async thunks for task operations
export const fetchTasks = createAsyncThunk(
  'tasks/fetchTasks',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/`, { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tasks');
    }
  }
);

export const fetchTasksByStudent = createAsyncThunk(
  'tasks/fetchTasksByStudent',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/student/`, { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch student tasks');
    }
  }
);

export const fetchAllTasksWithFilters = createAsyncThunk(
  'tasks/fetchAllTasksWithFilters',
  async (filters = {}, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/all/`, { params: filters });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tasks with filters');
    }
  }
);

export const fetchTasksMinimal = createAsyncThunk(
  'tasks/fetchTasksMinimal',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/minimal/`, { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch minimal tasks');
    }
  }
);

export const fetchTaskById = createAsyncThunk(
  'tasks/fetchTaskById',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch task');
    }
  }
);

export const fetchTaskForEdit = createAsyncThunk(
  'tasks/fetchTaskForEdit',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/edit/`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch task for edit');
    }
  }
);

export const fetchStudentTaskById = createAsyncThunk(
  'tasks/fetchStudentTaskById',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/student/`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch student task');
    }
  }
);

export const fetchTaskForTeacher = createAsyncThunk(
  'tasks/fetchTaskForTeacher',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/teacher/`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch task for teacher');
    }
  }
);

export const createTask = createAsyncThunk(
  'tasks/createTask',
  async (taskData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/`, taskData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task');
    }
  }
);

export const createTaskForStudent = createAsyncThunk(
  'tasks/createTaskForStudent',
  async (taskData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/student/`, taskData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task for student');
    }
  }
);

export const createTaskForClassroom = createAsyncThunk(
  'tasks/createTaskForClassroom',
  async (taskData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/classroom/`, taskData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task for classroom');
    }
  }
);

export const createTaskForMultipleStudents = createAsyncThunk(
  'tasks/createTaskForMultipleStudents',
  async (taskData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/multiple-students/`, taskData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task for multiple students');
    }
  }
);

export const updateTask = createAsyncThunk(
  'tasks/updateTask',
  async ({ taskId, taskData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/api/tasks/${taskId}/`, taskData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update task');
    }
  }
);

export const updateTaskForTeacher = createAsyncThunk(
  'tasks/updateTaskForTeacher',
  async ({ taskId, taskData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/api/tasks/${taskId}/teacher/`, taskData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update task for teacher');
    }
  }
);

export const deleteTask = createAsyncThunk(
  'tasks/deleteTask',
  async (taskId, { rejectWithValue }) => {
    try {
      await axios.delete(`${API_BASE_URL}/api/tasks/${taskId}/`);
      return taskId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete task');
    }
  }
);

// Task submission operations
export const submitTask = createAsyncThunk(
  'tasks/submitTask',
  async ({ taskId, submissionData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/submit/`, submissionData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to submit task');
    }
  }
);

export const fetchStudentTaskSubmission = createAsyncThunk(
  'tasks/fetchStudentTaskSubmission',
  async ({ taskId, studentId }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/submissions/${studentId}/`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch submission');
    }
  }
);

export const fetchTaskSubmissions = createAsyncThunk(
  'tasks/fetchTaskSubmissions',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/submissions/`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch submissions');
    }
  }
);

export const updateTaskStatus = createAsyncThunk(
  'tasks/updateTaskStatus',
  async ({ taskId, status }, { rejectWithValue }) => {
    try {
      const response = await axios.patch(`${API_BASE_URL}/api/tasks/${taskId}/status/`, { status });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update task status');
    }
  }
);

// Grading operations
export const gradeTaskSubmission = createAsyncThunk(
  'tasks/gradeTaskSubmission',
  async ({ submissionId, gradeData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/submissions/${submissionId}/grade/`, gradeData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to grade submission');
    }
  }
);

// Attachment operations
export const uploadTaskAttachment = createAsyncThunk(
  'tasks/uploadTaskAttachment',
  async ({ taskId, file, attachmentType = 'task' }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('attachment_type', attachmentType);
      
      const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/attachments/`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to upload attachment');
    }
  }
);

export const uploadTeacherTaskAttachment = createAsyncThunk(
  'tasks/uploadTeacherTaskAttachment',
  async ({ taskId, file }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/teacher-attachments/`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to upload teacher attachment');
    }
  }
);

export const fetchTaskAttachments = createAsyncThunk(
  'tasks/fetchTaskAttachments',
  async ({ taskId, attachmentType }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/attachments/`, {
        params: { attachment_type: attachmentType }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch attachments');
    }
  }
);

export const deleteTaskAttachment = createAsyncThunk(
  'tasks/deleteTaskAttachment',
  async (attachmentId, { rejectWithValue }) => {
    try {
      await axios.delete(`${API_BASE_URL}/api/tasks/attachments/${attachmentId}/`);
      return attachmentId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete attachment');
    }
  }
);

const initialState = {
  tasks: [],
  currentTask: null,
  submissions: [],
  currentSubmission: null,
  attachments: [],
  tasksLoading: false,
  tasksError: null,
  submissionLoading: false,
  submissionError: null,
  submissionStats: {
    total: 0,
    submitted: 0,
    pending: 0,
    graded: 0,
    averageScore: 0
  },
  gradingState: {
    loading: false,
    error: null,
    success: false
  },
  attachmentLoading: false,
  attachmentError: null,
  createLoading: false,
  createError: null,
  updateLoading: false,
  updateError: null
};

const taskSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    clearTaskState: (state) => {
      state.tasksError = null;
      state.createError = null;
      state.updateError = null;
    },
    clearSubmissionState: (state) => {
      state.submissionError = null;
      state.currentSubmission = null;
    },
    clearGradingState: (state) => {
      state.gradingState = {
        loading: false,
        error: null,
        success: false
      };
    },
    clearAttachmentState: (state) => {
      state.attachmentError = null;
    },
    setCurrentTask: (state, action) => {
      state.currentTask = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch tasks
      .addCase(fetchTasks.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTasks.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.tasks = action.payload;
      })
      .addCase(fetchTasks.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch tasks by student
      .addCase(fetchTasksByStudent.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTasksByStudent.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.tasks = action.payload;
      })
      .addCase(fetchTasksByStudent.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch all tasks with filters
      .addCase(fetchAllTasksWithFilters.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchAllTasksWithFilters.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.tasks = action.payload;
      })
      .addCase(fetchAllTasksWithFilters.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch tasks minimal
      .addCase(fetchTasksMinimal.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTasksMinimal.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.tasks = action.payload;
      })
      .addCase(fetchTasksMinimal.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch task by ID
      .addCase(fetchTaskById.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTaskById.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.currentTask = action.payload;
      })
      .addCase(fetchTaskById.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch task for edit
      .addCase(fetchTaskForEdit.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTaskForEdit.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.currentTask = action.payload;
      })
      .addCase(fetchTaskForEdit.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch student task by ID
      .addCase(fetchStudentTaskById.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchStudentTaskById.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.currentTask = action.payload;
      })
      .addCase(fetchStudentTaskById.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch task for teacher
      .addCase(fetchTaskForTeacher.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTaskForTeacher.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.currentTask = action.payload;
      })
      .addCase(fetchTaskForTeacher.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Create task
      .addCase(createTask.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTask.fulfilled, (state, action) => {
        state.createLoading = false;
        state.tasks.push(action.payload);
      })
      .addCase(createTask.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })
      
      // Create task for student
      .addCase(createTaskForStudent.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTaskForStudent.fulfilled, (state, action) => {
        state.createLoading = false;
        state.tasks.push(action.payload);
      })
      .addCase(createTaskForStudent.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Create task for classroom
      .addCase(createTaskForClassroom.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTaskForClassroom.fulfilled, (state, action) => {
        state.createLoading = false;
        if (Array.isArray(action.payload)) {
          state.tasks.push(...action.payload);
        } else {
          state.tasks.push(action.payload);
        }
      })
      .addCase(createTaskForClassroom.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Create task for multiple students
      .addCase(createTaskForMultipleStudents.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTaskForMultipleStudents.fulfilled, (state, action) => {
        state.createLoading = false;
        if (Array.isArray(action.payload)) {
          state.tasks.push(...action.payload);
        } else {
          state.tasks.push(action.payload);
        }
      })
      .addCase(createTaskForMultipleStudents.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Update task
      .addCase(updateTask.pending, (state) => {
        state.updateLoading = true;
        state.updateError = null;
      })
      .addCase(updateTask.fulfilled, (state, action) => {
        state.updateLoading = false;
        const index = state.tasks.findIndex(task => task.id === action.payload.id);
        if (index !== -1) {
          state.tasks[index] = action.payload;
        }
        if (state.currentTask && state.currentTask.id === action.payload.id) {
          state.currentTask = action.payload;
        }
      })
      .addCase(updateTask.rejected, (state, action) => {
        state.updateLoading = false;
        state.updateError = action.payload;
      })

      // Update task for teacher
      .addCase(updateTaskForTeacher.pending, (state) => {
        state.updateLoading = true;
        state.updateError = null;
      })
      .addCase(updateTaskForTeacher.fulfilled, (state, action) => {
        state.updateLoading = false;
        const index = state.tasks.findIndex(task => task.id === action.payload.id);
        if (index !== -1) {
          state.tasks[index] = action.payload;
        }
        if (state.currentTask && state.currentTask.id === action.payload.id) {
          state.currentTask = action.payload;
        }
      })
      .addCase(updateTaskForTeacher.rejected, (state, action) => {
        state.updateLoading = false;
        state.updateError = action.payload;
      })

      // Submit task
      .addCase(submitTask.pending, (state) => {
        state.submissionLoading = true;
        state.submissionError = null;
      })
      .addCase(submitTask.fulfilled, (state, action) => {
        state.submissionLoading = false;
        state.currentSubmission = action.payload;
      })
      .addCase(submitTask.rejected, (state, action) => {
        state.submissionLoading = false;
        state.submissionError = action.payload;
      })
      
      // Fetch submissions
      .addCase(fetchTaskSubmissions.pending, (state) => {
        state.submissionLoading = true;
        state.submissionError = null;
      })
      .addCase(fetchTaskSubmissions.fulfilled, (state, action) => {
        state.submissionLoading = false;
        state.submissions = action.payload;
      })
      .addCase(fetchTaskSubmissions.rejected, (state, action) => {
        state.submissionLoading = false;
        state.submissionError = action.payload;
      })
      
      // Grade submission
      .addCase(gradeTaskSubmission.pending, (state) => {
        state.gradingState.loading = true;
        state.gradingState.error = null;
        state.gradingState.success = false;
      })
      .addCase(gradeTaskSubmission.fulfilled, (state, action) => {
        state.gradingState.loading = false;
        state.gradingState.success = true;
        // Update the submission in the submissions array
        const index = state.submissions.findIndex(sub => sub.id === action.payload.id);
        if (index !== -1) {
          state.submissions[index] = action.payload;
        }
      })
      .addCase(gradeTaskSubmission.rejected, (state, action) => {
        state.gradingState.loading = false;
        state.gradingState.error = action.payload;
      })
      
      // Upload attachment
      .addCase(uploadTaskAttachment.pending, (state) => {
        state.attachmentLoading = true;
        state.attachmentError = null;
      })
      .addCase(uploadTaskAttachment.fulfilled, (state, action) => {
        state.attachmentLoading = false;
        state.attachments.push(action.payload);
      })
      .addCase(uploadTaskAttachment.rejected, (state, action) => {
        state.attachmentLoading = false;
        state.attachmentError = action.payload;
      })
      
      // Fetch attachments
      .addCase(fetchTaskAttachments.fulfilled, (state, action) => {
        state.attachments = action.payload;
      });
  }
});

// Export actions
export const {
  clearTaskState,
  clearSubmissionState,
  clearGradingState,
  clearAttachmentState,
  setCurrentTask
} = taskSlice.actions;

// Export selectors
export const selectTasks = (state) => state.tasks.tasks;
export const selectCurrentTask = (state) => state.tasks.currentTask;
export const selectTasksLoading = (state) => state.tasks.tasksLoading;
export const selectTasksError = (state) => state.tasks.tasksError;
export const selectSubmissions = (state) => state.tasks.submissions;
export const selectCurrentSubmission = (state) => state.tasks.currentSubmission;
export const selectSubmissionLoading = (state) => state.tasks.submissionLoading;
export const selectSubmissionError = (state) => state.tasks.submissionError;
export const selectSubmissionStats = (state) => {
  const submissions = state.tasks.submissions;
  if (!submissions || submissions.length === 0) {
    return {
      total: 0,
      submitted: 0,
      pending: 0,
      graded: 0,
      averageScore: 0
    };
  }

  const total = submissions.length;
  const submitted = submissions.filter(sub => sub.status === 'submitted').length;
  const pending = submissions.filter(sub => sub.status === 'pending').length;
  const graded = submissions.filter(sub => sub.grade !== null).length;
  const averageScore = graded > 0
    ? submissions
        .filter(sub => sub.grade !== null)
        .reduce((sum, sub) => sum + sub.grade, 0) / graded
    : 0;

  return {
    total,
    submitted,
    pending,
    graded,
    averageScore: Math.round(averageScore * 100) / 100
  };
};
export const selectGradingState = (state) => state.tasks.gradingState;
export const selectAttachments = (state) => state.tasks.attachments;
export const selectAttachmentLoading = (state) => state.tasks.attachmentLoading;
export const selectAttachmentError = (state) => state.tasks.attachmentError;
export const selectCreateLoading = (state) => state.tasks.createLoading;
export const selectCreateError = (state) => state.tasks.createError;

export default taskSlice.reducer;
