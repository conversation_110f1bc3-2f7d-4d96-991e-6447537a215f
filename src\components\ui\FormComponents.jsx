import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  FiEyeOff,
  FiCheck,
  FiX,
  FiAlertCircle,
  FiInfo,
  FiSearch,
  FiCalendar,
  FiDollarSign
} from 'react-icons/fi';

// Import the new custom datetime picker
import { CustomDateTimePicker as DateTimeInput } from './CustomDateTimePicker';

// Base form field wrapper
const FormField = ({ 
  label, 
  error, 
  hint, 
  required, 
  children, 
  className = '',
  labelClassName = '',
  errorClassName = '',
  hintClassName = ''
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className={`block text-sm font-semibold text-gray-800 dark:text-gray-200 ${labelClassName}`}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {children}

      {hint && !error && (
        <p className={`text-xs text-gray-600 dark:text-gray-400 flex items-center mt-1 ${hintClassName}`}>
          <FiInfo className="w-3 h-3 mr-1 flex-shrink-0" />
          {hint}
        </p>
      )}

      {error && (
        <p className={`text-xs text-red-600 dark:text-red-400 flex items-center mt-1 ${errorClassName}`}>
          <FiAlertCircle className="w-3 h-3 mr-1 flex-shrink-0" />
          {error}
        </p>
      )}
    </div>
  );
};

// Enhanced text input
export const TextInput = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  disabled,
  icon: IconComponent,
  rightIcon: RightIconComponent,
  onRightIconClick,
  autoComplete,
  maxLength,
  showCharCount = false,
  className = '',
  inputClassName = '',
  ...props
}) => {
  const hasError = !!error;
  const hasIcon = !!IconComponent;
  const hasRightIcon = !!RightIconComponent;

  const baseClasses = `
    w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 text-base min-h-[48px]
    bg-white dark:bg-gray-700 text-gray-900 dark:text-white
    placeholder-gray-500 dark:placeholder-gray-400
    focus:outline-none focus:ring-2 focus:ring-violet-500/50
    ${hasIcon ? 'pl-12' : 'pl-4'}
    ${hasRightIcon ? 'pr-12' : 'pr-4'}
    ${hasError
      ? 'border-red-400 dark:border-red-500 focus:border-red-500 focus:ring-red-500/50'
      : 'border-gray-300 dark:border-gray-600 focus:border-violet-500 hover:border-gray-400 dark:hover:border-gray-500'
    }
    ${disabled ? 'bg-gray-100 dark:bg-gray-800 cursor-not-allowed opacity-60' : ''}
  `;

  return (
    <div className={className}>
      <div className="relative">
        {hasIcon && (
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <IconComponent className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          </div>
        )}

        <input
          type={type}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          placeholder={placeholder}
          disabled={disabled}
          autoComplete={autoComplete}
          maxLength={maxLength}
          className={`${baseClasses} ${inputClassName}`}
          {...props}
        />

        {hasRightIcon && (
          <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
            <button
              type="button"
              onClick={onRightIconClick}
              className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200 focus:outline-none"
            >
              <RightIconComponent className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>
      
      {showCharCount && maxLength && (
        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 text-right">
          {value?.length || 0}/{maxLength}
        </div>
      )}
    </div>
  );
};

// Password input with visibility toggle
export const PasswordInput = ({ ...props }) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <TextInput
      type={showPassword ? 'text' : 'password'}
      rightIcon={showPassword ? FiEyeOff : FiEye}
      onRightIconClick={() => setShowPassword(!showPassword)}
      autoComplete="current-password"
      {...props}
    />
  );
};

// Enhanced textarea
export const TextArea = ({
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  disabled,
  rows = 3,
  maxLength,
  showCharCount = false,
  autoResize = false,
  className = '',
  ...props
}) => {
  const textareaRef = useRef(null);
  const hasError = !!error;

  // Auto-resize functionality
  useEffect(() => {
    if (autoResize && textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [value, autoResize]);

  const baseClasses = `
    form-textarea w-full transition-colors duration-200 resize-none
    ${hasError 
      ? 'border-red-300 dark:border-red-600 focus:border-red-500 focus:ring-red-500' 
      : 'border-gray-300 dark:border-gray-600 focus:border-violet-500 focus:ring-violet-500'
    }
    ${disabled ? 'bg-gray-50 dark:bg-gray-800 cursor-not-allowed' : ''}
  `;

  return (
    <div className={className}>
      <textarea
        ref={textareaRef}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        placeholder={placeholder}
        disabled={disabled}
        rows={autoResize ? 1 : rows}
        maxLength={maxLength}
        className={baseClasses}
        {...props}
      />
      
      {showCharCount && maxLength && (
        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 text-right">
          {value?.length || 0}/{maxLength}
        </div>
      )}
    </div>
  );
};

// Enhanced select
export const Select = ({
  options = [],
  value,
  onChange,
  onBlur,
  error,
  disabled,
  placeholder = 'Select an option',
  className = '',
  ...props
}) => {
  const hasError = !!error;

  const baseClasses = `
    form-select w-full transition-colors duration-200
    ${hasError 
      ? 'border-red-300 dark:border-red-600 focus:border-red-500 focus:ring-red-500' 
      : 'border-gray-300 dark:border-gray-600 focus:border-violet-500 focus:ring-violet-500'
    }
    ${disabled ? 'bg-gray-50 dark:bg-gray-800 cursor-not-allowed' : ''}
  `;

  return (
    <select
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      disabled={disabled}
      className={`${baseClasses} ${className}`}
      {...props}
    >
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};

// Checkbox with enhanced styling
export const Checkbox = ({
  checked,
  onChange,
  label,
  description,
  error,
  disabled,
  className = '',
  ...props
}) => {
  return (
    <div className={`flex items-start ${className}`}>
      <div className="flex items-center h-5">
        <input
          type="checkbox"
          checked={checked}
          onChange={onChange}
          disabled={disabled}
          className={`
            form-checkbox h-4 w-4 rounded transition-colors duration-200
            ${error 
              ? 'text-red-600 border-red-300 focus:ring-red-500' 
              : 'text-violet-600 border-gray-300 focus:ring-violet-500'
            }
            ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
          `}
          {...props}
        />
      </div>
      
      {(label || description) && (
        <div className="ml-3">
          {label && (
            <label className={`text-sm font-medium text-gray-700 dark:text-gray-300 ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}>
              {label}
            </label>
          )}
          {description && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {description}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

// Radio group
export const RadioGroup = ({
  options = [],
  value,
  onChange,
  name,
  error,
  disabled,
  className = '',
  ...props
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      {options.map((option) => (
        <div key={option.value} className="flex items-start">
          <div className="flex items-center h-5">
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={() => onChange(option.value)}
              disabled={disabled}
              className={`
                form-radio h-4 w-4 transition-colors duration-200
                ${error 
                  ? 'text-red-600 border-red-300 focus:ring-red-500' 
                  : 'text-violet-600 border-gray-300 focus:ring-violet-500'
                }
                ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
              `}
              {...props}
            />
          </div>
          <div className="ml-3">
            <label className={`text-sm font-medium text-gray-700 dark:text-gray-300 ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}>
              {option.label}
            </label>
            {option.description && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {option.description}
              </p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

// Number input with increment/decrement buttons
export const NumberInput = ({
  value,
  onChange,
  min,
  max,
  step = 1,
  showControls = true,
  prefix,
  suffix,
  ...props
}) => {
  const increment = () => {
    const newValue = (parseFloat(value) || 0) + step;
    if (max === undefined || newValue <= max) {
      onChange({ target: { value: newValue.toString() } });
    }
  };

  const decrement = () => {
    const newValue = (parseFloat(value) || 0) - step;
    if (min === undefined || newValue >= min) {
      onChange({ target: { value: newValue.toString() } });
    }
  };

  return (
    <div className="relative">
      {prefix && (
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <span className="text-gray-500 text-sm">{prefix}</span>
        </div>
      )}
      
      <TextInput
        type="number"
        value={value}
        onChange={onChange}
        min={min}
        max={max}
        step={step}
        className={`${prefix ? 'pl-8' : ''} ${suffix || showControls ? 'pr-16' : ''}`}
        {...props}
      />
      
      {suffix && (
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <span className="text-gray-500 text-sm">{suffix}</span>
        </div>
      )}
      
      {showControls && (
        <div className="absolute inset-y-0 right-0 flex flex-col">
          <button
            type="button"
            onClick={increment}
            className="px-2 py-1 text-xs text-gray-500 hover:text-gray-700 border-l border-gray-300"
          >
            +
          </button>
          <button
            type="button"
            onClick={decrement}
            className="px-2 py-1 text-xs text-gray-500 hover:text-gray-700 border-l border-t border-gray-300"
          >
            -
          </button>
        </div>
      )}
    </div>
  );
};

export { FormField, DateTimeInput };
export default {
  FormField,
  TextInput,
  PasswordInput,
  TextArea,
  Select,
  Checkbox,
  RadioGroup,
  NumberInput,
  DateTimeInput
};
