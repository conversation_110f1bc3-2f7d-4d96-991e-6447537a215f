GET
/api/admin/events/{event_id}
Get Event Admin


Get detailed event information with admin-level access.

curl -X 'GET' \
  'http://127.0.0.1:8000/api/admin/events/{event_id}' \
  -H 'accept: application/json'



--------------------------------------------------------
GET
/api/admin/events/{event_id}/registrations
Get Event Registrations Admin


Get all registrations for a specific event with admin-level details.

Admin can view registrations for any event regardless of organizer.

curl -X 'GET' \
  'http://127.0.0.1:8000/api/admin/events/{event_id}/registrations?skip=0&limit=20' \
  -H 'accept: application/json'


Name	Description
event_id *
string($uuid)
(path)
{event_id}
skip
integer
(query)
0
minimum: 0
limit
integer
(query)
20
maximum: 100
minimum: 1
status_filter
string | (string | null)
(query)

--------------------------------------------
DELETE
/api/admin/events/{event_id}
Delete Event Admin


Delete an event and all associated data.

Admin can delete any event. Use force=true to delete events with registrations.

curl -X 'DELETE' \
  'http://127.0.0.1:8000/api/admin/events/{event_id}?force=true' \
  -H 'accept: application/json'