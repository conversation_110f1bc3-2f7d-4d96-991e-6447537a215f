import React from 'react';
import {
  <PERSON>Calendar,
  <PERSON>Dollar<PERSON>ign,
  <PERSON><PERSON>lock,
  FiEdit3,
  FiTrash2,
  FiExternalLink,
  FiUser,
  FiHome
} from 'react-icons/fi';

const CollaborationCard = ({ 
  collaboration, 
  onEdit, 
  onDelete, 
  onViewDetails,
  userRole = 'mentor' // 'mentor' or 'institute'
}) => {
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const partner = userRole === 'mentor' ? collaboration.institute : collaboration.mentor;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-violet-100 dark:bg-violet-900/30 rounded-lg">
            {userRole === 'mentor' ? (
              <FiHome className="w-5 h-5 text-violet-600 dark:text-violet-400" />
            ) : (
              <FiUser className="w-5 h-5 text-violet-600 dark:text-violet-400" />
            )}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {userRole === 'mentor' ? partner?.institute_name : partner?.full_name}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {partner?.email}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(collaboration.status)}`}>
            {collaboration.status || 'Unknown'}
          </span>
        </div>
      </div>

      {/* Collaboration Details */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="flex items-center space-x-2">
          <FiDollarSign className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-600 dark:text-gray-300">
            ${collaboration.hourly_rate || 0}/hour
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <FiClock className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-600 dark:text-gray-300">
            {collaboration.hours_per_week || 0} hrs/week
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <FiCalendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-600 dark:text-gray-300">
            Start: {formatDate(collaboration.start_date)}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <FiCalendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-600 dark:text-gray-300">
            End: {formatDate(collaboration.end_date)}
          </span>
        </div>
      </div>

      {/* Contract Terms */}
      {collaboration.contract_terms && (
        <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
            <span className="font-medium">Terms:</span> {collaboration.contract_terms}
          </p>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={() => onViewDetails?.(collaboration)}
          className="flex items-center space-x-2 text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 transition-colors"
        >
          <FiExternalLink className="w-4 h-4" />
          <span className="text-sm font-medium">View Details</span>
        </button>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onEdit?.(collaboration)}
            className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/30"
            title="Edit collaboration"
          >
            <FiEdit3 className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => onDelete?.(collaboration)}
            className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors rounded-lg hover:bg-red-50 dark:hover:bg-red-900/30"
            title="Delete collaboration"
          >
            <FiTrash2 className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default CollaborationCard;
