import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { fetchClassroomForStudent } from '../../store/slices/ClassroomSlice';
import { fetchAnnouncementsForStudent } from '../../store/slices/AnnouncementSlice';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  FiHome,
  FiUsers,
  FiBookOpen,
  FiFileText
} from 'react-icons/fi';

// Import new components
import ClassroomHeader from '../../features/classroom/ClassroomHeader';
import ClassroomTabs from '../../features/classroom/ClassroomTabs';
import StreamTab from '../../features/classroom/student/StreamTab';
import PeopleTab from '../../features/classroom/student/PeopleTab';
import GradesTab from '../../features/classroom/student/GradesTab';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import ErrorMessage from '../../components/ui/ErrorMessage';

function StudentClassroom({ classroomId: propClassroomId }) {
  console.log('🎯 StudentClassroom component rendered');

  const { classroomId: paramClassroomId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();

  // Use prop classroomId if available, otherwise use params
  const classroomId = propClassroomId || paramClassroomId;

  console.log('🔍 StudentClassroom - propClassroomId:', propClassroomId);
  console.log('🔍 StudentClassroom - paramClassroomId:', paramClassroomId);
  console.log('🔍 StudentClassroom - final classroomId:', classroomId);
  
  const { classroom, students, loading, error } = useSelector((state) => state.classroom);
  const { announcements, loading: announcementsLoading, error: announcementsError } = useSelector((state) => state.announcements);
  
  const [activeTab, setActiveTab] = useState('stream');

  // Theme classes
  const bgPrimary = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  useEffect(() => {
    console.log('🔍 StudentClassroom useEffect - classroomId:', classroomId);
    if (classroomId) {
      console.log('🚀 Dispatching fetchClassroomForStudent with classroomId:', classroomId);
      dispatch(fetchClassroomForStudent(classroomId));
      // Note: Students don't need to fetch students list separately - it should come with classroom data
      // dispatch(fetchStudentsInClassroom(classroomId)); // REMOVED - This was calling wrong API
      dispatch(fetchAnnouncementsForStudent({ classroom_id: classroomId, skip: 0, limit: 100 }));
    } else {
      console.log('❌ No classroomId available');
    }
  }, [dispatch, classroomId]);

  const tabs = [
    { id: 'stream', label: 'Stream', icon: FiHome },
    { id: 'classwork', label: 'Classwork', icon: FiBookOpen },
    { id: 'people', label: 'People', icon: FiUsers },
    { id: 'grades', label: 'Grades', icon: FiFileText },
  ];

  if (loading) {
    return (
      <LoadingSpinner
        size="lg"
        text="Loading classroom..."
        fullScreen={true}
        currentTheme={currentTheme}
      />
    );
  }

  if (error) {
    return (
      <ErrorMessage
        error={error}
        title="Classroom not found"
        description="The classroom you're looking for doesn't exist or you don't have access to it."
        onRetry={() => navigate('/student/classes')}
        retryText="Back to Classes"
        fullScreen={true}
        currentTheme={currentTheme}
      />
    );
  }

  return (
    <div className={`min-h-screen ${bgPrimary}`}>
      {/* Header */}
      <ClassroomHeader
        classroom={classroom}
        onBack={() => navigate('/student/classes')}
        currentTheme={currentTheme}
        isStudent={true}
      />

      {/* Navigation Tabs */}
      <ClassroomTabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        currentTheme={currentTheme}
      />

      {/* Main Content */}
      <div className="w-full max-w-none px-4 sm:px-6 lg:px-8 py-6">
        {activeTab === 'stream' && (
          <StreamTab
            classroom={classroom}
            students={students}
            announcements={announcements}
            announcementsLoading={announcementsLoading}
            announcementsError={announcementsError}
            currentTheme={currentTheme}
          />
        )}

        {activeTab === 'people' && (
          <PeopleTab
            classroom={classroom}
            students={students}
            currentTheme={currentTheme}
          />
        )}

        {activeTab === 'grades' && (
          <GradesTab
            currentTheme={currentTheme}
          />
        )}

        {activeTab === 'classwork' && (
          <div className="space-y-6">
            <div className={`${bgSecondary} rounded-lg p-12 border ${borderColor} text-center`}>
              <FiBookOpen className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
              <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>No classwork yet</h3>
              <p className={textSecondary}>Your teacher hasn't assigned any work.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default StudentClassroom;
